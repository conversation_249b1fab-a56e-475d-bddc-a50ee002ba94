{"version": 3, "sources": ["../../plyr-react/esm/index.js", "../../plyr/dist/plyr.min.mjs", "../../react-aptor/esm/index.js"], "sourcesContent": ["import { jsx } from 'react/jsx-runtime';\nimport * as React from 'react';\nimport PlyrJS from 'plyr';\nimport PropTypes from 'prop-types';\nimport useAptor from 'react-aptor';\n\nconst instantiate = (_, params) => {\n  const plyr = new PlyrJS(\".plyr-react\", params.options || {});\n  if (params.source)\n    plyr.source = params.source;\n  return plyr;\n};\nconst destroy = (plyr) => {\n  if (plyr)\n    plyr.destroy();\n};\nconst noop = () => {\n};\nconst getAPI = (plyr) => {\n  if (!plyr) {\n    return () => new Proxy({ plyr: { source: null } }, {\n      get: (target, prop) => {\n        if (prop === \"plyr\") {\n          return target[prop];\n        }\n        return noop;\n      }\n    });\n  }\n  return () => ({\n    /**\n     * Plyr instance with all of its functionality\n     */\n    plyr\n  });\n};\nfunction usePlyr(ref, params, deps = null) {\n  return useAptor(\n    ref,\n    {\n      instantiate,\n      getAPI,\n      destroy,\n      params\n    },\n    deps || [params.options, params.source]\n  );\n}\nconst Plyr = React.forwardRef((props, ref) => {\n  const { source, options = null, ...rest } = props;\n  const raptorRef = usePlyr(ref, {\n    source,\n    options\n  });\n  return /* @__PURE__ */ jsx(\"video\", { ref: raptorRef, className: \"plyr-react plyr\", ...rest });\n});\nif ((import.meta.env && import.meta.env.MODE) !== \"production\") {\n  Plyr.displayName = \"Plyr\";\n  Plyr.defaultProps = {\n    options: {\n      controls: [\n        \"rewind\",\n        \"play\",\n        \"fast-forward\",\n        \"progress\",\n        \"current-time\",\n        \"duration\",\n        \"mute\",\n        \"volume\",\n        \"settings\",\n        \"fullscreen\"\n      ],\n      i18n: {\n        restart: \"Restart\",\n        rewind: \"Rewind {seektime}s\",\n        play: \"Play\",\n        pause: \"Pause\",\n        fastForward: \"Forward {seektime}s\",\n        seek: \"Seek\",\n        seekLabel: \"{currentTime} of {duration}\",\n        played: \"Played\",\n        buffered: \"Buffered\",\n        currentTime: \"Current time\",\n        duration: \"Duration\",\n        volume: \"Volume\",\n        mute: \"Mute\",\n        unmute: \"Unmute\",\n        enableCaptions: \"Enable captions\",\n        disableCaptions: \"Disable captions\",\n        download: \"Download\",\n        enterFullscreen: \"Enter fullscreen\",\n        exitFullscreen: \"Exit fullscreen\",\n        frameTitle: \"Player for {title}\",\n        captions: \"Captions\",\n        settings: \"Settings\",\n        menuBack: \"Go back to previous menu\",\n        speed: \"Speed\",\n        normal: \"Normal\",\n        quality: \"Quality\",\n        loop: \"Loop\"\n      }\n    },\n    source: {\n      type: \"video\",\n      sources: [\n        {\n          src: \"https://cdn.plyr.io/static/blank.mp4\",\n          type: \"video/mp4\",\n          size: 720\n        },\n        {\n          src: \"https://cdn.plyr.io/static/blank.mp4\",\n          type: \"video/mp4\",\n          size: 1080\n        }\n      ]\n    }\n  };\n  Plyr.propTypes = {\n    options: PropTypes.object,\n    source: PropTypes.any\n  };\n}\n\nexport { Plyr as default, usePlyr };\n", "function _defineProperty$1(e,t,i){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function _toPrimitive(e,t){if(\"object\"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var s=i.call(e,t||\"default\");if(\"object\"!=typeof s)return s;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}function _toPropertyKey(e){var t=_toPrimitive(e,\"string\");return\"symbol\"==typeof t?t:String(t)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}function _defineProperties(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,\"value\"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function _createClass(e,t,i){return t&&_defineProperties(e.prototype,t),i&&_defineProperties(e,i),e}function _defineProperty(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function ownKeys(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,s)}return i}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(i),!0).forEach((function(t){_defineProperty(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var defaults$1={addCSS:!0,thumbWidth:15,watch:!0};function matches$1(e,t){return function(){return Array.from(document.querySelectorAll(t)).includes(this)}.call(e,t)}function trigger(e,t){if(e&&t){var i=new Event(t,{bubbles:!0});e.dispatchEvent(i)}}var getConstructor$1=function(e){return null!=e?e.constructor:null},instanceOf$1=function(e,t){return!!(e&&t&&e instanceof t)},isNullOrUndefined$1=function(e){return null==e},isObject$1=function(e){return getConstructor$1(e)===Object},isNumber$1=function(e){return getConstructor$1(e)===Number&&!Number.isNaN(e)},isString$1=function(e){return getConstructor$1(e)===String},isBoolean$1=function(e){return getConstructor$1(e)===Boolean},isFunction$1=function(e){return getConstructor$1(e)===Function},isArray$1=function(e){return Array.isArray(e)},isNodeList$1=function(e){return instanceOf$1(e,NodeList)},isElement$1=function(e){return instanceOf$1(e,Element)},isEvent$1=function(e){return instanceOf$1(e,Event)},isEmpty$1=function(e){return isNullOrUndefined$1(e)||(isString$1(e)||isArray$1(e)||isNodeList$1(e))&&!e.length||isObject$1(e)&&!Object.keys(e).length},is$1={nullOrUndefined:isNullOrUndefined$1,object:isObject$1,number:isNumber$1,string:isString$1,boolean:isBoolean$1,function:isFunction$1,array:isArray$1,nodeList:isNodeList$1,element:isElement$1,event:isEvent$1,empty:isEmpty$1};function getDecimalPlaces(e){var t=\"\".concat(e).match(/(?:\\.(\\d+))?(?:[eE]([+-]?\\d+))?$/);return t?Math.max(0,(t[1]?t[1].length:0)-(t[2]?+t[2]:0)):0}function round(e,t){if(1>t){var i=getDecimalPlaces(t);return parseFloat(e.toFixed(i))}return Math.round(e/t)*t}var RangeTouch=function(){function e(t,i){_classCallCheck(this,e),is$1.element(t)?this.element=t:is$1.string(t)&&(this.element=document.querySelector(t)),is$1.element(this.element)&&is$1.empty(this.element.rangeTouch)&&(this.config=_objectSpread2({},defaults$1,{},i),this.init())}return _createClass(e,[{key:\"init\",value:function(){e.enabled&&(this.config.addCSS&&(this.element.style.userSelect=\"none\",this.element.style.webKitUserSelect=\"none\",this.element.style.touchAction=\"manipulation\"),this.listeners(!0),this.element.rangeTouch=this)}},{key:\"destroy\",value:function(){e.enabled&&(this.config.addCSS&&(this.element.style.userSelect=\"\",this.element.style.webKitUserSelect=\"\",this.element.style.touchAction=\"\"),this.listeners(!1),this.element.rangeTouch=null)}},{key:\"listeners\",value:function(e){var t=this,i=e?\"addEventListener\":\"removeEventListener\";[\"touchstart\",\"touchmove\",\"touchend\"].forEach((function(e){t.element[i](e,(function(e){return t.set(e)}),!1)}))}},{key:\"get\",value:function(t){if(!e.enabled||!is$1.event(t))return null;var i,s=t.target,n=t.changedTouches[0],r=parseFloat(s.getAttribute(\"min\"))||0,a=parseFloat(s.getAttribute(\"max\"))||100,o=parseFloat(s.getAttribute(\"step\"))||1,l=s.getBoundingClientRect(),c=100/l.width*(this.config.thumbWidth/2)/100;return 0>(i=100/l.width*(n.clientX-l.left))?i=0:100<i&&(i=100),50>i?i-=(100-2*i)*c:50<i&&(i+=2*(i-50)*c),r+round(i/100*(a-r),o)}},{key:\"set\",value:function(t){e.enabled&&is$1.event(t)&&!t.target.disabled&&(t.preventDefault(),t.target.value=this.get(t),trigger(t.target,\"touchend\"===t.type?\"change\":\"input\"))}}],[{key:\"setup\",value:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},s=null;if(is$1.empty(t)||is$1.string(t)?s=Array.from(document.querySelectorAll(is$1.string(t)?t:'input[type=\"range\"]')):is$1.element(t)?s=[t]:is$1.nodeList(t)?s=Array.from(t):is$1.array(t)&&(s=t.filter(is$1.element)),is$1.empty(s))return null;var n=_objectSpread2({},defaults$1,{},i);if(is$1.string(t)&&n.watch){var r=new MutationObserver((function(i){Array.from(i).forEach((function(i){Array.from(i.addedNodes).forEach((function(i){is$1.element(i)&&matches$1(i,t)&&new e(i,n)}))}))}));r.observe(document.body,{childList:!0,subtree:!0})}return s.map((function(t){return new e(t,i)}))}},{key:\"enabled\",get:function(){return\"ontouchstart\"in document.documentElement}}]),e}();const getConstructor=e=>null!=e?e.constructor:null,instanceOf=(e,t)=>Boolean(e&&t&&e instanceof t),isNullOrUndefined=e=>null==e,isObject=e=>getConstructor(e)===Object,isNumber=e=>getConstructor(e)===Number&&!Number.isNaN(e),isString=e=>getConstructor(e)===String,isBoolean=e=>getConstructor(e)===Boolean,isFunction=e=>\"function\"==typeof e,isArray=e=>Array.isArray(e),isWeakMap=e=>instanceOf(e,WeakMap),isNodeList=e=>instanceOf(e,NodeList),isTextNode=e=>getConstructor(e)===Text,isEvent=e=>instanceOf(e,Event),isKeyboardEvent=e=>instanceOf(e,KeyboardEvent),isCue=e=>instanceOf(e,window.TextTrackCue)||instanceOf(e,window.VTTCue),isTrack=e=>instanceOf(e,TextTrack)||!isNullOrUndefined(e)&&isString(e.kind),isPromise=e=>instanceOf(e,Promise)&&isFunction(e.then),isElement=e=>null!==e&&\"object\"==typeof e&&1===e.nodeType&&\"object\"==typeof e.style&&\"object\"==typeof e.ownerDocument,isEmpty=e=>isNullOrUndefined(e)||(isString(e)||isArray(e)||isNodeList(e))&&!e.length||isObject(e)&&!Object.keys(e).length,isUrl=e=>{if(instanceOf(e,window.URL))return!0;if(!isString(e))return!1;let t=e;e.startsWith(\"http://\")&&e.startsWith(\"https://\")||(t=`http://${e}`);try{return!isEmpty(new URL(t).hostname)}catch(e){return!1}};var is={nullOrUndefined:isNullOrUndefined,object:isObject,number:isNumber,string:isString,boolean:isBoolean,function:isFunction,array:isArray,weakMap:isWeakMap,nodeList:isNodeList,element:isElement,textNode:isTextNode,event:isEvent,keyboardEvent:isKeyboardEvent,cue:isCue,track:isTrack,promise:isPromise,url:isUrl,empty:isEmpty};const transitionEndEvent=(()=>{const e=document.createElement(\"span\"),t={WebkitTransition:\"webkitTransitionEnd\",MozTransition:\"transitionend\",OTransition:\"oTransitionEnd otransitionend\",transition:\"transitionend\"},i=Object.keys(t).find((t=>void 0!==e.style[t]));return!!is.string(i)&&t[i]})();function repaint(e,t){setTimeout((()=>{try{e.hidden=!0,e.offsetHeight,e.hidden=!1}catch(e){}}),t)}const isIE=Boolean(window.document.documentMode),isEdge=/Edge/g.test(navigator.userAgent),isWebKit=\"WebkitAppearance\"in document.documentElement.style&&!/Edge/g.test(navigator.userAgent),isIPhone=/iPhone|iPod/gi.test(navigator.userAgent)&&navigator.maxTouchPoints>1,isIPadOS=\"MacIntel\"===navigator.platform&&navigator.maxTouchPoints>1,isIos=/iPad|iPhone|iPod/gi.test(navigator.userAgent)&&navigator.maxTouchPoints>1;var browser={isIE:isIE,isEdge:isEdge,isWebKit:isWebKit,isIPhone:isIPhone,isIPadOS:isIPadOS,isIos:isIos};function cloneDeep(e){return JSON.parse(JSON.stringify(e))}function getDeep(e,t){return t.split(\".\").reduce(((e,t)=>e&&e[t]),e)}function extend(e={},...t){if(!t.length)return e;const i=t.shift();return is.object(i)?(Object.keys(i).forEach((t=>{is.object(i[t])?(Object.keys(e).includes(t)||Object.assign(e,{[t]:{}}),extend(e[t],i[t])):Object.assign(e,{[t]:i[t]})})),extend(e,...t)):e}function wrap(e,t){const i=e.length?e:[e];Array.from(i).reverse().forEach(((e,i)=>{const s=i>0?t.cloneNode(!0):t,n=e.parentNode,r=e.nextSibling;s.appendChild(e),r?n.insertBefore(s,r):n.appendChild(s)}))}function setAttributes(e,t){is.element(e)&&!is.empty(t)&&Object.entries(t).filter((([,e])=>!is.nullOrUndefined(e))).forEach((([t,i])=>e.setAttribute(t,i)))}function createElement(e,t,i){const s=document.createElement(e);return is.object(t)&&setAttributes(s,t),is.string(i)&&(s.innerText=i),s}function insertAfter(e,t){is.element(e)&&is.element(t)&&t.parentNode.insertBefore(e,t.nextSibling)}function insertElement(e,t,i,s){is.element(t)&&t.appendChild(createElement(e,i,s))}function removeElement(e){is.nodeList(e)||is.array(e)?Array.from(e).forEach(removeElement):is.element(e)&&is.element(e.parentNode)&&e.parentNode.removeChild(e)}function emptyElement(e){if(!is.element(e))return;let{length:t}=e.childNodes;for(;t>0;)e.removeChild(e.lastChild),t-=1}function replaceElement(e,t){return is.element(t)&&is.element(t.parentNode)&&is.element(e)?(t.parentNode.replaceChild(e,t),e):null}function getAttributesFromSelector(e,t){if(!is.string(e)||is.empty(e))return{};const i={},s=extend({},t);return e.split(\",\").forEach((e=>{const t=e.trim(),n=t.replace(\".\",\"\"),r=t.replace(/[[\\]]/g,\"\").split(\"=\"),[a]=r,o=r.length>1?r[1].replace(/[\"']/g,\"\"):\"\";switch(t.charAt(0)){case\".\":is.string(s.class)?i.class=`${s.class} ${n}`:i.class=n;break;case\"#\":i.id=t.replace(\"#\",\"\");break;case\"[\":i[a]=o}})),extend(s,i)}function toggleHidden(e,t){if(!is.element(e))return;let i=t;is.boolean(i)||(i=!e.hidden),e.hidden=i}function toggleClass(e,t,i){if(is.nodeList(e))return Array.from(e).map((e=>toggleClass(e,t,i)));if(is.element(e)){let s=\"toggle\";return void 0!==i&&(s=i?\"add\":\"remove\"),e.classList[s](t),e.classList.contains(t)}return!1}function hasClass(e,t){return is.element(e)&&e.classList.contains(t)}function matches(e,t){const{prototype:i}=Element;return(i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.msMatchesSelector||function(){return Array.from(document.querySelectorAll(t)).includes(this)}).call(e,t)}function closest$1(e,t){const{prototype:i}=Element;return(i.closest||function(){let e=this;do{if(matches.matches(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}).call(e,t)}function getElements(e){return this.elements.container.querySelectorAll(e)}function getElement(e){return this.elements.container.querySelector(e)}function setFocus(e=null,t=!1){is.element(e)&&e.focus({preventScroll:!0,focusVisible:t})}const defaultCodecs={\"audio/ogg\":\"vorbis\",\"audio/wav\":\"1\",\"video/webm\":\"vp8, vorbis\",\"video/mp4\":\"avc1.42E01E, mp4a.40.2\",\"video/ogg\":\"theora\"},support={audio:\"canPlayType\"in document.createElement(\"audio\"),video:\"canPlayType\"in document.createElement(\"video\"),check(e,t){const i=support[e]||\"html5\"!==t;return{api:i,ui:i&&support.rangeInput}},pip:!(browser.isIPhone||!is.function(createElement(\"video\").webkitSetPresentationMode)&&(!document.pictureInPictureEnabled||createElement(\"video\").disablePictureInPicture)),airplay:is.function(window.WebKitPlaybackTargetAvailabilityEvent),playsinline:\"playsInline\"in document.createElement(\"video\"),mime(e){if(is.empty(e))return!1;const[t]=e.split(\"/\");let i=e;if(!this.isHTML5||t!==this.type)return!1;Object.keys(defaultCodecs).includes(i)&&(i+=`; codecs=\"${defaultCodecs[e]}\"`);try{return Boolean(i&&this.media.canPlayType(i).replace(/no/,\"\"))}catch(e){return!1}},textTracks:\"textTracks\"in document.createElement(\"video\"),rangeInput:(()=>{const e=document.createElement(\"input\");return e.type=\"range\",\"range\"===e.type})(),touch:\"ontouchstart\"in document.documentElement,transitions:!1!==transitionEndEvent,reducedMotion:\"matchMedia\"in window&&window.matchMedia(\"(prefers-reduced-motion)\").matches},supportsPassiveListeners=(()=>{let e=!1;try{const t=Object.defineProperty({},\"passive\",{get:()=>(e=!0,null)});window.addEventListener(\"test\",null,t),window.removeEventListener(\"test\",null,t)}catch(e){}return e})();function toggleListener(e,t,i,s=!1,n=!0,r=!1){if(!e||!(\"addEventListener\"in e)||is.empty(t)||!is.function(i))return;const a=t.split(\" \");let o=r;supportsPassiveListeners&&(o={passive:n,capture:r}),a.forEach((t=>{this&&this.eventListeners&&s&&this.eventListeners.push({element:e,type:t,callback:i,options:o}),e[s?\"addEventListener\":\"removeEventListener\"](t,i,o)}))}function on(e,t=\"\",i,s=!0,n=!1){toggleListener.call(this,e,t,i,!0,s,n)}function off(e,t=\"\",i,s=!0,n=!1){toggleListener.call(this,e,t,i,!1,s,n)}function once(e,t=\"\",i,s=!0,n=!1){const r=(...a)=>{off(e,t,r,s,n),i.apply(this,a)};toggleListener.call(this,e,t,r,!0,s,n)}function triggerEvent(e,t=\"\",i=!1,s={}){if(!is.element(e)||is.empty(t))return;const n=new CustomEvent(t,{bubbles:i,detail:{...s,plyr:this}});e.dispatchEvent(n)}function unbindListeners(){this&&this.eventListeners&&(this.eventListeners.forEach((e=>{const{element:t,type:i,callback:s,options:n}=e;t.removeEventListener(i,s,n)})),this.eventListeners=[])}function ready(){return new Promise((e=>this.ready?setTimeout(e,0):on.call(this,this.elements.container,\"ready\",e))).then((()=>{}))}function silencePromise(e){is.promise(e)&&e.then(null,(()=>{}))}function dedupe(e){return is.array(e)?e.filter(((t,i)=>e.indexOf(t)===i)):e}function closest(e,t){return is.array(e)&&e.length?e.reduce(((e,i)=>Math.abs(i-t)<Math.abs(e-t)?i:e)):null}function supportsCSS(e){return!(!window||!window.CSS)&&window.CSS.supports(e)}const standardRatios=[[1,1],[4,3],[3,4],[5,4],[4,5],[3,2],[2,3],[16,10],[10,16],[16,9],[9,16],[21,9],[9,21],[32,9],[9,32]].reduce(((e,[t,i])=>({...e,[t/i]:[t,i]})),{});function validateAspectRatio(e){if(!(is.array(e)||is.string(e)&&e.includes(\":\")))return!1;return(is.array(e)?e:e.split(\":\")).map(Number).every(is.number)}function reduceAspectRatio(e){if(!is.array(e)||!e.every(is.number))return null;const[t,i]=e,s=(e,t)=>0===t?e:s(t,e%t),n=s(t,i);return[t/n,i/n]}function getAspectRatio(e){const t=e=>validateAspectRatio(e)?e.split(\":\").map(Number):null;let i=t(e);if(null===i&&(i=t(this.config.ratio)),null===i&&!is.empty(this.embed)&&is.array(this.embed.ratio)&&({ratio:i}=this.embed),null===i&&this.isHTML5){const{videoWidth:e,videoHeight:t}=this.media;i=[e,t]}return reduceAspectRatio(i)}function setAspectRatio(e){if(!this.isVideo)return{};const{wrapper:t}=this.elements,i=getAspectRatio.call(this,e);if(!is.array(i))return{};const[s,n]=reduceAspectRatio(i),r=100/s*n;if(supportsCSS(`aspect-ratio: ${s}/${n}`)?t.style.aspectRatio=`${s}/${n}`:t.style.paddingBottom=`${r}%`,this.isVimeo&&!this.config.vimeo.premium&&this.supported.ui){const e=100/this.media.offsetWidth*parseInt(window.getComputedStyle(this.media).paddingBottom,10),i=(e-r)/(e/50);this.fullscreen.active?t.style.paddingBottom=null:this.media.style.transform=`translateY(-${i}%)`}else this.isHTML5&&t.classList.add(this.config.classNames.videoFixedRatio);return{padding:r,ratio:i}}function roundAspectRatio(e,t,i=.05){const s=e/t,n=closest(Object.keys(standardRatios),s);return Math.abs(n-s)<=i?standardRatios[n]:[e,t]}function getViewportSize(){return[Math.max(document.documentElement.clientWidth||0,window.innerWidth||0),Math.max(document.documentElement.clientHeight||0,window.innerHeight||0)]}const html5={getSources(){if(!this.isHTML5)return[];return Array.from(this.media.querySelectorAll(\"source\")).filter((e=>{const t=e.getAttribute(\"type\");return!!is.empty(t)||support.mime.call(this,t)}))},getQualityOptions(){return this.config.quality.forced?this.config.quality.options:html5.getSources.call(this).map((e=>Number(e.getAttribute(\"size\")))).filter(Boolean)},setup(){if(!this.isHTML5)return;const e=this;e.options.speed=e.config.speed.options,is.empty(this.config.ratio)||setAspectRatio.call(e),Object.defineProperty(e.media,\"quality\",{get(){const t=html5.getSources.call(e).find((t=>t.getAttribute(\"src\")===e.source));return t&&Number(t.getAttribute(\"size\"))},set(t){if(e.quality!==t){if(e.config.quality.forced&&is.function(e.config.quality.onChange))e.config.quality.onChange(t);else{const i=html5.getSources.call(e).find((e=>Number(e.getAttribute(\"size\"))===t));if(!i)return;const{currentTime:s,paused:n,preload:r,readyState:a,playbackRate:o}=e.media;e.media.src=i.getAttribute(\"src\"),(\"none\"!==r||a)&&(e.once(\"loadedmetadata\",(()=>{e.speed=o,e.currentTime=s,n||silencePromise(e.play())})),e.media.load())}triggerEvent.call(e,e.media,\"qualitychange\",!1,{quality:t})}}})},cancelRequests(){this.isHTML5&&(removeElement(html5.getSources.call(this)),this.media.setAttribute(\"src\",this.config.blankVideo),this.media.load(),this.debug.log(\"Cancelled network requests\"))}};function generateId(e){return`${e}-${Math.floor(1e4*Math.random())}`}function format(e,...t){return is.empty(e)?e:e.toString().replace(/{(\\d+)}/g,((e,i)=>t[i].toString()))}function getPercentage(e,t){return 0===e||0===t||Number.isNaN(e)||Number.isNaN(t)?0:(e/t*100).toFixed(2)}const replaceAll=(e=\"\",t=\"\",i=\"\")=>e.replace(new RegExp(t.toString().replace(/([.*+?^=!:${}()|[\\]/\\\\])/g,\"\\\\$1\"),\"g\"),i.toString()),toTitleCase=(e=\"\")=>e.toString().replace(/\\w\\S*/g,(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()));function toPascalCase(e=\"\"){let t=e.toString();return t=replaceAll(t,\"-\",\" \"),t=replaceAll(t,\"_\",\" \"),t=toTitleCase(t),replaceAll(t,\" \",\"\")}function toCamelCase(e=\"\"){let t=e.toString();return t=toPascalCase(t),t.charAt(0).toLowerCase()+t.slice(1)}function stripHTML(e){const t=document.createDocumentFragment(),i=document.createElement(\"div\");return t.appendChild(i),i.innerHTML=e,t.firstChild.innerText}function getHTML(e){const t=document.createElement(\"div\");return t.appendChild(e),t.innerHTML}const resources={pip:\"PIP\",airplay:\"AirPlay\",html5:\"HTML5\",vimeo:\"Vimeo\",youtube:\"YouTube\"},i18n={get(e=\"\",t={}){if(is.empty(e)||is.empty(t))return\"\";let i=getDeep(t.i18n,e);if(is.empty(i))return Object.keys(resources).includes(e)?resources[e]:\"\";const s={\"{seektime}\":t.seekTime,\"{title}\":t.title};return Object.entries(s).forEach((([e,t])=>{i=replaceAll(i,e,t)})),i}};class Storage{constructor(e){_defineProperty$1(this,\"get\",(e=>{if(!Storage.supported||!this.enabled)return null;const t=window.localStorage.getItem(this.key);if(is.empty(t))return null;const i=JSON.parse(t);return is.string(e)&&e.length?i[e]:i})),_defineProperty$1(this,\"set\",(e=>{if(!Storage.supported||!this.enabled)return;if(!is.object(e))return;let t=this.get();is.empty(t)&&(t={}),extend(t,e);try{window.localStorage.setItem(this.key,JSON.stringify(t))}catch(e){}})),this.enabled=e.config.storage.enabled,this.key=e.config.storage.key}static get supported(){try{if(!(\"localStorage\"in window))return!1;const e=\"___test\";return window.localStorage.setItem(e,e),window.localStorage.removeItem(e),!0}catch(e){return!1}}}function fetch(e,t=\"text\"){return new Promise(((i,s)=>{try{const s=new XMLHttpRequest;if(!(\"withCredentials\"in s))return;s.addEventListener(\"load\",(()=>{if(\"text\"===t)try{i(JSON.parse(s.responseText))}catch(e){i(s.responseText)}else i(s.response)})),s.addEventListener(\"error\",(()=>{throw new Error(s.status)})),s.open(\"GET\",e,!0),s.responseType=t,s.send()}catch(e){s(e)}}))}function loadSprite(e,t){if(!is.string(e))return;const i=\"cache\",s=is.string(t);let n=!1;const r=()=>null!==document.getElementById(t),a=(e,t)=>{e.innerHTML=t,s&&r()||document.body.insertAdjacentElement(\"afterbegin\",e)};if(!s||!r()){const r=Storage.supported,o=document.createElement(\"div\");if(o.setAttribute(\"hidden\",\"\"),s&&o.setAttribute(\"id\",t),r){const e=window.localStorage.getItem(`${i}-${t}`);if(n=null!==e,n){const t=JSON.parse(e);a(o,t.content)}}fetch(e).then((e=>{if(!is.empty(e)){if(r)try{window.localStorage.setItem(`${i}-${t}`,JSON.stringify({content:e}))}catch(e){}a(o,e)}})).catch((()=>{}))}}const getHours=e=>Math.trunc(e/60/60%60,10),getMinutes=e=>Math.trunc(e/60%60,10),getSeconds=e=>Math.trunc(e%60,10);function formatTime(e=0,t=!1,i=!1){if(!is.number(e))return formatTime(void 0,t,i);const s=e=>`0${e}`.slice(-2);let n=getHours(e);const r=getMinutes(e),a=getSeconds(e);return n=t||n>0?`${n}:`:\"\",`${i&&e>0?\"-\":\"\"}${n}${s(r)}:${s(a)}`}const controls={getIconUrl(){const e=new URL(this.config.iconUrl,window.location),t=window.location.host?window.location.host:window.top.location.host,i=e.host!==t||browser.isIE&&!window.svg4everybody;return{url:this.config.iconUrl,cors:i}},findElements(){try{return this.elements.controls=getElement.call(this,this.config.selectors.controls.wrapper),this.elements.buttons={play:getElements.call(this,this.config.selectors.buttons.play),pause:getElement.call(this,this.config.selectors.buttons.pause),restart:getElement.call(this,this.config.selectors.buttons.restart),rewind:getElement.call(this,this.config.selectors.buttons.rewind),fastForward:getElement.call(this,this.config.selectors.buttons.fastForward),mute:getElement.call(this,this.config.selectors.buttons.mute),pip:getElement.call(this,this.config.selectors.buttons.pip),airplay:getElement.call(this,this.config.selectors.buttons.airplay),settings:getElement.call(this,this.config.selectors.buttons.settings),captions:getElement.call(this,this.config.selectors.buttons.captions),fullscreen:getElement.call(this,this.config.selectors.buttons.fullscreen)},this.elements.progress=getElement.call(this,this.config.selectors.progress),this.elements.inputs={seek:getElement.call(this,this.config.selectors.inputs.seek),volume:getElement.call(this,this.config.selectors.inputs.volume)},this.elements.display={buffer:getElement.call(this,this.config.selectors.display.buffer),currentTime:getElement.call(this,this.config.selectors.display.currentTime),duration:getElement.call(this,this.config.selectors.display.duration)},is.element(this.elements.progress)&&(this.elements.display.seekTooltip=this.elements.progress.querySelector(`.${this.config.classNames.tooltip}`)),!0}catch(e){return this.debug.warn(\"It looks like there is a problem with your custom controls HTML\",e),this.toggleNativeControls(!0),!1}},createIcon(e,t){const i=\"http://www.w3.org/2000/svg\",s=controls.getIconUrl.call(this),n=`${s.cors?\"\":s.url}#${this.config.iconPrefix}`,r=document.createElementNS(i,\"svg\");setAttributes(r,extend(t,{\"aria-hidden\":\"true\",focusable:\"false\"}));const a=document.createElementNS(i,\"use\"),o=`${n}-${e}`;return\"href\"in a&&a.setAttributeNS(\"http://www.w3.org/1999/xlink\",\"href\",o),a.setAttributeNS(\"http://www.w3.org/1999/xlink\",\"xlink:href\",o),r.appendChild(a),r},createLabel(e,t={}){const i=i18n.get(e,this.config);return createElement(\"span\",{...t,class:[t.class,this.config.classNames.hidden].filter(Boolean).join(\" \")},i)},createBadge(e){if(is.empty(e))return null;const t=createElement(\"span\",{class:this.config.classNames.menu.value});return t.appendChild(createElement(\"span\",{class:this.config.classNames.menu.badge},e)),t},createButton(e,t){const i=extend({},t);let s=toCamelCase(e);const n={element:\"button\",toggle:!1,label:null,icon:null,labelPressed:null,iconPressed:null};switch([\"element\",\"icon\",\"label\"].forEach((e=>{Object.keys(i).includes(e)&&(n[e]=i[e],delete i[e])})),\"button\"!==n.element||Object.keys(i).includes(\"type\")||(i.type=\"button\"),Object.keys(i).includes(\"class\")?i.class.split(\" \").some((e=>e===this.config.classNames.control))||extend(i,{class:`${i.class} ${this.config.classNames.control}`}):i.class=this.config.classNames.control,e){case\"play\":n.toggle=!0,n.label=\"play\",n.labelPressed=\"pause\",n.icon=\"play\",n.iconPressed=\"pause\";break;case\"mute\":n.toggle=!0,n.label=\"mute\",n.labelPressed=\"unmute\",n.icon=\"volume\",n.iconPressed=\"muted\";break;case\"captions\":n.toggle=!0,n.label=\"enableCaptions\",n.labelPressed=\"disableCaptions\",n.icon=\"captions-off\",n.iconPressed=\"captions-on\";break;case\"fullscreen\":n.toggle=!0,n.label=\"enterFullscreen\",n.labelPressed=\"exitFullscreen\",n.icon=\"enter-fullscreen\",n.iconPressed=\"exit-fullscreen\";break;case\"play-large\":i.class+=` ${this.config.classNames.control}--overlaid`,s=\"play\",n.label=\"play\",n.icon=\"play\";break;default:is.empty(n.label)&&(n.label=s),is.empty(n.icon)&&(n.icon=e)}const r=createElement(n.element);return n.toggle?(r.appendChild(controls.createIcon.call(this,n.iconPressed,{class:\"icon--pressed\"})),r.appendChild(controls.createIcon.call(this,n.icon,{class:\"icon--not-pressed\"})),r.appendChild(controls.createLabel.call(this,n.labelPressed,{class:\"label--pressed\"})),r.appendChild(controls.createLabel.call(this,n.label,{class:\"label--not-pressed\"}))):(r.appendChild(controls.createIcon.call(this,n.icon)),r.appendChild(controls.createLabel.call(this,n.label))),extend(i,getAttributesFromSelector(this.config.selectors.buttons[s],i)),setAttributes(r,i),\"play\"===s?(is.array(this.elements.buttons[s])||(this.elements.buttons[s]=[]),this.elements.buttons[s].push(r)):this.elements.buttons[s]=r,r},createRange(e,t){const i=createElement(\"input\",extend(getAttributesFromSelector(this.config.selectors.inputs[e]),{type:\"range\",min:0,max:100,step:.01,value:0,autocomplete:\"off\",role:\"slider\",\"aria-label\":i18n.get(e,this.config),\"aria-valuemin\":0,\"aria-valuemax\":100,\"aria-valuenow\":0},t));return this.elements.inputs[e]=i,controls.updateRangeFill.call(this,i),RangeTouch.setup(i),i},createProgress(e,t){const i=createElement(\"progress\",extend(getAttributesFromSelector(this.config.selectors.display[e]),{min:0,max:100,value:0,role:\"progressbar\",\"aria-hidden\":!0},t));if(\"volume\"!==e){i.appendChild(createElement(\"span\",null,\"0\"));const t={played:\"played\",buffer:\"buffered\"}[e],s=t?i18n.get(t,this.config):\"\";i.innerText=`% ${s.toLowerCase()}`}return this.elements.display[e]=i,i},createTime(e,t){const i=getAttributesFromSelector(this.config.selectors.display[e],t),s=createElement(\"div\",extend(i,{class:`${i.class?i.class:\"\"} ${this.config.classNames.display.time} `.trim(),\"aria-label\":i18n.get(e,this.config),role:\"timer\"}),\"00:00\");return this.elements.display[e]=s,s},bindMenuItemShortcuts(e,t){on.call(this,e,\"keydown keyup\",(i=>{if(![\" \",\"ArrowUp\",\"ArrowDown\",\"ArrowRight\"].includes(i.key))return;if(i.preventDefault(),i.stopPropagation(),\"keydown\"===i.type)return;const s=matches(e,'[role=\"menuitemradio\"]');if(!s&&[\" \",\"ArrowRight\"].includes(i.key))controls.showMenuPanel.call(this,t,!0);else{let t;\" \"!==i.key&&(\"ArrowDown\"===i.key||s&&\"ArrowRight\"===i.key?(t=e.nextElementSibling,is.element(t)||(t=e.parentNode.firstElementChild)):(t=e.previousElementSibling,is.element(t)||(t=e.parentNode.lastElementChild)),setFocus.call(this,t,!0))}}),!1),on.call(this,e,\"keyup\",(e=>{\"Return\"===e.key&&controls.focusFirstMenuItem.call(this,null,!0)}))},createMenuItem({value:e,list:t,type:i,title:s,badge:n=null,checked:r=!1}){const a=getAttributesFromSelector(this.config.selectors.inputs[i]),o=createElement(\"button\",extend(a,{type:\"button\",role:\"menuitemradio\",class:`${this.config.classNames.control} ${a.class?a.class:\"\"}`.trim(),\"aria-checked\":r,value:e})),l=createElement(\"span\");l.innerHTML=s,is.element(n)&&l.appendChild(n),o.appendChild(l),Object.defineProperty(o,\"checked\",{enumerable:!0,get:()=>\"true\"===o.getAttribute(\"aria-checked\"),set(e){e&&Array.from(o.parentNode.children).filter((e=>matches(e,'[role=\"menuitemradio\"]'))).forEach((e=>e.setAttribute(\"aria-checked\",\"false\"))),o.setAttribute(\"aria-checked\",e?\"true\":\"false\")}}),this.listeners.bind(o,\"click keyup\",(t=>{if(!is.keyboardEvent(t)||\" \"===t.key){switch(t.preventDefault(),t.stopPropagation(),o.checked=!0,i){case\"language\":this.currentTrack=Number(e);break;case\"quality\":this.quality=e;break;case\"speed\":this.speed=parseFloat(e)}controls.showMenuPanel.call(this,\"home\",is.keyboardEvent(t))}}),i,!1),controls.bindMenuItemShortcuts.call(this,o,i),t.appendChild(o)},formatTime(e=0,t=!1){if(!is.number(e))return e;return formatTime(e,getHours(this.duration)>0,t)},updateTimeDisplay(e=null,t=0,i=!1){is.element(e)&&is.number(t)&&(e.innerText=controls.formatTime(t,i))},updateVolume(){this.supported.ui&&(is.element(this.elements.inputs.volume)&&controls.setRange.call(this,this.elements.inputs.volume,this.muted?0:this.volume),is.element(this.elements.buttons.mute)&&(this.elements.buttons.mute.pressed=this.muted||0===this.volume))},setRange(e,t=0){is.element(e)&&(e.value=t,controls.updateRangeFill.call(this,e))},updateProgress(e){if(!this.supported.ui||!is.event(e))return;let t=0;const i=(e,t)=>{const i=is.number(t)?t:0,s=is.element(e)?e:this.elements.display.buffer;if(is.element(s)){s.value=i;const e=s.getElementsByTagName(\"span\")[0];is.element(e)&&(e.childNodes[0].nodeValue=i)}};if(e)switch(e.type){case\"timeupdate\":case\"seeking\":case\"seeked\":t=getPercentage(this.currentTime,this.duration),\"timeupdate\"===e.type&&controls.setRange.call(this,this.elements.inputs.seek,t);break;case\"playing\":case\"progress\":i(this.elements.display.buffer,100*this.buffered)}},updateRangeFill(e){const t=is.event(e)?e.target:e;if(is.element(t)&&\"range\"===t.getAttribute(\"type\")){if(matches(t,this.config.selectors.inputs.seek)){t.setAttribute(\"aria-valuenow\",this.currentTime);const e=controls.formatTime(this.currentTime),i=controls.formatTime(this.duration),s=i18n.get(\"seekLabel\",this.config);t.setAttribute(\"aria-valuetext\",s.replace(\"{currentTime}\",e).replace(\"{duration}\",i))}else if(matches(t,this.config.selectors.inputs.volume)){const e=100*t.value;t.setAttribute(\"aria-valuenow\",e),t.setAttribute(\"aria-valuetext\",`${e.toFixed(1)}%`)}else t.setAttribute(\"aria-valuenow\",t.value);(browser.isWebKit||browser.isIPadOS)&&t.style.setProperty(\"--value\",t.value/t.max*100+\"%\")}},updateSeekTooltip(e){var t,i;if(!this.config.tooltips.seek||!is.element(this.elements.inputs.seek)||!is.element(this.elements.display.seekTooltip)||0===this.duration)return;const s=this.elements.display.seekTooltip,n=`${this.config.classNames.tooltip}--visible`,r=e=>toggleClass(s,n,e);if(this.touch)return void r(!1);let a=0;const o=this.elements.progress.getBoundingClientRect();if(is.event(e))a=100/o.width*(e.pageX-o.left);else{if(!hasClass(s,n))return;a=parseFloat(s.style.left,10)}a<0?a=0:a>100&&(a=100);const l=this.duration/100*a;s.innerText=controls.formatTime(l);const c=null===(t=this.config.markers)||void 0===t||null===(i=t.points)||void 0===i?void 0:i.find((({time:e})=>e===Math.round(l)));c&&s.insertAdjacentHTML(\"afterbegin\",`${c.label}<br>`),s.style.left=`${a}%`,is.event(e)&&[\"mouseenter\",\"mouseleave\"].includes(e.type)&&r(\"mouseenter\"===e.type)},timeUpdate(e){const t=!is.element(this.elements.display.duration)&&this.config.invertTime;controls.updateTimeDisplay.call(this,this.elements.display.currentTime,t?this.duration-this.currentTime:this.currentTime,t),e&&\"timeupdate\"===e.type&&this.media.seeking||controls.updateProgress.call(this,e)},durationUpdate(){if(!this.supported.ui||!this.config.invertTime&&this.currentTime)return;if(this.duration>=2**32)return toggleHidden(this.elements.display.currentTime,!0),void toggleHidden(this.elements.progress,!0);is.element(this.elements.inputs.seek)&&this.elements.inputs.seek.setAttribute(\"aria-valuemax\",this.duration);const e=is.element(this.elements.display.duration);!e&&this.config.displayDuration&&this.paused&&controls.updateTimeDisplay.call(this,this.elements.display.currentTime,this.duration),e&&controls.updateTimeDisplay.call(this,this.elements.display.duration,this.duration),this.config.markers.enabled&&controls.setMarkers.call(this),controls.updateSeekTooltip.call(this)},toggleMenuButton(e,t){toggleHidden(this.elements.settings.buttons[e],!t)},updateSetting(e,t,i){const s=this.elements.settings.panels[e];let n=null,r=t;if(\"captions\"===e)n=this.currentTrack;else{if(n=is.empty(i)?this[e]:i,is.empty(n)&&(n=this.config[e].default),!is.empty(this.options[e])&&!this.options[e].includes(n))return void this.debug.warn(`Unsupported value of '${n}' for ${e}`);if(!this.config[e].options.includes(n))return void this.debug.warn(`Disabled value of '${n}' for ${e}`)}if(is.element(r)||(r=s&&s.querySelector('[role=\"menu\"]')),!is.element(r))return;this.elements.settings.buttons[e].querySelector(`.${this.config.classNames.menu.value}`).innerHTML=controls.getLabel.call(this,e,n);const a=r&&r.querySelector(`[value=\"${n}\"]`);is.element(a)&&(a.checked=!0)},getLabel(e,t){switch(e){case\"speed\":return 1===t?i18n.get(\"normal\",this.config):`${t}&times;`;case\"quality\":if(is.number(t)){const e=i18n.get(`qualityLabel.${t}`,this.config);return e.length?e:`${t}p`}return toTitleCase(t);case\"captions\":return captions.getLabel.call(this);default:return null}},setQualityMenu(e){if(!is.element(this.elements.settings.panels.quality))return;const t=\"quality\",i=this.elements.settings.panels.quality.querySelector('[role=\"menu\"]');is.array(e)&&(this.options.quality=dedupe(e).filter((e=>this.config.quality.options.includes(e))));const s=!is.empty(this.options.quality)&&this.options.quality.length>1;if(controls.toggleMenuButton.call(this,t,s),emptyElement(i),controls.checkMenu.call(this),!s)return;const n=e=>{const t=i18n.get(`qualityBadge.${e}`,this.config);return t.length?controls.createBadge.call(this,t):null};this.options.quality.sort(((e,t)=>{const i=this.config.quality.options;return i.indexOf(e)>i.indexOf(t)?1:-1})).forEach((e=>{controls.createMenuItem.call(this,{value:e,list:i,type:t,title:controls.getLabel.call(this,\"quality\",e),badge:n(e)})})),controls.updateSetting.call(this,t,i)},setCaptionsMenu(){if(!is.element(this.elements.settings.panels.captions))return;const e=\"captions\",t=this.elements.settings.panels.captions.querySelector('[role=\"menu\"]'),i=captions.getTracks.call(this),s=Boolean(i.length);if(controls.toggleMenuButton.call(this,e,s),emptyElement(t),controls.checkMenu.call(this),!s)return;const n=i.map(((e,i)=>({value:i,checked:this.captions.toggled&&this.currentTrack===i,title:captions.getLabel.call(this,e),badge:e.language&&controls.createBadge.call(this,e.language.toUpperCase()),list:t,type:\"language\"})));n.unshift({value:-1,checked:!this.captions.toggled,title:i18n.get(\"disabled\",this.config),list:t,type:\"language\"}),n.forEach(controls.createMenuItem.bind(this)),controls.updateSetting.call(this,e,t)},setSpeedMenu(){if(!is.element(this.elements.settings.panels.speed))return;const e=\"speed\",t=this.elements.settings.panels.speed.querySelector('[role=\"menu\"]');this.options.speed=this.options.speed.filter((e=>e>=this.minimumSpeed&&e<=this.maximumSpeed));const i=!is.empty(this.options.speed)&&this.options.speed.length>1;controls.toggleMenuButton.call(this,e,i),emptyElement(t),controls.checkMenu.call(this),i&&(this.options.speed.forEach((i=>{controls.createMenuItem.call(this,{value:i,list:t,type:e,title:controls.getLabel.call(this,\"speed\",i)})})),controls.updateSetting.call(this,e,t))},checkMenu(){const{buttons:e}=this.elements.settings,t=!is.empty(e)&&Object.values(e).some((e=>!e.hidden));toggleHidden(this.elements.settings.menu,!t)},focusFirstMenuItem(e,t=!1){if(this.elements.settings.popup.hidden)return;let i=e;is.element(i)||(i=Object.values(this.elements.settings.panels).find((e=>!e.hidden)));const s=i.querySelector('[role^=\"menuitem\"]');setFocus.call(this,s,t)},toggleMenu(e){const{popup:t}=this.elements.settings,i=this.elements.buttons.settings;if(!is.element(t)||!is.element(i))return;const{hidden:s}=t;let n=s;if(is.boolean(e))n=e;else if(is.keyboardEvent(e)&&\"Escape\"===e.key)n=!1;else if(is.event(e)){const s=is.function(e.composedPath)?e.composedPath()[0]:e.target,r=t.contains(s);if(r||!r&&e.target!==i&&n)return}i.setAttribute(\"aria-expanded\",n),toggleHidden(t,!n),toggleClass(this.elements.container,this.config.classNames.menu.open,n),n&&is.keyboardEvent(e)?controls.focusFirstMenuItem.call(this,null,!0):n||s||setFocus.call(this,i,is.keyboardEvent(e))},getMenuSize(e){const t=e.cloneNode(!0);t.style.position=\"absolute\",t.style.opacity=0,t.removeAttribute(\"hidden\"),e.parentNode.appendChild(t);const i=t.scrollWidth,s=t.scrollHeight;return removeElement(t),{width:i,height:s}},showMenuPanel(e=\"\",t=!1){const i=this.elements.container.querySelector(`#plyr-settings-${this.id}-${e}`);if(!is.element(i))return;const s=i.parentNode,n=Array.from(s.children).find((e=>!e.hidden));if(support.transitions&&!support.reducedMotion){s.style.width=`${n.scrollWidth}px`,s.style.height=`${n.scrollHeight}px`;const e=controls.getMenuSize.call(this,i),t=e=>{e.target===s&&[\"width\",\"height\"].includes(e.propertyName)&&(s.style.width=\"\",s.style.height=\"\",off.call(this,s,transitionEndEvent,t))};on.call(this,s,transitionEndEvent,t),s.style.width=`${e.width}px`,s.style.height=`${e.height}px`}toggleHidden(n,!0),toggleHidden(i,!1),controls.focusFirstMenuItem.call(this,i,t)},setDownloadUrl(){const e=this.elements.buttons.download;is.element(e)&&e.setAttribute(\"href\",this.download)},create(e){const{bindMenuItemShortcuts:t,createButton:i,createProgress:s,createRange:n,createTime:r,setQualityMenu:a,setSpeedMenu:o,showMenuPanel:l}=controls;this.elements.controls=null,is.array(this.config.controls)&&this.config.controls.includes(\"play-large\")&&this.elements.container.appendChild(i.call(this,\"play-large\"));const c=createElement(\"div\",getAttributesFromSelector(this.config.selectors.controls.wrapper));this.elements.controls=c;const u={class:\"plyr__controls__item\"};return dedupe(is.array(this.config.controls)?this.config.controls:[]).forEach((a=>{if(\"restart\"===a&&c.appendChild(i.call(this,\"restart\",u)),\"rewind\"===a&&c.appendChild(i.call(this,\"rewind\",u)),\"play\"===a&&c.appendChild(i.call(this,\"play\",u)),\"fast-forward\"===a&&c.appendChild(i.call(this,\"fast-forward\",u)),\"progress\"===a){const t=createElement(\"div\",{class:`${u.class} plyr__progress__container`}),i=createElement(\"div\",getAttributesFromSelector(this.config.selectors.progress));if(i.appendChild(n.call(this,\"seek\",{id:`plyr-seek-${e.id}`})),i.appendChild(s.call(this,\"buffer\")),this.config.tooltips.seek){const e=createElement(\"span\",{class:this.config.classNames.tooltip},\"00:00\");i.appendChild(e),this.elements.display.seekTooltip=e}this.elements.progress=i,t.appendChild(this.elements.progress),c.appendChild(t)}if(\"current-time\"===a&&c.appendChild(r.call(this,\"currentTime\",u)),\"duration\"===a&&c.appendChild(r.call(this,\"duration\",u)),\"mute\"===a||\"volume\"===a){let{volume:t}=this.elements;if(is.element(t)&&c.contains(t)||(t=createElement(\"div\",extend({},u,{class:`${u.class} plyr__volume`.trim()})),this.elements.volume=t,c.appendChild(t)),\"mute\"===a&&t.appendChild(i.call(this,\"mute\")),\"volume\"===a&&!browser.isIos&&!browser.isIPadOS){const i={max:1,step:.05,value:this.config.volume};t.appendChild(n.call(this,\"volume\",extend(i,{id:`plyr-volume-${e.id}`})))}}if(\"captions\"===a&&c.appendChild(i.call(this,\"captions\",u)),\"settings\"===a&&!is.empty(this.config.settings)){const s=createElement(\"div\",extend({},u,{class:`${u.class} plyr__menu`.trim(),hidden:\"\"}));s.appendChild(i.call(this,\"settings\",{\"aria-haspopup\":!0,\"aria-controls\":`plyr-settings-${e.id}`,\"aria-expanded\":!1}));const n=createElement(\"div\",{class:\"plyr__menu__container\",id:`plyr-settings-${e.id}`,hidden:\"\"}),r=createElement(\"div\"),a=createElement(\"div\",{id:`plyr-settings-${e.id}-home`}),o=createElement(\"div\",{role:\"menu\"});a.appendChild(o),r.appendChild(a),this.elements.settings.panels.home=a,this.config.settings.forEach((i=>{const s=createElement(\"button\",extend(getAttributesFromSelector(this.config.selectors.buttons.settings),{type:\"button\",class:`${this.config.classNames.control} ${this.config.classNames.control}--forward`,role:\"menuitem\",\"aria-haspopup\":!0,hidden:\"\"}));t.call(this,s,i),on.call(this,s,\"click\",(()=>{l.call(this,i,!1)}));const n=createElement(\"span\",null,i18n.get(i,this.config)),a=createElement(\"span\",{class:this.config.classNames.menu.value});a.innerHTML=e[i],n.appendChild(a),s.appendChild(n),o.appendChild(s);const c=createElement(\"div\",{id:`plyr-settings-${e.id}-${i}`,hidden:\"\"}),u=createElement(\"button\",{type:\"button\",class:`${this.config.classNames.control} ${this.config.classNames.control}--back`});u.appendChild(createElement(\"span\",{\"aria-hidden\":!0},i18n.get(i,this.config))),u.appendChild(createElement(\"span\",{class:this.config.classNames.hidden},i18n.get(\"menuBack\",this.config))),on.call(this,c,\"keydown\",(e=>{\"ArrowLeft\"===e.key&&(e.preventDefault(),e.stopPropagation(),l.call(this,\"home\",!0))}),!1),on.call(this,u,\"click\",(()=>{l.call(this,\"home\",!1)})),c.appendChild(u),c.appendChild(createElement(\"div\",{role:\"menu\"})),r.appendChild(c),this.elements.settings.buttons[i]=s,this.elements.settings.panels[i]=c})),n.appendChild(r),s.appendChild(n),c.appendChild(s),this.elements.settings.popup=n,this.elements.settings.menu=s}if(\"pip\"===a&&support.pip&&c.appendChild(i.call(this,\"pip\",u)),\"airplay\"===a&&support.airplay&&c.appendChild(i.call(this,\"airplay\",u)),\"download\"===a){const e=extend({},u,{element:\"a\",href:this.download,target:\"_blank\"});this.isHTML5&&(e.download=\"\");const{download:t}=this.config.urls;!is.url(t)&&this.isEmbed&&extend(e,{icon:`logo-${this.provider}`,label:this.provider}),c.appendChild(i.call(this,\"download\",e))}\"fullscreen\"===a&&c.appendChild(i.call(this,\"fullscreen\",u))})),this.isHTML5&&a.call(this,html5.getQualityOptions.call(this)),o.call(this),c},inject(){if(this.config.loadSprite){const e=controls.getIconUrl.call(this);e.cors&&loadSprite(e.url,\"sprite-plyr\")}this.id=Math.floor(1e4*Math.random());let e=null;this.elements.controls=null;const t={id:this.id,seektime:this.config.seekTime,title:this.config.title};let i=!0;is.function(this.config.controls)&&(this.config.controls=this.config.controls.call(this,t)),this.config.controls||(this.config.controls=[]),is.element(this.config.controls)||is.string(this.config.controls)?e=this.config.controls:(e=controls.create.call(this,{id:this.id,seektime:this.config.seekTime,speed:this.speed,quality:this.quality,captions:captions.getLabel.call(this)}),i=!1);let s;i&&is.string(this.config.controls)&&(e=(e=>{let i=e;return Object.entries(t).forEach((([e,t])=>{i=replaceAll(i,`{${e}}`,t)})),i})(e)),is.string(this.config.selectors.controls.container)&&(s=document.querySelector(this.config.selectors.controls.container)),is.element(s)||(s=this.elements.container);if(s[is.element(e)?\"insertAdjacentElement\":\"insertAdjacentHTML\"](\"afterbegin\",e),is.element(this.elements.controls)||controls.findElements.call(this),!is.empty(this.elements.buttons)){const e=e=>{const t=this.config.classNames.controlPressed;e.setAttribute(\"aria-pressed\",\"false\"),Object.defineProperty(e,\"pressed\",{configurable:!0,enumerable:!0,get:()=>hasClass(e,t),set(i=!1){toggleClass(e,t,i),e.setAttribute(\"aria-pressed\",i?\"true\":\"false\")}})};Object.values(this.elements.buttons).filter(Boolean).forEach((t=>{is.array(t)||is.nodeList(t)?Array.from(t).filter(Boolean).forEach(e):e(t)}))}if(browser.isEdge&&repaint(s),this.config.tooltips.controls){const{classNames:e,selectors:t}=this.config,i=`${t.controls.wrapper} ${t.labels} .${e.hidden}`,s=getElements.call(this,i);Array.from(s).forEach((e=>{toggleClass(e,this.config.classNames.hidden,!1),toggleClass(e,this.config.classNames.tooltip,!0)}))}},setMediaMetadata(){try{\"mediaSession\"in navigator&&(navigator.mediaSession.metadata=new window.MediaMetadata({title:this.config.mediaMetadata.title,artist:this.config.mediaMetadata.artist,album:this.config.mediaMetadata.album,artwork:this.config.mediaMetadata.artwork}))}catch(e){}},setMarkers(){var e,t;if(!this.duration||this.elements.markers)return;const i=null===(e=this.config.markers)||void 0===e||null===(t=e.points)||void 0===t?void 0:t.filter((({time:e})=>e>0&&e<this.duration));if(null==i||!i.length)return;const s=document.createDocumentFragment(),n=document.createDocumentFragment();let r=null;const a=`${this.config.classNames.tooltip}--visible`,o=e=>toggleClass(r,a,e);i.forEach((e=>{const t=createElement(\"span\",{class:this.config.classNames.marker},\"\"),i=e.time/this.duration*100+\"%\";r&&(t.addEventListener(\"mouseenter\",(()=>{e.label||(r.style.left=i,r.innerHTML=e.label,o(!0))})),t.addEventListener(\"mouseleave\",(()=>{o(!1)}))),t.addEventListener(\"click\",(()=>{this.currentTime=e.time})),t.style.left=i,n.appendChild(t)})),s.appendChild(n),this.config.tooltips.seek||(r=createElement(\"span\",{class:this.config.classNames.tooltip},\"\"),s.appendChild(r)),this.elements.markers={points:n,tip:r},this.elements.progress.appendChild(s)}};function parseUrl(e,t=!0){let i=e;if(t){const e=document.createElement(\"a\");e.href=i,i=e.href}try{return new URL(i)}catch(e){return null}}function buildUrlParams(e){const t=new URLSearchParams;return is.object(e)&&Object.entries(e).forEach((([e,i])=>{t.set(e,i)})),t}const captions={setup(){if(!this.supported.ui)return;if(!this.isVideo||this.isYouTube||this.isHTML5&&!support.textTracks)return void(is.array(this.config.controls)&&this.config.controls.includes(\"settings\")&&this.config.settings.includes(\"captions\")&&controls.setCaptionsMenu.call(this));if(is.element(this.elements.captions)||(this.elements.captions=createElement(\"div\",getAttributesFromSelector(this.config.selectors.captions)),this.elements.captions.setAttribute(\"dir\",\"auto\"),insertAfter(this.elements.captions,this.elements.wrapper)),browser.isIE&&window.URL){const e=this.media.querySelectorAll(\"track\");Array.from(e).forEach((e=>{const t=e.getAttribute(\"src\"),i=parseUrl(t);null!==i&&i.hostname!==window.location.href.hostname&&[\"http:\",\"https:\"].includes(i.protocol)&&fetch(t,\"blob\").then((t=>{e.setAttribute(\"src\",window.URL.createObjectURL(t))})).catch((()=>{removeElement(e)}))}))}const e=dedupe((navigator.languages||[navigator.language||navigator.userLanguage||\"en\"]).map((e=>e.split(\"-\")[0])));let t=(this.storage.get(\"language\")||this.config.captions.language||\"auto\").toLowerCase();\"auto\"===t&&([t]=e);let i=this.storage.get(\"captions\");if(is.boolean(i)||({active:i}=this.config.captions),Object.assign(this.captions,{toggled:!1,active:i,language:t,languages:e}),this.isHTML5){const e=this.config.captions.update?\"addtrack removetrack\":\"removetrack\";on.call(this,this.media.textTracks,e,captions.update.bind(this))}setTimeout(captions.update.bind(this),0)},update(){const e=captions.getTracks.call(this,!0),{active:t,language:i,meta:s,currentTrackNode:n}=this.captions,r=Boolean(e.find((e=>e.language===i)));this.isHTML5&&this.isVideo&&e.filter((e=>!s.get(e))).forEach((e=>{this.debug.log(\"Track added\",e),s.set(e,{default:\"showing\"===e.mode}),\"showing\"===e.mode&&(e.mode=\"hidden\"),on.call(this,e,\"cuechange\",(()=>captions.updateCues.call(this)))})),(r&&this.language!==i||!e.includes(n))&&(captions.setLanguage.call(this,i),captions.toggle.call(this,t&&r)),this.elements&&toggleClass(this.elements.container,this.config.classNames.captions.enabled,!is.empty(e)),is.array(this.config.controls)&&this.config.controls.includes(\"settings\")&&this.config.settings.includes(\"captions\")&&controls.setCaptionsMenu.call(this)},toggle(e,t=!0){if(!this.supported.ui)return;const{toggled:i}=this.captions,s=this.config.classNames.captions.active,n=is.nullOrUndefined(e)?!i:e;if(n!==i){if(t||(this.captions.active=n,this.storage.set({captions:n})),!this.language&&n&&!t){const e=captions.getTracks.call(this),t=captions.findTrack.call(this,[this.captions.language,...this.captions.languages],!0);return this.captions.language=t.language,void captions.set.call(this,e.indexOf(t))}this.elements.buttons.captions&&(this.elements.buttons.captions.pressed=n),toggleClass(this.elements.container,s,n),this.captions.toggled=n,controls.updateSetting.call(this,\"captions\"),triggerEvent.call(this,this.media,n?\"captionsenabled\":\"captionsdisabled\")}setTimeout((()=>{n&&this.captions.toggled&&(this.captions.currentTrackNode.mode=\"hidden\")}))},set(e,t=!0){const i=captions.getTracks.call(this);if(-1!==e)if(is.number(e))if(e in i){if(this.captions.currentTrack!==e){this.captions.currentTrack=e;const s=i[e],{language:n}=s||{};this.captions.currentTrackNode=s,controls.updateSetting.call(this,\"captions\"),t||(this.captions.language=n,this.storage.set({language:n})),this.isVimeo&&this.embed.enableTextTrack(n),triggerEvent.call(this,this.media,\"languagechange\")}captions.toggle.call(this,!0,t),this.isHTML5&&this.isVideo&&captions.updateCues.call(this)}else this.debug.warn(\"Track not found\",e);else this.debug.warn(\"Invalid caption argument\",e);else captions.toggle.call(this,!1,t)},setLanguage(e,t=!0){if(!is.string(e))return void this.debug.warn(\"Invalid language argument\",e);const i=e.toLowerCase();this.captions.language=i;const s=captions.getTracks.call(this),n=captions.findTrack.call(this,[i]);captions.set.call(this,s.indexOf(n),t)},getTracks(e=!1){return Array.from((this.media||{}).textTracks||[]).filter((t=>!this.isHTML5||e||this.captions.meta.has(t))).filter((e=>[\"captions\",\"subtitles\"].includes(e.kind)))},findTrack(e,t=!1){const i=captions.getTracks.call(this),s=e=>Number((this.captions.meta.get(e)||{}).default),n=Array.from(i).sort(((e,t)=>s(t)-s(e)));let r;return e.every((e=>(r=n.find((t=>t.language===e)),!r))),r||(t?n[0]:void 0)},getCurrentTrack(){return captions.getTracks.call(this)[this.currentTrack]},getLabel(e){let t=e;return!is.track(t)&&support.textTracks&&this.captions.toggled&&(t=captions.getCurrentTrack.call(this)),is.track(t)?is.empty(t.label)?is.empty(t.language)?i18n.get(\"enabled\",this.config):e.language.toUpperCase():t.label:i18n.get(\"disabled\",this.config)},updateCues(e){if(!this.supported.ui)return;if(!is.element(this.elements.captions))return void this.debug.warn(\"No captions element to render to\");if(!is.nullOrUndefined(e)&&!Array.isArray(e))return void this.debug.warn(\"updateCues: Invalid input\",e);let t=e;if(!t){const e=captions.getCurrentTrack.call(this);t=Array.from((e||{}).activeCues||[]).map((e=>e.getCueAsHTML())).map(getHTML)}const i=t.map((e=>e.trim())).join(\"\\n\");if(i!==this.elements.captions.innerHTML){emptyElement(this.elements.captions);const e=createElement(\"span\",getAttributesFromSelector(this.config.selectors.caption));e.innerHTML=i,this.elements.captions.appendChild(e),triggerEvent.call(this,this.media,\"cuechange\")}}},defaults={enabled:!0,title:\"\",debug:!1,autoplay:!1,autopause:!0,playsinline:!0,seekTime:10,volume:1,muted:!1,duration:null,displayDuration:!0,invertTime:!0,toggleInvert:!0,ratio:null,clickToPlay:!0,hideControls:!0,resetOnEnd:!1,disableContextMenu:!0,loadSprite:!0,iconPrefix:\"plyr\",iconUrl:\"https://cdn.plyr.io/3.7.8/plyr.svg\",blankVideo:\"https://cdn.plyr.io/static/blank.mp4\",quality:{default:576,options:[4320,2880,2160,1440,1080,720,576,480,360,240],forced:!1,onChange:null},loop:{active:!1},speed:{selected:1,options:[.5,.75,1,1.25,1.5,1.75,2,4]},keyboard:{focused:!0,global:!1},tooltips:{controls:!1,seek:!0},captions:{active:!1,language:\"auto\",update:!1},fullscreen:{enabled:!0,fallback:!0,iosNative:!1},storage:{enabled:!0,key:\"plyr\"},controls:[\"play-large\",\"play\",\"progress\",\"current-time\",\"mute\",\"volume\",\"captions\",\"settings\",\"pip\",\"airplay\",\"fullscreen\"],settings:[\"captions\",\"quality\",\"speed\"],i18n:{restart:\"Restart\",rewind:\"Rewind {seektime}s\",play:\"Play\",pause:\"Pause\",fastForward:\"Forward {seektime}s\",seek:\"Seek\",seekLabel:\"{currentTime} of {duration}\",played:\"Played\",buffered:\"Buffered\",currentTime:\"Current time\",duration:\"Duration\",volume:\"Volume\",mute:\"Mute\",unmute:\"Unmute\",enableCaptions:\"Enable captions\",disableCaptions:\"Disable captions\",download:\"Download\",enterFullscreen:\"Enter fullscreen\",exitFullscreen:\"Exit fullscreen\",frameTitle:\"Player for {title}\",captions:\"Captions\",settings:\"Settings\",pip:\"PIP\",menuBack:\"Go back to previous menu\",speed:\"Speed\",normal:\"Normal\",quality:\"Quality\",loop:\"Loop\",start:\"Start\",end:\"End\",all:\"All\",reset:\"Reset\",disabled:\"Disabled\",enabled:\"Enabled\",advertisement:\"Ad\",qualityBadge:{2160:\"4K\",1440:\"HD\",1080:\"HD\",720:\"HD\",576:\"SD\",480:\"SD\"}},urls:{download:null,vimeo:{sdk:\"https://player.vimeo.com/api/player.js\",iframe:\"https://player.vimeo.com/video/{0}?{1}\",api:\"https://vimeo.com/api/oembed.json?url={0}\"},youtube:{sdk:\"https://www.youtube.com/iframe_api\",api:\"https://noembed.com/embed?url=https://www.youtube.com/watch?v={0}\"},googleIMA:{sdk:\"https://imasdk.googleapis.com/js/sdkloader/ima3.js\"}},listeners:{seek:null,play:null,pause:null,restart:null,rewind:null,fastForward:null,mute:null,volume:null,captions:null,download:null,fullscreen:null,pip:null,airplay:null,speed:null,quality:null,loop:null,language:null},events:[\"ended\",\"progress\",\"stalled\",\"playing\",\"waiting\",\"canplay\",\"canplaythrough\",\"loadstart\",\"loadeddata\",\"loadedmetadata\",\"timeupdate\",\"volumechange\",\"play\",\"pause\",\"error\",\"seeking\",\"seeked\",\"emptied\",\"ratechange\",\"cuechange\",\"download\",\"enterfullscreen\",\"exitfullscreen\",\"captionsenabled\",\"captionsdisabled\",\"languagechange\",\"controlshidden\",\"controlsshown\",\"ready\",\"statechange\",\"qualitychange\",\"adsloaded\",\"adscontentpause\",\"adscontentresume\",\"adstarted\",\"adsmidpoint\",\"adscomplete\",\"adsallcomplete\",\"adsimpression\",\"adsclick\"],selectors:{editable:\"input, textarea, select, [contenteditable]\",container:\".plyr\",controls:{container:null,wrapper:\".plyr__controls\"},labels:\"[data-plyr]\",buttons:{play:'[data-plyr=\"play\"]',pause:'[data-plyr=\"pause\"]',restart:'[data-plyr=\"restart\"]',rewind:'[data-plyr=\"rewind\"]',fastForward:'[data-plyr=\"fast-forward\"]',mute:'[data-plyr=\"mute\"]',captions:'[data-plyr=\"captions\"]',download:'[data-plyr=\"download\"]',fullscreen:'[data-plyr=\"fullscreen\"]',pip:'[data-plyr=\"pip\"]',airplay:'[data-plyr=\"airplay\"]',settings:'[data-plyr=\"settings\"]',loop:'[data-plyr=\"loop\"]'},inputs:{seek:'[data-plyr=\"seek\"]',volume:'[data-plyr=\"volume\"]',speed:'[data-plyr=\"speed\"]',language:'[data-plyr=\"language\"]',quality:'[data-plyr=\"quality\"]'},display:{currentTime:\".plyr__time--current\",duration:\".plyr__time--duration\",buffer:\".plyr__progress__buffer\",loop:\".plyr__progress__loop\",volume:\".plyr__volume--display\"},progress:\".plyr__progress\",captions:\".plyr__captions\",caption:\".plyr__caption\"},classNames:{type:\"plyr--{0}\",provider:\"plyr--{0}\",video:\"plyr__video-wrapper\",embed:\"plyr__video-embed\",videoFixedRatio:\"plyr__video-wrapper--fixed-ratio\",embedContainer:\"plyr__video-embed__container\",poster:\"plyr__poster\",posterEnabled:\"plyr__poster-enabled\",ads:\"plyr__ads\",control:\"plyr__control\",controlPressed:\"plyr__control--pressed\",playing:\"plyr--playing\",paused:\"plyr--paused\",stopped:\"plyr--stopped\",loading:\"plyr--loading\",hover:\"plyr--hover\",tooltip:\"plyr__tooltip\",cues:\"plyr__cues\",marker:\"plyr__progress__marker\",hidden:\"plyr__sr-only\",hideControls:\"plyr--hide-controls\",isTouch:\"plyr--is-touch\",uiSupported:\"plyr--full-ui\",noTransition:\"plyr--no-transition\",display:{time:\"plyr__time\"},menu:{value:\"plyr__menu__value\",badge:\"plyr__badge\",open:\"plyr--menu-open\"},captions:{enabled:\"plyr--captions-enabled\",active:\"plyr--captions-active\"},fullscreen:{enabled:\"plyr--fullscreen-enabled\",fallback:\"plyr--fullscreen-fallback\"},pip:{supported:\"plyr--pip-supported\",active:\"plyr--pip-active\"},airplay:{supported:\"plyr--airplay-supported\",active:\"plyr--airplay-active\"},previewThumbnails:{thumbContainer:\"plyr__preview-thumb\",thumbContainerShown:\"plyr__preview-thumb--is-shown\",imageContainer:\"plyr__preview-thumb__image-container\",timeContainer:\"plyr__preview-thumb__time-container\",scrubbingContainer:\"plyr__preview-scrubbing\",scrubbingContainerShown:\"plyr__preview-scrubbing--is-shown\"}},attributes:{embed:{provider:\"data-plyr-provider\",id:\"data-plyr-embed-id\",hash:\"data-plyr-embed-hash\"}},ads:{enabled:!1,publisherId:\"\",tagUrl:\"\"},previewThumbnails:{enabled:!1,src:\"\"},vimeo:{byline:!1,portrait:!1,title:!1,speed:!0,transparent:!1,customControls:!0,referrerPolicy:null,premium:!1},youtube:{rel:0,showinfo:0,iv_load_policy:3,modestbranding:1,customControls:!0,noCookie:!1},mediaMetadata:{title:\"\",artist:\"\",album:\"\",artwork:[]},markers:{enabled:!1,points:[]}},pip={active:\"picture-in-picture\",inactive:\"inline\"},providers={html5:\"html5\",youtube:\"youtube\",vimeo:\"vimeo\"},types={audio:\"audio\",video:\"video\"};function getProviderByUrl(e){return/^(https?:\\/\\/)?(www\\.)?(youtube\\.com|youtube-nocookie\\.com|youtu\\.?be)\\/.+$/.test(e)?providers.youtube:/^https?:\\/\\/player.vimeo.com\\/video\\/\\d{0,9}(?=\\b|\\/)/.test(e)?providers.vimeo:null}const noop=()=>{};class Console{constructor(e=!1){this.enabled=window.console&&e,this.enabled&&this.log(\"Debugging enabled\")}get log(){return this.enabled?Function.prototype.bind.call(console.log,console):noop}get warn(){return this.enabled?Function.prototype.bind.call(console.warn,console):noop}get error(){return this.enabled?Function.prototype.bind.call(console.error,console):noop}}class Fullscreen{constructor(e){_defineProperty$1(this,\"onChange\",(()=>{if(!this.supported)return;const e=this.player.elements.buttons.fullscreen;is.element(e)&&(e.pressed=this.active);const t=this.target===this.player.media?this.target:this.player.elements.container;triggerEvent.call(this.player,t,this.active?\"enterfullscreen\":\"exitfullscreen\",!0)})),_defineProperty$1(this,\"toggleFallback\",((e=!1)=>{if(e?this.scrollPosition={x:window.scrollX??0,y:window.scrollY??0}:window.scrollTo(this.scrollPosition.x,this.scrollPosition.y),document.body.style.overflow=e?\"hidden\":\"\",toggleClass(this.target,this.player.config.classNames.fullscreen.fallback,e),browser.isIos){let t=document.head.querySelector('meta[name=\"viewport\"]');const i=\"viewport-fit=cover\";t||(t=document.createElement(\"meta\"),t.setAttribute(\"name\",\"viewport\"));const s=is.string(t.content)&&t.content.includes(i);e?(this.cleanupViewport=!s,s||(t.content+=`,${i}`)):this.cleanupViewport&&(t.content=t.content.split(\",\").filter((e=>e.trim()!==i)).join(\",\"))}this.onChange()})),_defineProperty$1(this,\"trapFocus\",(e=>{if(browser.isIos||browser.isIPadOS||!this.active||\"Tab\"!==e.key)return;const t=document.activeElement,i=getElements.call(this.player,\"a[href], button:not(:disabled), input:not(:disabled), [tabindex]\"),[s]=i,n=i[i.length-1];t!==n||e.shiftKey?t===s&&e.shiftKey&&(n.focus(),e.preventDefault()):(s.focus(),e.preventDefault())})),_defineProperty$1(this,\"update\",(()=>{if(this.supported){let e;e=this.forceFallback?\"Fallback (forced)\":Fullscreen.nativeSupported?\"Native\":\"Fallback\",this.player.debug.log(`${e} fullscreen enabled`)}else this.player.debug.log(\"Fullscreen not supported and fallback disabled\");toggleClass(this.player.elements.container,this.player.config.classNames.fullscreen.enabled,this.supported)})),_defineProperty$1(this,\"enter\",(()=>{this.supported&&(browser.isIos&&this.player.config.fullscreen.iosNative?this.player.isVimeo?this.player.embed.requestFullscreen():this.target.webkitEnterFullscreen():!Fullscreen.nativeSupported||this.forceFallback?this.toggleFallback(!0):this.prefix?is.empty(this.prefix)||this.target[`${this.prefix}Request${this.property}`]():this.target.requestFullscreen({navigationUI:\"hide\"}))})),_defineProperty$1(this,\"exit\",(()=>{if(this.supported)if(browser.isIos&&this.player.config.fullscreen.iosNative)this.player.isVimeo?this.player.embed.exitFullscreen():this.target.webkitEnterFullscreen(),silencePromise(this.player.play());else if(!Fullscreen.nativeSupported||this.forceFallback)this.toggleFallback(!1);else if(this.prefix){if(!is.empty(this.prefix)){const e=\"moz\"===this.prefix?\"Cancel\":\"Exit\";document[`${this.prefix}${e}${this.property}`]()}}else(document.cancelFullScreen||document.exitFullscreen).call(document)})),_defineProperty$1(this,\"toggle\",(()=>{this.active?this.exit():this.enter()})),this.player=e,this.prefix=Fullscreen.prefix,this.property=Fullscreen.property,this.scrollPosition={x:0,y:0},this.forceFallback=\"force\"===e.config.fullscreen.fallback,this.player.elements.fullscreen=e.config.fullscreen.container&&closest$1(this.player.elements.container,e.config.fullscreen.container),on.call(this.player,document,\"ms\"===this.prefix?\"MSFullscreenChange\":`${this.prefix}fullscreenchange`,(()=>{this.onChange()})),on.call(this.player,this.player.elements.container,\"dblclick\",(e=>{is.element(this.player.elements.controls)&&this.player.elements.controls.contains(e.target)||this.player.listeners.proxy(e,this.toggle,\"fullscreen\")})),on.call(this,this.player.elements.container,\"keydown\",(e=>this.trapFocus(e))),this.update()}static get nativeSupported(){return!!(document.fullscreenEnabled||document.webkitFullscreenEnabled||document.mozFullScreenEnabled||document.msFullscreenEnabled)}get useNative(){return Fullscreen.nativeSupported&&!this.forceFallback}static get prefix(){if(is.function(document.exitFullscreen))return\"\";let e=\"\";return[\"webkit\",\"moz\",\"ms\"].some((t=>!(!is.function(document[`${t}ExitFullscreen`])&&!is.function(document[`${t}CancelFullScreen`]))&&(e=t,!0))),e}static get property(){return\"moz\"===this.prefix?\"FullScreen\":\"Fullscreen\"}get supported(){return[this.player.config.fullscreen.enabled,this.player.isVideo,Fullscreen.nativeSupported||this.player.config.fullscreen.fallback,!this.player.isYouTube||Fullscreen.nativeSupported||!browser.isIos||this.player.config.playsinline&&!this.player.config.fullscreen.iosNative].every(Boolean)}get active(){if(!this.supported)return!1;if(!Fullscreen.nativeSupported||this.forceFallback)return hasClass(this.target,this.player.config.classNames.fullscreen.fallback);const e=this.prefix?this.target.getRootNode()[`${this.prefix}${this.property}Element`]:this.target.getRootNode().fullscreenElement;return e&&e.shadowRoot?e===this.target.getRootNode().host:e===this.target}get target(){return browser.isIos&&this.player.config.fullscreen.iosNative?this.player.media:this.player.elements.fullscreen??this.player.elements.container}}function loadImage(e,t=1){return new Promise(((i,s)=>{const n=new Image,r=()=>{delete n.onload,delete n.onerror,(n.naturalWidth>=t?i:s)(n)};Object.assign(n,{onload:r,onerror:r,src:e})}))}const ui={addStyleHook(){toggleClass(this.elements.container,this.config.selectors.container.replace(\".\",\"\"),!0),toggleClass(this.elements.container,this.config.classNames.uiSupported,this.supported.ui)},toggleNativeControls(e=!1){e&&this.isHTML5?this.media.setAttribute(\"controls\",\"\"):this.media.removeAttribute(\"controls\")},build(){if(this.listeners.media(),!this.supported.ui)return this.debug.warn(`Basic support only for ${this.provider} ${this.type}`),void ui.toggleNativeControls.call(this,!0);is.element(this.elements.controls)||(controls.inject.call(this),this.listeners.controls()),ui.toggleNativeControls.call(this),this.isHTML5&&captions.setup.call(this),this.volume=null,this.muted=null,this.loop=null,this.quality=null,this.speed=null,controls.updateVolume.call(this),controls.timeUpdate.call(this),controls.durationUpdate.call(this),ui.checkPlaying.call(this),toggleClass(this.elements.container,this.config.classNames.pip.supported,support.pip&&this.isHTML5&&this.isVideo),toggleClass(this.elements.container,this.config.classNames.airplay.supported,support.airplay&&this.isHTML5),toggleClass(this.elements.container,this.config.classNames.isTouch,this.touch),this.ready=!0,setTimeout((()=>{triggerEvent.call(this,this.media,\"ready\")}),0),ui.setTitle.call(this),this.poster&&ui.setPoster.call(this,this.poster,!1).catch((()=>{})),this.config.duration&&controls.durationUpdate.call(this),this.config.mediaMetadata&&controls.setMediaMetadata.call(this)},setTitle(){let e=i18n.get(\"play\",this.config);if(is.string(this.config.title)&&!is.empty(this.config.title)&&(e+=`, ${this.config.title}`),Array.from(this.elements.buttons.play||[]).forEach((t=>{t.setAttribute(\"aria-label\",e)})),this.isEmbed){const e=getElement.call(this,\"iframe\");if(!is.element(e))return;const t=is.empty(this.config.title)?\"video\":this.config.title,i=i18n.get(\"frameTitle\",this.config);e.setAttribute(\"title\",i.replace(\"{title}\",t))}},togglePoster(e){toggleClass(this.elements.container,this.config.classNames.posterEnabled,e)},setPoster(e,t=!0){return t&&this.poster?Promise.reject(new Error(\"Poster already set\")):(this.media.setAttribute(\"data-poster\",e),this.elements.poster.removeAttribute(\"hidden\"),ready.call(this).then((()=>loadImage(e))).catch((t=>{throw e===this.poster&&ui.togglePoster.call(this,!1),t})).then((()=>{if(e!==this.poster)throw new Error(\"setPoster cancelled by later call to setPoster\")})).then((()=>(Object.assign(this.elements.poster.style,{backgroundImage:`url('${e}')`,backgroundSize:\"\"}),ui.togglePoster.call(this,!0),e))))},checkPlaying(e){toggleClass(this.elements.container,this.config.classNames.playing,this.playing),toggleClass(this.elements.container,this.config.classNames.paused,this.paused),toggleClass(this.elements.container,this.config.classNames.stopped,this.stopped),Array.from(this.elements.buttons.play||[]).forEach((e=>{Object.assign(e,{pressed:this.playing}),e.setAttribute(\"aria-label\",i18n.get(this.playing?\"pause\":\"play\",this.config))})),is.event(e)&&\"timeupdate\"===e.type||ui.toggleControls.call(this)},checkLoading(e){this.loading=[\"stalled\",\"waiting\"].includes(e.type),clearTimeout(this.timers.loading),this.timers.loading=setTimeout((()=>{toggleClass(this.elements.container,this.config.classNames.loading,this.loading),ui.toggleControls.call(this)}),this.loading?250:0)},toggleControls(e){const{controls:t}=this.elements;if(t&&this.config.hideControls){const i=this.touch&&this.lastSeekTime+2e3>Date.now();this.toggleControls(Boolean(e||this.loading||this.paused||t.pressed||t.hover||i))}},migrateStyles(){Object.values({...this.media.style}).filter((e=>!is.empty(e)&&is.string(e)&&e.startsWith(\"--plyr\"))).forEach((e=>{this.elements.container.style.setProperty(e,this.media.style.getPropertyValue(e)),this.media.style.removeProperty(e)})),is.empty(this.media.style)&&this.media.removeAttribute(\"style\")}};class Listeners{constructor(e){_defineProperty$1(this,\"firstTouch\",(()=>{const{player:e}=this,{elements:t}=e;e.touch=!0,toggleClass(t.container,e.config.classNames.isTouch,!0)})),_defineProperty$1(this,\"global\",((e=!0)=>{const{player:t}=this;t.config.keyboard.global&&toggleListener.call(t,window,\"keydown keyup\",this.handleKey,e,!1),toggleListener.call(t,document.body,\"click\",this.toggleMenu,e),once.call(t,document.body,\"touchstart\",this.firstTouch)})),_defineProperty$1(this,\"container\",(()=>{const{player:e}=this,{config:t,elements:i,timers:s}=e;!t.keyboard.global&&t.keyboard.focused&&on.call(e,i.container,\"keydown keyup\",this.handleKey,!1),on.call(e,i.container,\"mousemove mouseleave touchstart touchmove enterfullscreen exitfullscreen\",(t=>{const{controls:n}=i;n&&\"enterfullscreen\"===t.type&&(n.pressed=!1,n.hover=!1);let r=0;[\"touchstart\",\"touchmove\",\"mousemove\"].includes(t.type)&&(ui.toggleControls.call(e,!0),r=e.touch?3e3:2e3),clearTimeout(s.controls),s.controls=setTimeout((()=>ui.toggleControls.call(e,!1)),r)}));const n=()=>{if(!e.isVimeo||e.config.vimeo.premium)return;const t=i.wrapper,{active:s}=e.fullscreen,[n,r]=getAspectRatio.call(e),a=supportsCSS(`aspect-ratio: ${n} / ${r}`);if(!s)return void(a?(t.style.width=null,t.style.height=null):(t.style.maxWidth=null,t.style.margin=null));const[o,l]=getViewportSize(),c=o/l>n/r;a?(t.style.width=c?\"auto\":\"100%\",t.style.height=c?\"100%\":\"auto\"):(t.style.maxWidth=c?l/r*n+\"px\":null,t.style.margin=c?\"0 auto\":null)},r=()=>{clearTimeout(s.resized),s.resized=setTimeout(n,50)};on.call(e,i.container,\"enterfullscreen exitfullscreen\",(t=>{const{target:s}=e.fullscreen;if(s!==i.container)return;if(!e.isEmbed&&is.empty(e.config.ratio))return;n();(\"enterfullscreen\"===t.type?on:off).call(e,window,\"resize\",r)}))})),_defineProperty$1(this,\"media\",(()=>{const{player:e}=this,{elements:t}=e;if(on.call(e,e.media,\"timeupdate seeking seeked\",(t=>controls.timeUpdate.call(e,t))),on.call(e,e.media,\"durationchange loadeddata loadedmetadata\",(t=>controls.durationUpdate.call(e,t))),on.call(e,e.media,\"ended\",(()=>{e.isHTML5&&e.isVideo&&e.config.resetOnEnd&&(e.restart(),e.pause())})),on.call(e,e.media,\"progress playing seeking seeked\",(t=>controls.updateProgress.call(e,t))),on.call(e,e.media,\"volumechange\",(t=>controls.updateVolume.call(e,t))),on.call(e,e.media,\"playing play pause ended emptied timeupdate\",(t=>ui.checkPlaying.call(e,t))),on.call(e,e.media,\"waiting canplay seeked playing\",(t=>ui.checkLoading.call(e,t))),e.supported.ui&&e.config.clickToPlay&&!e.isAudio){const i=getElement.call(e,`.${e.config.classNames.video}`);if(!is.element(i))return;on.call(e,t.container,\"click\",(s=>{([t.container,i].includes(s.target)||i.contains(s.target))&&(e.touch&&e.config.hideControls||(e.ended?(this.proxy(s,e.restart,\"restart\"),this.proxy(s,(()=>{silencePromise(e.play())}),\"play\")):this.proxy(s,(()=>{silencePromise(e.togglePlay())}),\"play\")))}))}e.supported.ui&&e.config.disableContextMenu&&on.call(e,t.wrapper,\"contextmenu\",(e=>{e.preventDefault()}),!1),on.call(e,e.media,\"volumechange\",(()=>{e.storage.set({volume:e.volume,muted:e.muted})})),on.call(e,e.media,\"ratechange\",(()=>{controls.updateSetting.call(e,\"speed\"),e.storage.set({speed:e.speed})})),on.call(e,e.media,\"qualitychange\",(t=>{controls.updateSetting.call(e,\"quality\",null,t.detail.quality)})),on.call(e,e.media,\"ready qualitychange\",(()=>{controls.setDownloadUrl.call(e)}));const i=e.config.events.concat([\"keyup\",\"keydown\"]).join(\" \");on.call(e,e.media,i,(i=>{let{detail:s={}}=i;\"error\"===i.type&&(s=e.media.error),triggerEvent.call(e,t.container,i.type,!0,s)}))})),_defineProperty$1(this,\"proxy\",((e,t,i)=>{const{player:s}=this,n=s.config.listeners[i];let r=!0;is.function(n)&&(r=n.call(s,e)),!1!==r&&is.function(t)&&t.call(s,e)})),_defineProperty$1(this,\"bind\",((e,t,i,s,n=!0)=>{const{player:r}=this,a=r.config.listeners[s],o=is.function(a);on.call(r,e,t,(e=>this.proxy(e,i,s)),n&&!o)})),_defineProperty$1(this,\"controls\",(()=>{const{player:e}=this,{elements:t}=e,i=browser.isIE?\"change\":\"input\";if(t.buttons.play&&Array.from(t.buttons.play).forEach((t=>{this.bind(t,\"click\",(()=>{silencePromise(e.togglePlay())}),\"play\")})),this.bind(t.buttons.restart,\"click\",e.restart,\"restart\"),this.bind(t.buttons.rewind,\"click\",(()=>{e.lastSeekTime=Date.now(),e.rewind()}),\"rewind\"),this.bind(t.buttons.fastForward,\"click\",(()=>{e.lastSeekTime=Date.now(),e.forward()}),\"fastForward\"),this.bind(t.buttons.mute,\"click\",(()=>{e.muted=!e.muted}),\"mute\"),this.bind(t.buttons.captions,\"click\",(()=>e.toggleCaptions())),this.bind(t.buttons.download,\"click\",(()=>{triggerEvent.call(e,e.media,\"download\")}),\"download\"),this.bind(t.buttons.fullscreen,\"click\",(()=>{e.fullscreen.toggle()}),\"fullscreen\"),this.bind(t.buttons.pip,\"click\",(()=>{e.pip=\"toggle\"}),\"pip\"),this.bind(t.buttons.airplay,\"click\",e.airplay,\"airplay\"),this.bind(t.buttons.settings,\"click\",(t=>{t.stopPropagation(),t.preventDefault(),controls.toggleMenu.call(e,t)}),null,!1),this.bind(t.buttons.settings,\"keyup\",(t=>{[\" \",\"Enter\"].includes(t.key)&&(\"Enter\"!==t.key?(t.preventDefault(),t.stopPropagation(),controls.toggleMenu.call(e,t)):controls.focusFirstMenuItem.call(e,null,!0))}),null,!1),this.bind(t.settings.menu,\"keydown\",(t=>{\"Escape\"===t.key&&controls.toggleMenu.call(e,t)})),this.bind(t.inputs.seek,\"mousedown mousemove\",(e=>{const i=t.progress.getBoundingClientRect(),s=100/i.width*(e.pageX-i.left);e.currentTarget.setAttribute(\"seek-value\",s)})),this.bind(t.inputs.seek,\"mousedown mouseup keydown keyup touchstart touchend\",(t=>{const i=t.currentTarget,s=\"play-on-seeked\";if(is.keyboardEvent(t)&&![\"ArrowLeft\",\"ArrowRight\"].includes(t.key))return;e.lastSeekTime=Date.now();const n=i.hasAttribute(s),r=[\"mouseup\",\"touchend\",\"keyup\"].includes(t.type);n&&r?(i.removeAttribute(s),silencePromise(e.play())):!r&&e.playing&&(i.setAttribute(s,\"\"),e.pause())})),browser.isIos){const t=getElements.call(e,'input[type=\"range\"]');Array.from(t).forEach((e=>this.bind(e,i,(e=>repaint(e.target)))))}this.bind(t.inputs.seek,i,(t=>{const i=t.currentTarget;let s=i.getAttribute(\"seek-value\");is.empty(s)&&(s=i.value),i.removeAttribute(\"seek-value\"),e.currentTime=s/i.max*e.duration}),\"seek\"),this.bind(t.progress,\"mouseenter mouseleave mousemove\",(t=>controls.updateSeekTooltip.call(e,t))),this.bind(t.progress,\"mousemove touchmove\",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.startMove(t)})),this.bind(t.progress,\"mouseleave touchend click\",(()=>{const{previewThumbnails:t}=e;t&&t.loaded&&t.endMove(!1,!0)})),this.bind(t.progress,\"mousedown touchstart\",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.startScrubbing(t)})),this.bind(t.progress,\"mouseup touchend\",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.endScrubbing(t)})),browser.isWebKit&&Array.from(getElements.call(e,'input[type=\"range\"]')).forEach((t=>{this.bind(t,\"input\",(t=>controls.updateRangeFill.call(e,t.target)))})),e.config.toggleInvert&&!is.element(t.display.duration)&&this.bind(t.display.currentTime,\"click\",(()=>{0!==e.currentTime&&(e.config.invertTime=!e.config.invertTime,controls.timeUpdate.call(e))})),this.bind(t.inputs.volume,i,(t=>{e.volume=t.target.value}),\"volume\"),this.bind(t.controls,\"mouseenter mouseleave\",(i=>{t.controls.hover=!e.touch&&\"mouseenter\"===i.type})),t.fullscreen&&Array.from(t.fullscreen.children).filter((e=>!e.contains(t.container))).forEach((i=>{this.bind(i,\"mouseenter mouseleave\",(i=>{t.controls&&(t.controls.hover=!e.touch&&\"mouseenter\"===i.type)}))})),this.bind(t.controls,\"mousedown mouseup touchstart touchend touchcancel\",(e=>{t.controls.pressed=[\"mousedown\",\"touchstart\"].includes(e.type)})),this.bind(t.controls,\"focusin\",(()=>{const{config:i,timers:s}=e;toggleClass(t.controls,i.classNames.noTransition,!0),ui.toggleControls.call(e,!0),setTimeout((()=>{toggleClass(t.controls,i.classNames.noTransition,!1)}),0);const n=this.touch?3e3:4e3;clearTimeout(s.controls),s.controls=setTimeout((()=>ui.toggleControls.call(e,!1)),n)})),this.bind(t.inputs.volume,\"wheel\",(t=>{const i=t.webkitDirectionInvertedFromDevice,[s,n]=[t.deltaX,-t.deltaY].map((e=>i?-e:e)),r=Math.sign(Math.abs(s)>Math.abs(n)?s:n);e.increaseVolume(r/50);const{volume:a}=e.media;(1===r&&a<1||-1===r&&a>0)&&t.preventDefault()}),\"volume\",!1)})),this.player=e,this.lastKey=null,this.focusTimer=null,this.lastKeyDown=null,this.handleKey=this.handleKey.bind(this),this.toggleMenu=this.toggleMenu.bind(this),this.firstTouch=this.firstTouch.bind(this)}handleKey(e){const{player:t}=this,{elements:i}=t,{key:s,type:n,altKey:r,ctrlKey:a,metaKey:o,shiftKey:l}=e,c=\"keydown\"===n,u=c&&s===this.lastKey;if(r||a||o||l)return;if(!s)return;if(c){const n=document.activeElement;if(is.element(n)){const{editable:s}=t.config.selectors,{seek:r}=i.inputs;if(n!==r&&matches(n,s))return;if(\" \"===e.key&&matches(n,'button, [role^=\"menuitem\"]'))return}switch([\" \",\"ArrowLeft\",\"ArrowUp\",\"ArrowRight\",\"ArrowDown\",\"0\",\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"c\",\"f\",\"k\",\"l\",\"m\"].includes(s)&&(e.preventDefault(),e.stopPropagation()),s){case\"0\":case\"1\":case\"2\":case\"3\":case\"4\":case\"5\":case\"6\":case\"7\":case\"8\":case\"9\":u||(d=parseInt(s,10),t.currentTime=t.duration/10*d);break;case\" \":case\"k\":u||silencePromise(t.togglePlay());break;case\"ArrowUp\":t.increaseVolume(.1);break;case\"ArrowDown\":t.decreaseVolume(.1);break;case\"m\":u||(t.muted=!t.muted);break;case\"ArrowRight\":t.forward();break;case\"ArrowLeft\":t.rewind();break;case\"f\":t.fullscreen.toggle();break;case\"c\":u||t.toggleCaptions();break;case\"l\":t.loop=!t.loop}\"Escape\"===s&&!t.fullscreen.usingNative&&t.fullscreen.active&&t.fullscreen.toggle(),this.lastKey=s}else this.lastKey=null;var d}toggleMenu(e){controls.toggleMenu.call(this.player,e)}}var commonjsGlobal=\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof window?window:\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:{};function createCommonjsModule(e,t){return e(t={exports:{}},t.exports),t.exports}var loadjs_umd=createCommonjsModule((function(e,t){e.exports=function(){var e=function(){},t={},i={},s={};function n(e,t){e=e.push?e:[e];var n,r,a,o=[],l=e.length,c=l;for(n=function(e,i){i.length&&o.push(e),--c||t(o)};l--;)r=e[l],(a=i[r])?n(r,a):(s[r]=s[r]||[]).push(n)}function r(e,t){if(e){var n=s[e];if(i[e]=t,n)for(;n.length;)n[0](e,t),n.splice(0,1)}}function a(t,i){t.call&&(t={success:t}),i.length?(t.error||e)(i):(t.success||e)(t)}function o(t,i,s,n){var r,a,l=document,c=s.async,u=(s.numRetries||0)+1,d=s.before||e,h=t.replace(/[\\?|#].*$/,\"\"),m=t.replace(/^(css|img)!/,\"\");n=n||0,/(^css!|\\.css$)/.test(h)?((a=l.createElement(\"link\")).rel=\"stylesheet\",a.href=m,(r=\"hideFocus\"in a)&&a.relList&&(r=0,a.rel=\"preload\",a.as=\"style\")):/(^img!|\\.(png|gif|jpg|svg|webp)$)/.test(h)?(a=l.createElement(\"img\")).src=m:((a=l.createElement(\"script\")).src=t,a.async=void 0===c||c),a.onload=a.onerror=a.onbeforeload=function(e){var l=e.type[0];if(r)try{a.sheet.cssText.length||(l=\"e\")}catch(e){18!=e.code&&(l=\"e\")}if(\"e\"==l){if((n+=1)<u)return o(t,i,s,n)}else if(\"preload\"==a.rel&&\"style\"==a.as)return a.rel=\"stylesheet\";i(t,l,e.defaultPrevented)},!1!==d(t,a)&&l.head.appendChild(a)}function l(e,t,i){var s,n,r=(e=e.push?e:[e]).length,a=r,l=[];for(s=function(e,i,s){if(\"e\"==i&&l.push(e),\"b\"==i){if(!s)return;l.push(e)}--r||t(l)},n=0;n<a;n++)o(e[n],s,i)}function c(e,i,s){var n,o;if(i&&i.trim&&(n=i),o=(n?s:i)||{},n){if(n in t)throw\"LoadJS\";t[n]=!0}function c(t,i){l(e,(function(e){a(o,e),t&&a({success:t,error:i},e),r(n,e)}),o)}if(o.returnPromise)return new Promise(c);c()}return c.ready=function(e,t){return n(e,(function(e){a(t,e)})),c},c.done=function(e){r(e,[])},c.reset=function(){t={},i={},s={}},c.isDefined=function(e){return e in t},c}()}));function loadScript(e){return new Promise(((t,i)=>{loadjs_umd(e,{success:t,error:i})}))}function parseId$1(e){if(is.empty(e))return null;if(is.number(Number(e)))return e;return e.match(/^.*(vimeo.com\\/|video\\/)(\\d+).*/)?RegExp.$2:e}function parseHash(e){const t=e.match(/^.*(vimeo.com\\/|video\\/)(\\d+)(\\?.*&*h=|\\/)+([\\d,a-f]+)/);return t&&5===t.length?t[4]:null}function assurePlaybackState$1(e){e&&!this.embed.hasPlayed&&(this.embed.hasPlayed=!0),this.media.paused===e&&(this.media.paused=!e,triggerEvent.call(this,this.media,e?\"play\":\"pause\"))}const vimeo={setup(){const e=this;toggleClass(e.elements.wrapper,e.config.classNames.embed,!0),e.options.speed=e.config.speed.options,setAspectRatio.call(e),is.object(window.Vimeo)?vimeo.ready.call(e):loadScript(e.config.urls.vimeo.sdk).then((()=>{vimeo.ready.call(e)})).catch((t=>{e.debug.warn(\"Vimeo SDK (player.js) failed to load\",t)}))},ready(){const e=this,t=e.config.vimeo,{premium:i,referrerPolicy:s,...n}=t;let r=e.media.getAttribute(\"src\"),a=\"\";is.empty(r)?(r=e.media.getAttribute(e.config.attributes.embed.id),a=e.media.getAttribute(e.config.attributes.embed.hash)):a=parseHash(r);const o=a?{h:a}:{};i&&Object.assign(n,{controls:!1,sidedock:!1});const l=buildUrlParams({loop:e.config.loop.active,autoplay:e.autoplay,muted:e.muted,gesture:\"media\",playsinline:e.config.playsinline,...o,...n}),c=parseId$1(r),u=createElement(\"iframe\"),d=format(e.config.urls.vimeo.iframe,c,l);if(u.setAttribute(\"src\",d),u.setAttribute(\"allowfullscreen\",\"\"),u.setAttribute(\"allow\",[\"autoplay\",\"fullscreen\",\"picture-in-picture\",\"encrypted-media\",\"accelerometer\",\"gyroscope\"].join(\"; \")),is.empty(s)||u.setAttribute(\"referrerPolicy\",s),i||!t.customControls)u.setAttribute(\"data-poster\",e.poster),e.media=replaceElement(u,e.media);else{const t=createElement(\"div\",{class:e.config.classNames.embedContainer,\"data-poster\":e.poster});t.appendChild(u),e.media=replaceElement(t,e.media)}t.customControls||fetch(format(e.config.urls.vimeo.api,d)).then((t=>{!is.empty(t)&&t.thumbnail_url&&ui.setPoster.call(e,t.thumbnail_url).catch((()=>{}))})),e.embed=new window.Vimeo.Player(u,{autopause:e.config.autopause,muted:e.muted}),e.media.paused=!0,e.media.currentTime=0,e.supported.ui&&e.embed.disableTextTrack(),e.media.play=()=>(assurePlaybackState$1.call(e,!0),e.embed.play()),e.media.pause=()=>(assurePlaybackState$1.call(e,!1),e.embed.pause()),e.media.stop=()=>{e.pause(),e.currentTime=0};let{currentTime:h}=e.media;Object.defineProperty(e.media,\"currentTime\",{get:()=>h,set(t){const{embed:i,media:s,paused:n,volume:r}=e,a=n&&!i.hasPlayed;s.seeking=!0,triggerEvent.call(e,s,\"seeking\"),Promise.resolve(a&&i.setVolume(0)).then((()=>i.setCurrentTime(t))).then((()=>a&&i.pause())).then((()=>a&&i.setVolume(r))).catch((()=>{}))}});let m=e.config.speed.selected;Object.defineProperty(e.media,\"playbackRate\",{get:()=>m,set(t){e.embed.setPlaybackRate(t).then((()=>{m=t,triggerEvent.call(e,e.media,\"ratechange\")})).catch((()=>{e.options.speed=[1]}))}});let{volume:p}=e.config;Object.defineProperty(e.media,\"volume\",{get:()=>p,set(t){e.embed.setVolume(t).then((()=>{p=t,triggerEvent.call(e,e.media,\"volumechange\")}))}});let{muted:g}=e.config;Object.defineProperty(e.media,\"muted\",{get:()=>g,set(t){const i=!!is.boolean(t)&&t;e.embed.setMuted(!!i||e.config.muted).then((()=>{g=i,triggerEvent.call(e,e.media,\"volumechange\")}))}});let f,{loop:y}=e.config;Object.defineProperty(e.media,\"loop\",{get:()=>y,set(t){const i=is.boolean(t)?t:e.config.loop.active;e.embed.setLoop(i).then((()=>{y=i}))}}),e.embed.getVideoUrl().then((t=>{f=t,controls.setDownloadUrl.call(e)})).catch((e=>{this.debug.warn(e)})),Object.defineProperty(e.media,\"currentSrc\",{get:()=>f}),Object.defineProperty(e.media,\"ended\",{get:()=>e.currentTime===e.duration}),Promise.all([e.embed.getVideoWidth(),e.embed.getVideoHeight()]).then((t=>{const[i,s]=t;e.embed.ratio=roundAspectRatio(i,s),setAspectRatio.call(this)})),e.embed.setAutopause(e.config.autopause).then((t=>{e.config.autopause=t})),e.embed.getVideoTitle().then((t=>{e.config.title=t,ui.setTitle.call(this)})),e.embed.getCurrentTime().then((t=>{h=t,triggerEvent.call(e,e.media,\"timeupdate\")})),e.embed.getDuration().then((t=>{e.media.duration=t,triggerEvent.call(e,e.media,\"durationchange\")})),e.embed.getTextTracks().then((t=>{e.media.textTracks=t,captions.setup.call(e)})),e.embed.on(\"cuechange\",(({cues:t=[]})=>{const i=t.map((e=>stripHTML(e.text)));captions.updateCues.call(e,i)})),e.embed.on(\"loaded\",(()=>{if(e.embed.getPaused().then((t=>{assurePlaybackState$1.call(e,!t),t||triggerEvent.call(e,e.media,\"playing\")})),is.element(e.embed.element)&&e.supported.ui){e.embed.element.setAttribute(\"tabindex\",-1)}})),e.embed.on(\"bufferstart\",(()=>{triggerEvent.call(e,e.media,\"waiting\")})),e.embed.on(\"bufferend\",(()=>{triggerEvent.call(e,e.media,\"playing\")})),e.embed.on(\"play\",(()=>{assurePlaybackState$1.call(e,!0),triggerEvent.call(e,e.media,\"playing\")})),e.embed.on(\"pause\",(()=>{assurePlaybackState$1.call(e,!1)})),e.embed.on(\"timeupdate\",(t=>{e.media.seeking=!1,h=t.seconds,triggerEvent.call(e,e.media,\"timeupdate\")})),e.embed.on(\"progress\",(t=>{e.media.buffered=t.percent,triggerEvent.call(e,e.media,\"progress\"),1===parseInt(t.percent,10)&&triggerEvent.call(e,e.media,\"canplaythrough\"),e.embed.getDuration().then((t=>{t!==e.media.duration&&(e.media.duration=t,triggerEvent.call(e,e.media,\"durationchange\"))}))})),e.embed.on(\"seeked\",(()=>{e.media.seeking=!1,triggerEvent.call(e,e.media,\"seeked\")})),e.embed.on(\"ended\",(()=>{e.media.paused=!0,triggerEvent.call(e,e.media,\"ended\")})),e.embed.on(\"error\",(t=>{e.media.error=t,triggerEvent.call(e,e.media,\"error\")})),t.customControls&&setTimeout((()=>ui.build.call(e)),0)}};function parseId(e){if(is.empty(e))return null;return e.match(/^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/)?RegExp.$2:e}function assurePlaybackState(e){e&&!this.embed.hasPlayed&&(this.embed.hasPlayed=!0),this.media.paused===e&&(this.media.paused=!e,triggerEvent.call(this,this.media,e?\"play\":\"pause\"))}function getHost(e){return e.noCookie?\"https://www.youtube-nocookie.com\":\"http:\"===window.location.protocol?\"http://www.youtube.com\":void 0}const youtube={setup(){if(toggleClass(this.elements.wrapper,this.config.classNames.embed,!0),is.object(window.YT)&&is.function(window.YT.Player))youtube.ready.call(this);else{const e=window.onYouTubeIframeAPIReady;window.onYouTubeIframeAPIReady=()=>{is.function(e)&&e(),youtube.ready.call(this)},loadScript(this.config.urls.youtube.sdk).catch((e=>{this.debug.warn(\"YouTube API failed to load\",e)}))}},getTitle(e){fetch(format(this.config.urls.youtube.api,e)).then((e=>{if(is.object(e)){const{title:t,height:i,width:s}=e;this.config.title=t,ui.setTitle.call(this),this.embed.ratio=roundAspectRatio(s,i)}setAspectRatio.call(this)})).catch((()=>{setAspectRatio.call(this)}))},ready(){const e=this,t=e.config.youtube,i=e.media&&e.media.getAttribute(\"id\");if(!is.empty(i)&&i.startsWith(\"youtube-\"))return;let s=e.media.getAttribute(\"src\");is.empty(s)&&(s=e.media.getAttribute(this.config.attributes.embed.id));const n=parseId(s),r=createElement(\"div\",{id:generateId(e.provider),\"data-poster\":t.customControls?e.poster:void 0});if(e.media=replaceElement(r,e.media),t.customControls){const t=e=>`https://i.ytimg.com/vi/${n}/${e}default.jpg`;loadImage(t(\"maxres\"),121).catch((()=>loadImage(t(\"sd\"),121))).catch((()=>loadImage(t(\"hq\")))).then((t=>ui.setPoster.call(e,t.src))).then((t=>{t.includes(\"maxres\")||(e.elements.poster.style.backgroundSize=\"cover\")})).catch((()=>{}))}e.embed=new window.YT.Player(e.media,{videoId:n,host:getHost(t),playerVars:extend({},{autoplay:e.config.autoplay?1:0,hl:e.config.hl,controls:e.supported.ui&&t.customControls?0:1,disablekb:1,playsinline:e.config.playsinline&&!e.config.fullscreen.iosNative?1:0,cc_load_policy:e.captions.active?1:0,cc_lang_pref:e.config.captions.language,widget_referrer:window?window.location.href:null},t),events:{onError(t){if(!e.media.error){const i=t.data,s={2:\"The request contains an invalid parameter value. For example, this error occurs if you specify a video ID that does not have 11 characters, or if the video ID contains invalid characters, such as exclamation points or asterisks.\",5:\"The requested content cannot be played in an HTML5 player or another error related to the HTML5 player has occurred.\",100:\"The video requested was not found. This error occurs when a video has been removed (for any reason) or has been marked as private.\",101:\"The owner of the requested video does not allow it to be played in embedded players.\",150:\"The owner of the requested video does not allow it to be played in embedded players.\"}[i]||\"An unknown error occurred\";e.media.error={code:i,message:s},triggerEvent.call(e,e.media,\"error\")}},onPlaybackRateChange(t){const i=t.target;e.media.playbackRate=i.getPlaybackRate(),triggerEvent.call(e,e.media,\"ratechange\")},onReady(i){if(is.function(e.media.play))return;const s=i.target;youtube.getTitle.call(e,n),e.media.play=()=>{assurePlaybackState.call(e,!0),s.playVideo()},e.media.pause=()=>{assurePlaybackState.call(e,!1),s.pauseVideo()},e.media.stop=()=>{s.stopVideo()},e.media.duration=s.getDuration(),e.media.paused=!0,e.media.currentTime=0,Object.defineProperty(e.media,\"currentTime\",{get:()=>Number(s.getCurrentTime()),set(t){e.paused&&!e.embed.hasPlayed&&e.embed.mute(),e.media.seeking=!0,triggerEvent.call(e,e.media,\"seeking\"),s.seekTo(t)}}),Object.defineProperty(e.media,\"playbackRate\",{get:()=>s.getPlaybackRate(),set(e){s.setPlaybackRate(e)}});let{volume:r}=e.config;Object.defineProperty(e.media,\"volume\",{get:()=>r,set(t){r=t,s.setVolume(100*r),triggerEvent.call(e,e.media,\"volumechange\")}});let{muted:a}=e.config;Object.defineProperty(e.media,\"muted\",{get:()=>a,set(t){const i=is.boolean(t)?t:a;a=i,s[i?\"mute\":\"unMute\"](),s.setVolume(100*r),triggerEvent.call(e,e.media,\"volumechange\")}}),Object.defineProperty(e.media,\"currentSrc\",{get:()=>s.getVideoUrl()}),Object.defineProperty(e.media,\"ended\",{get:()=>e.currentTime===e.duration});const o=s.getAvailablePlaybackRates();e.options.speed=o.filter((t=>e.config.speed.options.includes(t))),e.supported.ui&&t.customControls&&e.media.setAttribute(\"tabindex\",-1),triggerEvent.call(e,e.media,\"timeupdate\"),triggerEvent.call(e,e.media,\"durationchange\"),clearInterval(e.timers.buffering),e.timers.buffering=setInterval((()=>{e.media.buffered=s.getVideoLoadedFraction(),(null===e.media.lastBuffered||e.media.lastBuffered<e.media.buffered)&&triggerEvent.call(e,e.media,\"progress\"),e.media.lastBuffered=e.media.buffered,1===e.media.buffered&&(clearInterval(e.timers.buffering),triggerEvent.call(e,e.media,\"canplaythrough\"))}),200),t.customControls&&setTimeout((()=>ui.build.call(e)),50)},onStateChange(i){const s=i.target;clearInterval(e.timers.playing);switch(e.media.seeking&&[1,2].includes(i.data)&&(e.media.seeking=!1,triggerEvent.call(e,e.media,\"seeked\")),i.data){case-1:triggerEvent.call(e,e.media,\"timeupdate\"),e.media.buffered=s.getVideoLoadedFraction(),triggerEvent.call(e,e.media,\"progress\");break;case 0:assurePlaybackState.call(e,!1),e.media.loop?(s.stopVideo(),s.playVideo()):triggerEvent.call(e,e.media,\"ended\");break;case 1:t.customControls&&!e.config.autoplay&&e.media.paused&&!e.embed.hasPlayed?e.media.pause():(assurePlaybackState.call(e,!0),triggerEvent.call(e,e.media,\"playing\"),e.timers.playing=setInterval((()=>{triggerEvent.call(e,e.media,\"timeupdate\")}),50),e.media.duration!==s.getDuration()&&(e.media.duration=s.getDuration(),triggerEvent.call(e,e.media,\"durationchange\")));break;case 2:e.muted||e.embed.unMute(),assurePlaybackState.call(e,!1);break;case 3:triggerEvent.call(e,e.media,\"waiting\")}triggerEvent.call(e,e.elements.container,\"statechange\",!1,{code:i.data})}}})}},media={setup(){this.media?(toggleClass(this.elements.container,this.config.classNames.type.replace(\"{0}\",this.type),!0),toggleClass(this.elements.container,this.config.classNames.provider.replace(\"{0}\",this.provider),!0),this.isEmbed&&toggleClass(this.elements.container,this.config.classNames.type.replace(\"{0}\",\"video\"),!0),this.isVideo&&(this.elements.wrapper=createElement(\"div\",{class:this.config.classNames.video}),wrap(this.media,this.elements.wrapper),this.elements.poster=createElement(\"div\",{class:this.config.classNames.poster}),this.elements.wrapper.appendChild(this.elements.poster)),this.isHTML5?html5.setup.call(this):this.isYouTube?youtube.setup.call(this):this.isVimeo&&vimeo.setup.call(this)):this.debug.warn(\"No media element found!\")}},destroy=e=>{e.manager&&e.manager.destroy(),e.elements.displayContainer&&e.elements.displayContainer.destroy(),e.elements.container.remove()};class Ads{constructor(e){_defineProperty$1(this,\"load\",(()=>{this.enabled&&(is.object(window.google)&&is.object(window.google.ima)?this.ready():loadScript(this.player.config.urls.googleIMA.sdk).then((()=>{this.ready()})).catch((()=>{this.trigger(\"error\",new Error(\"Google IMA SDK failed to load\"))})))})),_defineProperty$1(this,\"ready\",(()=>{var e;this.enabled||((e=this).manager&&e.manager.destroy(),e.elements.displayContainer&&e.elements.displayContainer.destroy(),e.elements.container.remove()),this.startSafetyTimer(12e3,\"ready()\"),this.managerPromise.then((()=>{this.clearSafetyTimer(\"onAdsManagerLoaded()\")})),this.listeners(),this.setupIMA()})),_defineProperty$1(this,\"setupIMA\",(()=>{this.elements.container=createElement(\"div\",{class:this.player.config.classNames.ads}),this.player.elements.container.appendChild(this.elements.container),google.ima.settings.setVpaidMode(google.ima.ImaSdkSettings.VpaidMode.ENABLED),google.ima.settings.setLocale(this.player.config.ads.language),google.ima.settings.setDisableCustomPlaybackForIOS10Plus(this.player.config.playsinline),this.elements.displayContainer=new google.ima.AdDisplayContainer(this.elements.container,this.player.media),this.loader=new google.ima.AdsLoader(this.elements.displayContainer),this.loader.addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,(e=>this.onAdsManagerLoaded(e)),!1),this.loader.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,(e=>this.onAdError(e)),!1),this.requestAds()})),_defineProperty$1(this,\"requestAds\",(()=>{const{container:e}=this.player.elements;try{const t=new google.ima.AdsRequest;t.adTagUrl=this.tagUrl,t.linearAdSlotWidth=e.offsetWidth,t.linearAdSlotHeight=e.offsetHeight,t.nonLinearAdSlotWidth=e.offsetWidth,t.nonLinearAdSlotHeight=e.offsetHeight,t.forceNonLinearFullSlot=!1,t.setAdWillPlayMuted(!this.player.muted),this.loader.requestAds(t)}catch(e){this.onAdError(e)}})),_defineProperty$1(this,\"pollCountdown\",((e=!1)=>{if(!e)return clearInterval(this.countdownTimer),void this.elements.container.removeAttribute(\"data-badge-text\");this.countdownTimer=setInterval((()=>{const e=formatTime(Math.max(this.manager.getRemainingTime(),0)),t=`${i18n.get(\"advertisement\",this.player.config)} - ${e}`;this.elements.container.setAttribute(\"data-badge-text\",t)}),100)})),_defineProperty$1(this,\"onAdsManagerLoaded\",(e=>{if(!this.enabled)return;const t=new google.ima.AdsRenderingSettings;t.restoreCustomPlaybackStateOnAdBreakComplete=!0,t.enablePreloading=!0,this.manager=e.getAdsManager(this.player,t),this.cuePoints=this.manager.getCuePoints(),this.manager.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,(e=>this.onAdError(e))),Object.keys(google.ima.AdEvent.Type).forEach((e=>{this.manager.addEventListener(google.ima.AdEvent.Type[e],(e=>this.onAdEvent(e)))})),this.trigger(\"loaded\")})),_defineProperty$1(this,\"addCuePoints\",(()=>{is.empty(this.cuePoints)||this.cuePoints.forEach((e=>{if(0!==e&&-1!==e&&e<this.player.duration){const t=this.player.elements.progress;if(is.element(t)){const i=100/this.player.duration*e,s=createElement(\"span\",{class:this.player.config.classNames.cues});s.style.left=`${i.toString()}%`,t.appendChild(s)}}}))})),_defineProperty$1(this,\"onAdEvent\",(e=>{const{container:t}=this.player.elements,i=e.getAd(),s=e.getAdData();switch((e=>{triggerEvent.call(this.player,this.player.media,`ads${e.replace(/_/g,\"\").toLowerCase()}`)})(e.type),e.type){case google.ima.AdEvent.Type.LOADED:this.trigger(\"loaded\"),this.pollCountdown(!0),i.isLinear()||(i.width=t.offsetWidth,i.height=t.offsetHeight);break;case google.ima.AdEvent.Type.STARTED:this.manager.setVolume(this.player.volume);break;case google.ima.AdEvent.Type.ALL_ADS_COMPLETED:this.player.ended?this.loadAds():this.loader.contentComplete();break;case google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED:this.pauseContent();break;case google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED:this.pollCountdown(),this.resumeContent();break;case google.ima.AdEvent.Type.LOG:s.adError&&this.player.debug.warn(`Non-fatal ad error: ${s.adError.getMessage()}`)}})),_defineProperty$1(this,\"onAdError\",(e=>{this.cancel(),this.player.debug.warn(\"Ads error\",e)})),_defineProperty$1(this,\"listeners\",(()=>{const{container:e}=this.player.elements;let t;this.player.on(\"canplay\",(()=>{this.addCuePoints()})),this.player.on(\"ended\",(()=>{this.loader.contentComplete()})),this.player.on(\"timeupdate\",(()=>{t=this.player.currentTime})),this.player.on(\"seeked\",(()=>{const e=this.player.currentTime;is.empty(this.cuePoints)||this.cuePoints.forEach(((i,s)=>{t<i&&i<e&&(this.manager.discardAdBreak(),this.cuePoints.splice(s,1))}))})),window.addEventListener(\"resize\",(()=>{this.manager&&this.manager.resize(e.offsetWidth,e.offsetHeight,google.ima.ViewMode.NORMAL)}))})),_defineProperty$1(this,\"play\",(()=>{const{container:e}=this.player.elements;this.managerPromise||this.resumeContent(),this.managerPromise.then((()=>{this.manager.setVolume(this.player.volume),this.elements.displayContainer.initialize();try{this.initialized||(this.manager.init(e.offsetWidth,e.offsetHeight,google.ima.ViewMode.NORMAL),this.manager.start()),this.initialized=!0}catch(e){this.onAdError(e)}})).catch((()=>{}))})),_defineProperty$1(this,\"resumeContent\",(()=>{this.elements.container.style.zIndex=\"\",this.playing=!1,silencePromise(this.player.media.play())})),_defineProperty$1(this,\"pauseContent\",(()=>{this.elements.container.style.zIndex=3,this.playing=!0,this.player.media.pause()})),_defineProperty$1(this,\"cancel\",(()=>{this.initialized&&this.resumeContent(),this.trigger(\"error\"),this.loadAds()})),_defineProperty$1(this,\"loadAds\",(()=>{this.managerPromise.then((()=>{this.manager&&this.manager.destroy(),this.managerPromise=new Promise((e=>{this.on(\"loaded\",e),this.player.debug.log(this.manager)})),this.initialized=!1,this.requestAds()})).catch((()=>{}))})),_defineProperty$1(this,\"trigger\",((e,...t)=>{const i=this.events[e];is.array(i)&&i.forEach((e=>{is.function(e)&&e.apply(this,t)}))})),_defineProperty$1(this,\"on\",((e,t)=>(is.array(this.events[e])||(this.events[e]=[]),this.events[e].push(t),this))),_defineProperty$1(this,\"startSafetyTimer\",((e,t)=>{this.player.debug.log(`Safety timer invoked from: ${t}`),this.safetyTimer=setTimeout((()=>{this.cancel(),this.clearSafetyTimer(\"startSafetyTimer()\")}),e)})),_defineProperty$1(this,\"clearSafetyTimer\",(e=>{is.nullOrUndefined(this.safetyTimer)||(this.player.debug.log(`Safety timer cleared from: ${e}`),clearTimeout(this.safetyTimer),this.safetyTimer=null)})),this.player=e,this.config=e.config.ads,this.playing=!1,this.initialized=!1,this.elements={container:null,displayContainer:null},this.manager=null,this.loader=null,this.cuePoints=null,this.events={},this.safetyTimer=null,this.countdownTimer=null,this.managerPromise=new Promise(((e,t)=>{this.on(\"loaded\",e),this.on(\"error\",t)})),this.load()}get enabled(){const{config:e}=this;return this.player.isHTML5&&this.player.isVideo&&e.enabled&&(!is.empty(e.publisherId)||is.url(e.tagUrl))}get tagUrl(){const{config:e}=this;if(is.url(e.tagUrl))return e.tagUrl;return`https://go.aniview.com/api/adserver6/vast/?${buildUrlParams({AV_PUBLISHERID:\"58c25bb0073ef448b1087ad6\",AV_CHANNELID:\"5a0458dc28a06145e4519d21\",AV_URL:window.location.hostname,cb:Date.now(),AV_WIDTH:640,AV_HEIGHT:480,AV_CDIM2:e.publisherId})}`}}function clamp(e=0,t=0,i=255){return Math.min(Math.max(e,t),i)}const parseVtt=e=>{const t=[];return e.split(/\\r\\n\\r\\n|\\n\\n|\\r\\r/).forEach((e=>{const i={};e.split(/\\r\\n|\\n|\\r/).forEach((e=>{if(is.number(i.startTime)){if(!is.empty(e.trim())&&is.empty(i.text)){const t=e.trim().split(\"#xywh=\");[i.text]=t,t[1]&&([i.x,i.y,i.w,i.h]=t[1].split(\",\"))}}else{const t=e.match(/([0-9]{2})?:?([0-9]{2}):([0-9]{2}).([0-9]{2,3})( ?--> ?)([0-9]{2})?:?([0-9]{2}):([0-9]{2}).([0-9]{2,3})/);t&&(i.startTime=60*Number(t[1]||0)*60+60*Number(t[2])+Number(t[3])+Number(`0.${t[4]}`),i.endTime=60*Number(t[6]||0)*60+60*Number(t[7])+Number(t[8])+Number(`0.${t[9]}`))}})),i.text&&t.push(i)})),t},fitRatio=(e,t)=>{const i={};return e>t.width/t.height?(i.width=t.width,i.height=1/e*t.width):(i.height=t.height,i.width=e*t.height),i};class PreviewThumbnails{constructor(e){_defineProperty$1(this,\"load\",(()=>{this.player.elements.display.seekTooltip&&(this.player.elements.display.seekTooltip.hidden=this.enabled),this.enabled&&this.getThumbnails().then((()=>{this.enabled&&(this.render(),this.determineContainerAutoSizing(),this.listeners(),this.loaded=!0)}))})),_defineProperty$1(this,\"getThumbnails\",(()=>new Promise((e=>{const{src:t}=this.player.config.previewThumbnails;if(is.empty(t))throw new Error(\"Missing previewThumbnails.src config attribute\");const i=()=>{this.thumbnails.sort(((e,t)=>e.height-t.height)),this.player.debug.log(\"Preview thumbnails\",this.thumbnails),e()};if(is.function(t))t((e=>{this.thumbnails=e,i()}));else{const e=(is.string(t)?[t]:t).map((e=>this.getThumbnail(e)));Promise.all(e).then(i)}})))),_defineProperty$1(this,\"getThumbnail\",(e=>new Promise((t=>{fetch(e).then((i=>{const s={frames:parseVtt(i),height:null,urlPrefix:\"\"};s.frames[0].text.startsWith(\"/\")||s.frames[0].text.startsWith(\"http://\")||s.frames[0].text.startsWith(\"https://\")||(s.urlPrefix=e.substring(0,e.lastIndexOf(\"/\")+1));const n=new Image;n.onload=()=>{s.height=n.naturalHeight,s.width=n.naturalWidth,this.thumbnails.push(s),t()},n.src=s.urlPrefix+s.frames[0].text}))})))),_defineProperty$1(this,\"startMove\",(e=>{if(this.loaded&&is.event(e)&&[\"touchmove\",\"mousemove\"].includes(e.type)&&this.player.media.duration){if(\"touchmove\"===e.type)this.seekTime=this.player.media.duration*(this.player.elements.inputs.seek.value/100);else{var t,i;const s=this.player.elements.progress.getBoundingClientRect(),n=100/s.width*(e.pageX-s.left);this.seekTime=this.player.media.duration*(n/100),this.seekTime<0&&(this.seekTime=0),this.seekTime>this.player.media.duration-1&&(this.seekTime=this.player.media.duration-1),this.mousePosX=e.pageX,this.elements.thumb.time.innerText=formatTime(this.seekTime);const r=null===(t=this.player.config.markers)||void 0===t||null===(i=t.points)||void 0===i?void 0:i.find((({time:e})=>e===Math.round(this.seekTime)));r&&this.elements.thumb.time.insertAdjacentHTML(\"afterbegin\",`${r.label}<br>`)}this.showImageAtCurrentTime()}})),_defineProperty$1(this,\"endMove\",(()=>{this.toggleThumbContainer(!1,!0)})),_defineProperty$1(this,\"startScrubbing\",(e=>{(is.nullOrUndefined(e.button)||!1===e.button||0===e.button)&&(this.mouseDown=!0,this.player.media.duration&&(this.toggleScrubbingContainer(!0),this.toggleThumbContainer(!1,!0),this.showImageAtCurrentTime()))})),_defineProperty$1(this,\"endScrubbing\",(()=>{this.mouseDown=!1,Math.ceil(this.lastTime)===Math.ceil(this.player.media.currentTime)?this.toggleScrubbingContainer(!1):once.call(this.player,this.player.media,\"timeupdate\",(()=>{this.mouseDown||this.toggleScrubbingContainer(!1)}))})),_defineProperty$1(this,\"listeners\",(()=>{this.player.on(\"play\",(()=>{this.toggleThumbContainer(!1,!0)})),this.player.on(\"seeked\",(()=>{this.toggleThumbContainer(!1)})),this.player.on(\"timeupdate\",(()=>{this.lastTime=this.player.media.currentTime}))})),_defineProperty$1(this,\"render\",(()=>{this.elements.thumb.container=createElement(\"div\",{class:this.player.config.classNames.previewThumbnails.thumbContainer}),this.elements.thumb.imageContainer=createElement(\"div\",{class:this.player.config.classNames.previewThumbnails.imageContainer}),this.elements.thumb.container.appendChild(this.elements.thumb.imageContainer);const e=createElement(\"div\",{class:this.player.config.classNames.previewThumbnails.timeContainer});this.elements.thumb.time=createElement(\"span\",{},\"00:00\"),e.appendChild(this.elements.thumb.time),this.elements.thumb.imageContainer.appendChild(e),is.element(this.player.elements.progress)&&this.player.elements.progress.appendChild(this.elements.thumb.container),this.elements.scrubbing.container=createElement(\"div\",{class:this.player.config.classNames.previewThumbnails.scrubbingContainer}),this.player.elements.wrapper.appendChild(this.elements.scrubbing.container)})),_defineProperty$1(this,\"destroy\",(()=>{this.elements.thumb.container&&this.elements.thumb.container.remove(),this.elements.scrubbing.container&&this.elements.scrubbing.container.remove()})),_defineProperty$1(this,\"showImageAtCurrentTime\",(()=>{this.mouseDown?this.setScrubbingContainerSize():this.setThumbContainerSizeAndPos();const e=this.thumbnails[0].frames.findIndex((e=>this.seekTime>=e.startTime&&this.seekTime<=e.endTime)),t=e>=0;let i=0;this.mouseDown||this.toggleThumbContainer(t),t&&(this.thumbnails.forEach(((t,s)=>{this.loadedImages.includes(t.frames[e].text)&&(i=s)})),e!==this.showingThumb&&(this.showingThumb=e,this.loadImage(i)))})),_defineProperty$1(this,\"loadImage\",((e=0)=>{const t=this.showingThumb,i=this.thumbnails[e],{urlPrefix:s}=i,n=i.frames[t],r=i.frames[t].text,a=s+r;if(this.currentImageElement&&this.currentImageElement.dataset.filename===r)this.showImage(this.currentImageElement,n,e,t,r,!1),this.currentImageElement.dataset.index=t,this.removeOldImages(this.currentImageElement);else{this.loadingImage&&this.usingSprites&&(this.loadingImage.onload=null);const i=new Image;i.src=a,i.dataset.index=t,i.dataset.filename=r,this.showingThumbFilename=r,this.player.debug.log(`Loading image: ${a}`),i.onload=()=>this.showImage(i,n,e,t,r,!0),this.loadingImage=i,this.removeOldImages(i)}})),_defineProperty$1(this,\"showImage\",((e,t,i,s,n,r=!0)=>{this.player.debug.log(`Showing thumb: ${n}. num: ${s}. qual: ${i}. newimg: ${r}`),this.setImageSizeAndOffset(e,t),r&&(this.currentImageContainer.appendChild(e),this.currentImageElement=e,this.loadedImages.includes(n)||this.loadedImages.push(n)),this.preloadNearby(s,!0).then(this.preloadNearby(s,!1)).then(this.getHigherQuality(i,e,t,n))})),_defineProperty$1(this,\"removeOldImages\",(e=>{Array.from(this.currentImageContainer.children).forEach((t=>{if(\"img\"!==t.tagName.toLowerCase())return;const i=this.usingSprites?500:1e3;if(t.dataset.index!==e.dataset.index&&!t.dataset.deleting){t.dataset.deleting=!0;const{currentImageContainer:e}=this;setTimeout((()=>{e.removeChild(t),this.player.debug.log(`Removing thumb: ${t.dataset.filename}`)}),i)}}))})),_defineProperty$1(this,\"preloadNearby\",((e,t=!0)=>new Promise((i=>{setTimeout((()=>{const s=this.thumbnails[0].frames[e].text;if(this.showingThumbFilename===s){let n;n=t?this.thumbnails[0].frames.slice(e):this.thumbnails[0].frames.slice(0,e).reverse();let r=!1;n.forEach((e=>{const t=e.text;if(t!==s&&!this.loadedImages.includes(t)){r=!0,this.player.debug.log(`Preloading thumb filename: ${t}`);const{urlPrefix:e}=this.thumbnails[0],s=e+t,n=new Image;n.src=s,n.onload=()=>{this.player.debug.log(`Preloaded thumb filename: ${t}`),this.loadedImages.includes(t)||this.loadedImages.push(t),i()}}})),r||i()}}),300)})))),_defineProperty$1(this,\"getHigherQuality\",((e,t,i,s)=>{if(e<this.thumbnails.length-1){let n=t.naturalHeight;this.usingSprites&&(n=i.h),n<this.thumbContainerHeight&&setTimeout((()=>{this.showingThumbFilename===s&&(this.player.debug.log(`Showing higher quality thumb for: ${s}`),this.loadImage(e+1))}),300)}})),_defineProperty$1(this,\"toggleThumbContainer\",((e=!1,t=!1)=>{const i=this.player.config.classNames.previewThumbnails.thumbContainerShown;this.elements.thumb.container.classList.toggle(i,e),!e&&t&&(this.showingThumb=null,this.showingThumbFilename=null)})),_defineProperty$1(this,\"toggleScrubbingContainer\",((e=!1)=>{const t=this.player.config.classNames.previewThumbnails.scrubbingContainerShown;this.elements.scrubbing.container.classList.toggle(t,e),e||(this.showingThumb=null,this.showingThumbFilename=null)})),_defineProperty$1(this,\"determineContainerAutoSizing\",(()=>{(this.elements.thumb.imageContainer.clientHeight>20||this.elements.thumb.imageContainer.clientWidth>20)&&(this.sizeSpecifiedInCSS=!0)})),_defineProperty$1(this,\"setThumbContainerSizeAndPos\",(()=>{const{imageContainer:e}=this.elements.thumb;if(this.sizeSpecifiedInCSS){if(e.clientHeight>20&&e.clientWidth<20){const t=Math.floor(e.clientHeight*this.thumbAspectRatio);e.style.width=`${t}px`}else if(e.clientHeight<20&&e.clientWidth>20){const t=Math.floor(e.clientWidth/this.thumbAspectRatio);e.style.height=`${t}px`}}else{const t=Math.floor(this.thumbContainerHeight*this.thumbAspectRatio);e.style.height=`${this.thumbContainerHeight}px`,e.style.width=`${t}px`}this.setThumbContainerPos()})),_defineProperty$1(this,\"setThumbContainerPos\",(()=>{const e=this.player.elements.progress.getBoundingClientRect(),t=this.player.elements.container.getBoundingClientRect(),{container:i}=this.elements.thumb,s=t.left-e.left+10,n=t.right-e.left-i.clientWidth-10,r=this.mousePosX-e.left-i.clientWidth/2,a=clamp(r,s,n);i.style.left=`${a}px`,i.style.setProperty(\"--preview-arrow-offset\",r-a+\"px\")})),_defineProperty$1(this,\"setScrubbingContainerSize\",(()=>{const{width:e,height:t}=fitRatio(this.thumbAspectRatio,{width:this.player.media.clientWidth,height:this.player.media.clientHeight});this.elements.scrubbing.container.style.width=`${e}px`,this.elements.scrubbing.container.style.height=`${t}px`})),_defineProperty$1(this,\"setImageSizeAndOffset\",((e,t)=>{if(!this.usingSprites)return;const i=this.thumbContainerHeight/t.h;e.style.height=e.naturalHeight*i+\"px\",e.style.width=e.naturalWidth*i+\"px\",e.style.left=`-${t.x*i}px`,e.style.top=`-${t.y*i}px`})),this.player=e,this.thumbnails=[],this.loaded=!1,this.lastMouseMoveTime=Date.now(),this.mouseDown=!1,this.loadedImages=[],this.elements={thumb:{},scrubbing:{}},this.load()}get enabled(){return this.player.isHTML5&&this.player.isVideo&&this.player.config.previewThumbnails.enabled}get currentImageContainer(){return this.mouseDown?this.elements.scrubbing.container:this.elements.thumb.imageContainer}get usingSprites(){return Object.keys(this.thumbnails[0].frames[0]).includes(\"w\")}get thumbAspectRatio(){return this.usingSprites?this.thumbnails[0].frames[0].w/this.thumbnails[0].frames[0].h:this.thumbnails[0].width/this.thumbnails[0].height}get thumbContainerHeight(){if(this.mouseDown){const{height:e}=fitRatio(this.thumbAspectRatio,{width:this.player.media.clientWidth,height:this.player.media.clientHeight});return e}return this.sizeSpecifiedInCSS?this.elements.thumb.imageContainer.clientHeight:Math.floor(this.player.media.clientWidth/this.thumbAspectRatio/4)}get currentImageElement(){return this.mouseDown?this.currentScrubbingImageElement:this.currentThumbnailImageElement}set currentImageElement(e){this.mouseDown?this.currentScrubbingImageElement=e:this.currentThumbnailImageElement=e}}const source={insertElements(e,t){is.string(t)?insertElement(e,this.media,{src:t}):is.array(t)&&t.forEach((t=>{insertElement(e,this.media,t)}))},change(e){getDeep(e,\"sources.length\")?(html5.cancelRequests.call(this),this.destroy.call(this,(()=>{this.options.quality=[],removeElement(this.media),this.media=null,is.element(this.elements.container)&&this.elements.container.removeAttribute(\"class\");const{sources:t,type:i}=e,[{provider:s=providers.html5,src:n}]=t,r=\"html5\"===s?i:\"div\",a=\"html5\"===s?{}:{src:n};Object.assign(this,{provider:s,type:i,supported:support.check(i,s,this.config.playsinline),media:createElement(r,a)}),this.elements.container.appendChild(this.media),is.boolean(e.autoplay)&&(this.config.autoplay=e.autoplay),this.isHTML5&&(this.config.crossorigin&&this.media.setAttribute(\"crossorigin\",\"\"),this.config.autoplay&&this.media.setAttribute(\"autoplay\",\"\"),is.empty(e.poster)||(this.poster=e.poster),this.config.loop.active&&this.media.setAttribute(\"loop\",\"\"),this.config.muted&&this.media.setAttribute(\"muted\",\"\"),this.config.playsinline&&this.media.setAttribute(\"playsinline\",\"\")),ui.addStyleHook.call(this),this.isHTML5&&source.insertElements.call(this,\"source\",t),this.config.title=e.title,media.setup.call(this),this.isHTML5&&Object.keys(e).includes(\"tracks\")&&source.insertElements.call(this,\"track\",e.tracks),(this.isHTML5||this.isEmbed&&!this.supported.ui)&&ui.build.call(this),this.isHTML5&&this.media.load(),is.empty(e.previewThumbnails)||(Object.assign(this.config.previewThumbnails,e.previewThumbnails),this.previewThumbnails&&this.previewThumbnails.loaded&&(this.previewThumbnails.destroy(),this.previewThumbnails=null),this.config.previewThumbnails.enabled&&(this.previewThumbnails=new PreviewThumbnails(this))),this.fullscreen.update()}),!0)):this.debug.warn(\"Invalid source format\")}};class Plyr{constructor(e,t){if(_defineProperty$1(this,\"play\",(()=>is.function(this.media.play)?(this.ads&&this.ads.enabled&&this.ads.managerPromise.then((()=>this.ads.play())).catch((()=>silencePromise(this.media.play()))),this.media.play()):null)),_defineProperty$1(this,\"pause\",(()=>this.playing&&is.function(this.media.pause)?this.media.pause():null)),_defineProperty$1(this,\"togglePlay\",(e=>(is.boolean(e)?e:!this.playing)?this.play():this.pause())),_defineProperty$1(this,\"stop\",(()=>{this.isHTML5?(this.pause(),this.restart()):is.function(this.media.stop)&&this.media.stop()})),_defineProperty$1(this,\"restart\",(()=>{this.currentTime=0})),_defineProperty$1(this,\"rewind\",(e=>{this.currentTime-=is.number(e)?e:this.config.seekTime})),_defineProperty$1(this,\"forward\",(e=>{this.currentTime+=is.number(e)?e:this.config.seekTime})),_defineProperty$1(this,\"increaseVolume\",(e=>{const t=this.media.muted?0:this.volume;this.volume=t+(is.number(e)?e:0)})),_defineProperty$1(this,\"decreaseVolume\",(e=>{this.increaseVolume(-e)})),_defineProperty$1(this,\"airplay\",(()=>{support.airplay&&this.media.webkitShowPlaybackTargetPicker()})),_defineProperty$1(this,\"toggleControls\",(e=>{if(this.supported.ui&&!this.isAudio){const t=hasClass(this.elements.container,this.config.classNames.hideControls),i=void 0===e?void 0:!e,s=toggleClass(this.elements.container,this.config.classNames.hideControls,i);if(s&&is.array(this.config.controls)&&this.config.controls.includes(\"settings\")&&!is.empty(this.config.settings)&&controls.toggleMenu.call(this,!1),s!==t){const e=s?\"controlshidden\":\"controlsshown\";triggerEvent.call(this,this.media,e)}return!s}return!1})),_defineProperty$1(this,\"on\",((e,t)=>{on.call(this,this.elements.container,e,t)})),_defineProperty$1(this,\"once\",((e,t)=>{once.call(this,this.elements.container,e,t)})),_defineProperty$1(this,\"off\",((e,t)=>{off(this.elements.container,e,t)})),_defineProperty$1(this,\"destroy\",((e,t=!1)=>{if(!this.ready)return;const i=()=>{document.body.style.overflow=\"\",this.embed=null,t?(Object.keys(this.elements).length&&(removeElement(this.elements.buttons.play),removeElement(this.elements.captions),removeElement(this.elements.controls),removeElement(this.elements.wrapper),this.elements.buttons.play=null,this.elements.captions=null,this.elements.controls=null,this.elements.wrapper=null),is.function(e)&&e()):(unbindListeners.call(this),html5.cancelRequests.call(this),replaceElement(this.elements.original,this.elements.container),triggerEvent.call(this,this.elements.original,\"destroyed\",!0),is.function(e)&&e.call(this.elements.original),this.ready=!1,setTimeout((()=>{this.elements=null,this.media=null}),200))};this.stop(),clearTimeout(this.timers.loading),clearTimeout(this.timers.controls),clearTimeout(this.timers.resized),this.isHTML5?(ui.toggleNativeControls.call(this,!0),i()):this.isYouTube?(clearInterval(this.timers.buffering),clearInterval(this.timers.playing),null!==this.embed&&is.function(this.embed.destroy)&&this.embed.destroy(),i()):this.isVimeo&&(null!==this.embed&&this.embed.unload().then(i),setTimeout(i,200))})),_defineProperty$1(this,\"supports\",(e=>support.mime.call(this,e))),this.timers={},this.ready=!1,this.loading=!1,this.failed=!1,this.touch=support.touch,this.media=e,is.string(this.media)&&(this.media=document.querySelectorAll(this.media)),(window.jQuery&&this.media instanceof jQuery||is.nodeList(this.media)||is.array(this.media))&&(this.media=this.media[0]),this.config=extend({},defaults,Plyr.defaults,t||{},(()=>{try{return JSON.parse(this.media.getAttribute(\"data-plyr-config\"))}catch(e){return{}}})()),this.elements={container:null,fullscreen:null,captions:null,buttons:{},display:{},progress:{},inputs:{},settings:{popup:null,menu:null,panels:{},buttons:{}}},this.captions={active:null,currentTrack:-1,meta:new WeakMap},this.fullscreen={active:!1},this.options={speed:[],quality:[]},this.debug=new Console(this.config.debug),this.debug.log(\"Config\",this.config),this.debug.log(\"Support\",support),is.nullOrUndefined(this.media)||!is.element(this.media))return void this.debug.error(\"Setup failed: no suitable element passed\");if(this.media.plyr)return void this.debug.warn(\"Target already setup\");if(!this.config.enabled)return void this.debug.error(\"Setup failed: disabled by config\");if(!support.check().api)return void this.debug.error(\"Setup failed: no support\");const i=this.media.cloneNode(!0);i.autoplay=!1,this.elements.original=i;const s=this.media.tagName.toLowerCase();let n=null,r=null;switch(s){case\"div\":if(n=this.media.querySelector(\"iframe\"),is.element(n)){if(r=parseUrl(n.getAttribute(\"src\")),this.provider=getProviderByUrl(r.toString()),this.elements.container=this.media,this.media=n,this.elements.container.className=\"\",r.search.length){const e=[\"1\",\"true\"];e.includes(r.searchParams.get(\"autoplay\"))&&(this.config.autoplay=!0),e.includes(r.searchParams.get(\"loop\"))&&(this.config.loop.active=!0),this.isYouTube?(this.config.playsinline=e.includes(r.searchParams.get(\"playsinline\")),this.config.youtube.hl=r.searchParams.get(\"hl\")):this.config.playsinline=!0}}else this.provider=this.media.getAttribute(this.config.attributes.embed.provider),this.media.removeAttribute(this.config.attributes.embed.provider);if(is.empty(this.provider)||!Object.values(providers).includes(this.provider))return void this.debug.error(\"Setup failed: Invalid provider\");this.type=types.video;break;case\"video\":case\"audio\":this.type=s,this.provider=providers.html5,this.media.hasAttribute(\"crossorigin\")&&(this.config.crossorigin=!0),this.media.hasAttribute(\"autoplay\")&&(this.config.autoplay=!0),(this.media.hasAttribute(\"playsinline\")||this.media.hasAttribute(\"webkit-playsinline\"))&&(this.config.playsinline=!0),this.media.hasAttribute(\"muted\")&&(this.config.muted=!0),this.media.hasAttribute(\"loop\")&&(this.config.loop.active=!0);break;default:return void this.debug.error(\"Setup failed: unsupported type\")}this.supported=support.check(this.type,this.provider),this.supported.api?(this.eventListeners=[],this.listeners=new Listeners(this),this.storage=new Storage(this),this.media.plyr=this,is.element(this.elements.container)||(this.elements.container=createElement(\"div\"),wrap(this.media,this.elements.container)),ui.migrateStyles.call(this),ui.addStyleHook.call(this),media.setup.call(this),this.config.debug&&on.call(this,this.elements.container,this.config.events.join(\" \"),(e=>{this.debug.log(`event: ${e.type}`)})),this.fullscreen=new Fullscreen(this),(this.isHTML5||this.isEmbed&&!this.supported.ui)&&ui.build.call(this),this.listeners.container(),this.listeners.global(),this.config.ads.enabled&&(this.ads=new Ads(this)),this.isHTML5&&this.config.autoplay&&this.once(\"canplay\",(()=>silencePromise(this.play()))),this.lastSeekTime=0,this.config.previewThumbnails.enabled&&(this.previewThumbnails=new PreviewThumbnails(this))):this.debug.error(\"Setup failed: no support\")}get isHTML5(){return this.provider===providers.html5}get isEmbed(){return this.isYouTube||this.isVimeo}get isYouTube(){return this.provider===providers.youtube}get isVimeo(){return this.provider===providers.vimeo}get isVideo(){return this.type===types.video}get isAudio(){return this.type===types.audio}get playing(){return Boolean(this.ready&&!this.paused&&!this.ended)}get paused(){return Boolean(this.media.paused)}get stopped(){return Boolean(this.paused&&0===this.currentTime)}get ended(){return Boolean(this.media.ended)}set currentTime(e){if(!this.duration)return;const t=is.number(e)&&e>0;this.media.currentTime=t?Math.min(e,this.duration):0,this.debug.log(`Seeking to ${this.currentTime} seconds`)}get currentTime(){return Number(this.media.currentTime)}get buffered(){const{buffered:e}=this.media;return is.number(e)?e:e&&e.length&&this.duration>0?e.end(0)/this.duration:0}get seeking(){return Boolean(this.media.seeking)}get duration(){const e=parseFloat(this.config.duration),t=(this.media||{}).duration,i=is.number(t)&&t!==1/0?t:0;return e||i}set volume(e){let t=e;is.string(t)&&(t=Number(t)),is.number(t)||(t=this.storage.get(\"volume\")),is.number(t)||({volume:t}=this.config),t>1&&(t=1),t<0&&(t=0),this.config.volume=t,this.media.volume=t,!is.empty(e)&&this.muted&&t>0&&(this.muted=!1)}get volume(){return Number(this.media.volume)}set muted(e){let t=e;is.boolean(t)||(t=this.storage.get(\"muted\")),is.boolean(t)||(t=this.config.muted),this.config.muted=t,this.media.muted=t}get muted(){return Boolean(this.media.muted)}get hasAudio(){return!this.isHTML5||(!!this.isAudio||(Boolean(this.media.mozHasAudio)||Boolean(this.media.webkitAudioDecodedByteCount)||Boolean(this.media.audioTracks&&this.media.audioTracks.length)))}set speed(e){let t=null;is.number(e)&&(t=e),is.number(t)||(t=this.storage.get(\"speed\")),is.number(t)||(t=this.config.speed.selected);const{minimumSpeed:i,maximumSpeed:s}=this;t=clamp(t,i,s),this.config.speed.selected=t,setTimeout((()=>{this.media&&(this.media.playbackRate=t)}),0)}get speed(){return Number(this.media.playbackRate)}get minimumSpeed(){return this.isYouTube?Math.min(...this.options.speed):this.isVimeo?.5:.0625}get maximumSpeed(){return this.isYouTube?Math.max(...this.options.speed):this.isVimeo?2:16}set quality(e){const t=this.config.quality,i=this.options.quality;if(!i.length)return;let s=[!is.empty(e)&&Number(e),this.storage.get(\"quality\"),t.selected,t.default].find(is.number),n=!0;if(!i.includes(s)){const e=closest(i,s);this.debug.warn(`Unsupported quality option: ${s}, using ${e} instead`),s=e,n=!1}t.selected=s,this.media.quality=s,n&&this.storage.set({quality:s})}get quality(){return this.media.quality}set loop(e){const t=is.boolean(e)?e:this.config.loop.active;this.config.loop.active=t,this.media.loop=t}get loop(){return Boolean(this.media.loop)}set source(e){source.change.call(this,e)}get source(){return this.media.currentSrc}get download(){const{download:e}=this.config.urls;return is.url(e)?e:this.source}set download(e){is.url(e)&&(this.config.urls.download=e,controls.setDownloadUrl.call(this))}set poster(e){this.isVideo?ui.setPoster.call(this,e,!1).catch((()=>{})):this.debug.warn(\"Poster can only be set for video\")}get poster(){return this.isVideo?this.media.getAttribute(\"poster\")||this.media.getAttribute(\"data-poster\"):null}get ratio(){if(!this.isVideo)return null;const e=reduceAspectRatio(getAspectRatio.call(this));return is.array(e)?e.join(\":\"):e}set ratio(e){this.isVideo?is.string(e)&&validateAspectRatio(e)?(this.config.ratio=reduceAspectRatio(e),setAspectRatio.call(this)):this.debug.error(`Invalid aspect ratio specified (${e})`):this.debug.warn(\"Aspect ratio can only be set for video\")}set autoplay(e){this.config.autoplay=is.boolean(e)?e:this.config.autoplay}get autoplay(){return Boolean(this.config.autoplay)}toggleCaptions(e){captions.toggle.call(this,e,!1)}set currentTrack(e){captions.set.call(this,e,!1),captions.setup.call(this)}get currentTrack(){const{toggled:e,currentTrack:t}=this.captions;return e?t:-1}set language(e){captions.setLanguage.call(this,e,!1)}get language(){return(captions.getCurrentTrack.call(this)||{}).language}set pip(e){if(!support.pip)return;const t=is.boolean(e)?e:!this.pip;is.function(this.media.webkitSetPresentationMode)&&this.media.webkitSetPresentationMode(t?pip.active:pip.inactive),is.function(this.media.requestPictureInPicture)&&(!this.pip&&t?this.media.requestPictureInPicture():this.pip&&!t&&document.exitPictureInPicture())}get pip(){return support.pip?is.empty(this.media.webkitPresentationMode)?this.media===document.pictureInPictureElement:this.media.webkitPresentationMode===pip.active:null}setPreviewThumbnails(e){this.previewThumbnails&&this.previewThumbnails.loaded&&(this.previewThumbnails.destroy(),this.previewThumbnails=null),Object.assign(this.config.previewThumbnails,e),this.config.previewThumbnails.enabled&&(this.previewThumbnails=new PreviewThumbnails(this))}static supported(e,t){return support.check(e,t)}static loadSprite(e,t){return loadSprite(e,t)}static setup(e,t={}){let i=null;return is.string(e)?i=Array.from(document.querySelectorAll(e)):is.nodeList(e)?i=Array.from(e):is.array(e)&&(i=e.filter(is.element)),is.empty(i)?null:i.map((e=>new Plyr(e,t)))}}Plyr.defaults=cloneDeep(defaults);export{Plyr as default};", "import { useState, useRef, useEffect, useMemo, useImperativeHandle } from 'react';\n\nfunction useAptor(ref, configuration, deps = []) {\n  const [instance, setInstance] = useState(null);\n  const domRef = useRef(null);\n  const { instantiate, destroy, getAPI, params } = configuration;\n  useEffect(() => {\n    const instanceReference = instantiate(domRef.current, params);\n    setInstance(instanceReference);\n    return () => {\n      if (destroy)\n        destroy(instanceReference, params);\n    };\n  }, deps);\n  const api = useMemo(() => getAPI(instance, params), [instance]);\n  useImperativeHandle(ref, api, [api]);\n  return domRef;\n}\n\nexport { useAptor as default, useAptor };\n"], "mappings": ";;;;;;;;;;;;;;AAAA,yBAAoB;AACpB,YAAuB;;;ACDvB,SAAS,kBAAkB,GAAE,GAAE,GAAE;AAAC,UAAO,IAAE,eAAe,CAAC,MAAK,IAAE,OAAO,eAAe,GAAE,GAAE,EAAC,OAAM,GAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAE;AAAC;AAAC,SAAS,aAAa,GAAE,GAAE;AAAC,MAAG,YAAU,OAAO,KAAG,SAAO,EAAE,QAAO;AAAE,MAAI,IAAE,EAAE,OAAO,WAAW;AAAE,MAAG,WAAS,GAAE;AAAC,QAAI,IAAE,EAAE,KAAK,GAAE,KAAG,SAAS;AAAE,QAAG,YAAU,OAAO,EAAE,QAAO;AAAE,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAC;AAAC,UAAO,aAAW,IAAE,SAAO,QAAQ,CAAC;AAAC;AAAC,SAAS,eAAe,GAAE;AAAC,MAAI,IAAE,aAAa,GAAE,QAAQ;AAAE,SAAM,YAAU,OAAO,IAAE,IAAE,OAAO,CAAC;AAAC;AAAC,SAAS,gBAAgB,GAAE,GAAE;AAAC,MAAG,EAAE,aAAa,GAAG,OAAM,IAAI,UAAU,mCAAmC;AAAC;AAAC,SAAS,kBAAkB,GAAE,GAAE;AAAC,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,QAAI,IAAE,EAAE,CAAC;AAAE,MAAE,aAAW,EAAE,cAAY,OAAG,EAAE,eAAa,MAAG,WAAU,MAAI,EAAE,WAAS,OAAI,OAAO,eAAe,GAAE,EAAE,KAAI,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,aAAa,GAAE,GAAE,GAAE;AAAC,SAAO,KAAG,kBAAkB,EAAE,WAAU,CAAC,GAAE,KAAG,kBAAkB,GAAE,CAAC,GAAE;AAAC;AAAC,SAAS,gBAAgB,GAAE,GAAE,GAAE;AAAC,SAAO,KAAK,IAAE,OAAO,eAAe,GAAE,GAAE,EAAC,OAAM,GAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAE;AAAC;AAAC,SAAS,QAAQ,GAAE,GAAE;AAAC,MAAI,IAAE,OAAO,KAAK,CAAC;AAAE,MAAG,OAAO,uBAAsB;AAAC,QAAI,IAAE,OAAO,sBAAsB,CAAC;AAAE,UAAI,IAAE,EAAE,OAAQ,SAASA,IAAE;AAAC,aAAO,OAAO,yBAAyB,GAAEA,EAAC,EAAE;AAAA,IAAU,CAAE,IAAG,EAAE,KAAK,MAAM,GAAE,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,eAAe,GAAE;AAAC,WAAQ,IAAE,GAAE,IAAE,UAAU,QAAO,KAAI;AAAC,QAAI,IAAE,QAAM,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,QAAE,IAAE,QAAQ,OAAO,CAAC,GAAE,IAAE,EAAE,QAAS,SAASA,IAAE;AAAC,sBAAgB,GAAEA,IAAE,EAAEA,EAAC,CAAC;AAAA,IAAC,CAAE,IAAE,OAAO,4BAA0B,OAAO,iBAAiB,GAAE,OAAO,0BAA0B,CAAC,CAAC,IAAE,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAS,SAASA,IAAE;AAAC,aAAO,eAAe,GAAEA,IAAE,OAAO,yBAAyB,GAAEA,EAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,IAAI,aAAW,EAAC,QAAO,MAAG,YAAW,IAAG,OAAM,KAAE;AAAE,SAAS,UAAU,GAAE,GAAE;AAAC,UAAO,WAAU;AAAC,WAAO,MAAM,KAAK,SAAS,iBAAiB,CAAC,CAAC,EAAE,SAAS,IAAI;AAAA,EAAC,GAAE,KAAK,GAAE,CAAC;AAAC;AAAC,SAAS,QAAQ,GAAE,GAAE;AAAC,MAAG,KAAG,GAAE;AAAC,QAAI,IAAE,IAAI,MAAM,GAAE,EAAC,SAAQ,KAAE,CAAC;AAAE,MAAE,cAAc,CAAC;AAAA,EAAC;AAAC;AAAC,IAAI,mBAAiB,SAAS,GAAE;AAAC,SAAO,QAAM,IAAE,EAAE,cAAY;AAAI;AAAlE,IAAoE,eAAa,SAAS,GAAE,GAAE;AAAC,SAAM,CAAC,EAAE,KAAG,KAAG,aAAa;AAAE;AAA7H,IAA+H,sBAAoB,SAAS,GAAE;AAAC,SAAO,QAAM;AAAC;AAA7K,IAA+K,aAAW,SAAS,GAAE;AAAC,SAAO,iBAAiB,CAAC,MAAI;AAAM;AAAzO,IAA2O,aAAW,SAAS,GAAE;AAAC,SAAO,iBAAiB,CAAC,MAAI,UAAQ,CAAC,OAAO,MAAM,CAAC;AAAC;AAAvT,IAAyT,aAAW,SAAS,GAAE;AAAC,SAAO,iBAAiB,CAAC,MAAI;AAAM;AAAnX,IAAqX,cAAY,SAAS,GAAE;AAAC,SAAO,iBAAiB,CAAC,MAAI;AAAO;AAAjb,IAAmb,eAAa,SAAS,GAAE;AAAC,SAAO,iBAAiB,CAAC,MAAI;AAAQ;AAAjf,IAAmf,YAAU,SAAS,GAAE;AAAC,SAAO,MAAM,QAAQ,CAAC;AAAC;AAAhiB,IAAkiB,eAAa,SAAS,GAAE;AAAC,SAAO,aAAa,GAAE,QAAQ;AAAC;AAA1lB,IAA4lB,cAAY,SAAS,GAAE;AAAC,SAAO,aAAa,GAAE,OAAO;AAAC;AAAlpB,IAAopB,YAAU,SAAS,GAAE;AAAC,SAAO,aAAa,GAAE,KAAK;AAAC;AAAtsB,IAAwsB,YAAU,SAAS,GAAE;AAAC,SAAO,oBAAoB,CAAC,MAAI,WAAW,CAAC,KAAG,UAAU,CAAC,KAAG,aAAa,CAAC,MAAI,CAAC,EAAE,UAAQ,WAAW,CAAC,KAAG,CAAC,OAAO,KAAK,CAAC,EAAE;AAAM;AAA71B,IAA+1B,OAAK,EAAC,iBAAgB,qBAAoB,QAAO,YAAW,QAAO,YAAW,QAAO,YAAW,SAAQ,aAAY,UAAS,cAAa,OAAM,WAAU,UAAS,cAAa,SAAQ,aAAY,OAAM,WAAU,OAAM,UAAS;AAAE,SAAS,iBAAiB,GAAE;AAAC,MAAI,IAAE,GAAG,OAAO,CAAC,EAAE,MAAM,kCAAkC;AAAE,SAAO,IAAE,KAAK,IAAI,IAAG,EAAE,CAAC,IAAE,EAAE,CAAC,EAAE,SAAO,MAAI,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,IAAE,EAAE,IAAE;AAAC;AAAC,SAAS,MAAM,GAAE,GAAE;AAAC,MAAG,IAAE,GAAE;AAAC,QAAI,IAAE,iBAAiB,CAAC;AAAE,WAAO,WAAW,EAAE,QAAQ,CAAC,CAAC;AAAA,EAAC;AAAC,SAAO,KAAK,MAAM,IAAE,CAAC,IAAE;AAAC;AAAC,IAAI,aAAW,WAAU;AAAC,WAAS,EAAE,GAAE,GAAE;AAAC,oBAAgB,MAAK,CAAC,GAAE,KAAK,QAAQ,CAAC,IAAE,KAAK,UAAQ,IAAE,KAAK,OAAO,CAAC,MAAI,KAAK,UAAQ,SAAS,cAAc,CAAC,IAAG,KAAK,QAAQ,KAAK,OAAO,KAAG,KAAK,MAAM,KAAK,QAAQ,UAAU,MAAI,KAAK,SAAO,eAAe,CAAC,GAAE,YAAW,CAAC,GAAE,CAAC,GAAE,KAAK,KAAK;AAAA,EAAE;AAAC,SAAO,aAAa,GAAE,CAAC,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,MAAE,YAAU,KAAK,OAAO,WAAS,KAAK,QAAQ,MAAM,aAAW,QAAO,KAAK,QAAQ,MAAM,mBAAiB,QAAO,KAAK,QAAQ,MAAM,cAAY,iBAAgB,KAAK,UAAU,IAAE,GAAE,KAAK,QAAQ,aAAW;AAAA,EAAK,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,MAAE,YAAU,KAAK,OAAO,WAAS,KAAK,QAAQ,MAAM,aAAW,IAAG,KAAK,QAAQ,MAAM,mBAAiB,IAAG,KAAK,QAAQ,MAAM,cAAY,KAAI,KAAK,UAAU,KAAE,GAAE,KAAK,QAAQ,aAAW;AAAA,EAAK,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,SAASC,IAAE;AAAC,QAAI,IAAE,MAAK,IAAEA,KAAE,qBAAmB;AAAsB,KAAC,cAAa,aAAY,UAAU,EAAE,QAAS,SAASA,IAAE;AAAC,QAAE,QAAQ,CAAC,EAAEA,IAAG,SAASA,IAAE;AAAC,eAAO,EAAE,IAAIA,EAAC;AAAA,MAAC,GAAG,KAAE;AAAA,IAAC,CAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,OAAM,OAAM,SAAS,GAAE;AAAC,QAAG,CAAC,EAAE,WAAS,CAAC,KAAK,MAAM,CAAC,EAAE,QAAO;AAAK,QAAI,GAAE,IAAE,EAAE,QAAO,IAAE,EAAE,eAAe,CAAC,GAAE,IAAE,WAAW,EAAE,aAAa,KAAK,CAAC,KAAG,GAAE,IAAE,WAAW,EAAE,aAAa,KAAK,CAAC,KAAG,KAAI,IAAE,WAAW,EAAE,aAAa,MAAM,CAAC,KAAG,GAAE,IAAE,EAAE,sBAAsB,GAAE,IAAE,MAAI,EAAE,SAAO,KAAK,OAAO,aAAW,KAAG;AAAI,WAAO,KAAG,IAAE,MAAI,EAAE,SAAO,EAAE,UAAQ,EAAE,SAAO,IAAE,IAAE,MAAI,MAAI,IAAE,MAAK,KAAG,IAAE,MAAI,MAAI,IAAE,KAAG,IAAE,KAAG,MAAI,KAAG,KAAG,IAAE,MAAI,IAAG,IAAE,MAAM,IAAE,OAAK,IAAE,IAAG,CAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,OAAM,OAAM,SAAS,GAAE;AAAC,MAAE,WAAS,KAAK,MAAM,CAAC,KAAG,CAAC,EAAE,OAAO,aAAW,EAAE,eAAe,GAAE,EAAE,OAAO,QAAM,KAAK,IAAI,CAAC,GAAE,QAAQ,EAAE,QAAO,eAAa,EAAE,OAAK,WAAS,OAAO;AAAA,EAAE,EAAC,CAAC,GAAE,CAAC,EAAC,KAAI,SAAQ,OAAM,SAAS,GAAE;AAAC,QAAI,IAAE,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC,GAAE,IAAE;AAAK,QAAG,KAAK,MAAM,CAAC,KAAG,KAAK,OAAO,CAAC,IAAE,IAAE,MAAM,KAAK,SAAS,iBAAiB,KAAK,OAAO,CAAC,IAAE,IAAE,qBAAqB,CAAC,IAAE,KAAK,QAAQ,CAAC,IAAE,IAAE,CAAC,CAAC,IAAE,KAAK,SAAS,CAAC,IAAE,IAAE,MAAM,KAAK,CAAC,IAAE,KAAK,MAAM,CAAC,MAAI,IAAE,EAAE,OAAO,KAAK,OAAO,IAAG,KAAK,MAAM,CAAC,EAAE,QAAO;AAAK,QAAI,IAAE,eAAe,CAAC,GAAE,YAAW,CAAC,GAAE,CAAC;AAAE,QAAG,KAAK,OAAO,CAAC,KAAG,EAAE,OAAM;AAAC,UAAI,IAAE,IAAI,iBAAkB,SAASC,IAAE;AAAC,cAAM,KAAKA,EAAC,EAAE,QAAS,SAASA,IAAE;AAAC,gBAAM,KAAKA,GAAE,UAAU,EAAE,QAAS,SAASA,IAAE;AAAC,iBAAK,QAAQA,EAAC,KAAG,UAAUA,IAAE,CAAC,KAAG,IAAI,EAAEA,IAAE,CAAC;AAAA,UAAC,CAAE;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE;AAAE,QAAE,QAAQ,SAAS,MAAK,EAAC,WAAU,MAAG,SAAQ,KAAE,CAAC;AAAA,IAAC;AAAC,WAAO,EAAE,IAAK,SAASF,IAAE;AAAC,aAAO,IAAI,EAAEA,IAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,WAAU,KAAI,WAAU;AAAC,WAAM,kBAAiB,SAAS;AAAA,EAAe,EAAC,CAAC,CAAC,GAAE;AAAC,EAAE;AAAE,IAAM,iBAAe,OAAG,QAAM,IAAE,EAAE,cAAY;AAA9C,IAAmD,aAAW,CAAC,GAAE,MAAI,QAAQ,KAAG,KAAG,aAAa,CAAC;AAAjG,IAAmG,oBAAkB,OAAG,QAAM;AAA9H,IAAgI,WAAS,OAAG,eAAe,CAAC,MAAI;AAAhK,IAAuK,WAAS,OAAG,eAAe,CAAC,MAAI,UAAQ,CAAC,OAAO,MAAM,CAAC;AAA9N,IAAgO,WAAS,OAAG,eAAe,CAAC,MAAI;AAAhQ,IAAuQ,YAAU,OAAG,eAAe,CAAC,MAAI;AAAxS,IAAgT,aAAW,OAAG,cAAY,OAAO;AAAjV,IAAmV,UAAQ,OAAG,MAAM,QAAQ,CAAC;AAA7W,IAA+W,YAAU,OAAG,WAAW,GAAE,OAAO;AAAhZ,IAAkZ,aAAW,OAAG,WAAW,GAAE,QAAQ;AAArb,IAAub,aAAW,OAAG,eAAe,CAAC,MAAI;AAAzd,IAA8d,UAAQ,OAAG,WAAW,GAAE,KAAK;AAA3f,IAA6f,kBAAgB,OAAG,WAAW,GAAE,aAAa;AAA1iB,IAA4iB,QAAM,OAAG,WAAW,GAAE,OAAO,YAAY,KAAG,WAAW,GAAE,OAAO,MAAM;AAAlnB,IAAonB,UAAQ,OAAG,WAAW,GAAE,SAAS,KAAG,CAAC,kBAAkB,CAAC,KAAG,SAAS,EAAE,IAAI;AAA9rB,IAAgsB,YAAU,OAAG,WAAW,GAAE,OAAO,KAAG,WAAW,EAAE,IAAI;AAArvB,IAAuvB,YAAU,OAAG,SAAO,KAAG,YAAU,OAAO,KAAG,MAAI,EAAE,YAAU,YAAU,OAAO,EAAE,SAAO,YAAU,OAAO,EAAE;AAA/1B,IAA62B,UAAQ,OAAG,kBAAkB,CAAC,MAAI,SAAS,CAAC,KAAG,QAAQ,CAAC,KAAG,WAAW,CAAC,MAAI,CAAC,EAAE,UAAQ,SAAS,CAAC,KAAG,CAAC,OAAO,KAAK,CAAC,EAAE;AAAh+B,IAAu+B,QAAM,OAAG;AAAC,MAAG,WAAW,GAAE,OAAO,GAAG,EAAE,QAAM;AAAG,MAAG,CAAC,SAAS,CAAC,EAAE,QAAM;AAAG,MAAI,IAAE;AAAE,IAAE,WAAW,SAAS,KAAG,EAAE,WAAW,UAAU,MAAI,IAAE,UAAU,CAAC;AAAI,MAAG;AAAC,WAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE,QAAQ;AAAA,EAAC,SAAOC,IAAE;AAAC,WAAM;AAAA,EAAE;AAAC;AAAE,IAAI,KAAG,EAAC,iBAAgB,mBAAkB,QAAO,UAAS,QAAO,UAAS,QAAO,UAAS,SAAQ,WAAU,UAAS,YAAW,OAAM,SAAQ,SAAQ,WAAU,UAAS,YAAW,SAAQ,WAAU,UAAS,YAAW,OAAM,SAAQ,eAAc,iBAAgB,KAAI,OAAM,OAAM,SAAQ,SAAQ,WAAU,KAAI,OAAM,OAAM,QAAO;AAAE,IAAM,sBAAoB,MAAI;AAAC,QAAM,IAAE,SAAS,cAAc,MAAM,GAAE,IAAE,EAAC,kBAAiB,uBAAsB,eAAc,iBAAgB,aAAY,iCAAgC,YAAW,gBAAe,GAAE,IAAE,OAAO,KAAK,CAAC,EAAE,KAAM,CAAAD,OAAG,WAAS,EAAE,MAAMA,EAAC,CAAE;AAAE,SAAM,CAAC,CAAC,GAAG,OAAO,CAAC,KAAG,EAAE,CAAC;AAAC,GAAG;AAAE,SAAS,QAAQ,GAAE,GAAE;AAAC,aAAY,MAAI;AAAC,QAAG;AAAC,QAAE,SAAO,MAAG,EAAE,cAAa,EAAE,SAAO;AAAA,IAAE,SAAOC,IAAE;AAAA,IAAC;AAAA,EAAC,GAAG,CAAC;AAAC;AAAC,IAAM,OAAK,QAAQ,OAAO,SAAS,YAAY;AAA/C,IAAiD,SAAO,QAAQ,KAAK,UAAU,SAAS;AAAxF,IAA0F,WAAS,sBAAqB,SAAS,gBAAgB,SAAO,CAAC,QAAQ,KAAK,UAAU,SAAS;AAAzL,IAA2L,WAAS,gBAAgB,KAAK,UAAU,SAAS,KAAG,UAAU,iBAAe;AAAxQ,IAA0Q,WAAS,eAAa,UAAU,YAAU,UAAU,iBAAe;AAA7U,IAA+U,QAAM,qBAAqB,KAAK,UAAU,SAAS,KAAG,UAAU,iBAAe;AAAE,IAAI,UAAQ,EAAC,MAAU,QAAc,UAAkB,UAAkB,UAAkB,MAAW;AAAE,SAAS,UAAU,GAAE;AAAC,SAAO,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC;AAAC;AAAC,SAAS,QAAQ,GAAE,GAAE;AAAC,SAAO,EAAE,MAAM,GAAG,EAAE,OAAQ,CAACA,IAAED,OAAIC,MAAGA,GAAED,EAAC,GAAG,CAAC;AAAC;AAAC,SAAS,OAAO,IAAE,CAAC,MAAK,GAAE;AAAC,MAAG,CAAC,EAAE,OAAO,QAAO;AAAE,QAAM,IAAE,EAAE,MAAM;AAAE,SAAO,GAAG,OAAO,CAAC,KAAG,OAAO,KAAK,CAAC,EAAE,QAAS,CAAAA,OAAG;AAAC,OAAG,OAAO,EAAEA,EAAC,CAAC,KAAG,OAAO,KAAK,CAAC,EAAE,SAASA,EAAC,KAAG,OAAO,OAAO,GAAE,EAAC,CAACA,EAAC,GAAE,CAAC,EAAC,CAAC,GAAE,OAAO,EAAEA,EAAC,GAAE,EAAEA,EAAC,CAAC,KAAG,OAAO,OAAO,GAAE,EAAC,CAACA,EAAC,GAAE,EAAEA,EAAC,EAAC,CAAC;AAAA,EAAC,CAAE,GAAE,OAAO,GAAE,GAAG,CAAC,KAAG;AAAC;AAAC,SAAS,KAAK,GAAE,GAAE;AAAC,QAAM,IAAE,EAAE,SAAO,IAAE,CAAC,CAAC;AAAE,QAAM,KAAK,CAAC,EAAE,QAAQ,EAAE,QAAS,CAACC,IAAEC,OAAI;AAAC,UAAM,IAAEA,KAAE,IAAE,EAAE,UAAU,IAAE,IAAE,GAAE,IAAED,GAAE,YAAW,IAAEA,GAAE;AAAY,MAAE,YAAYA,EAAC,GAAE,IAAE,EAAE,aAAa,GAAE,CAAC,IAAE,EAAE,YAAY,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,cAAc,GAAE,GAAE;AAAC,KAAG,QAAQ,CAAC,KAAG,CAAC,GAAG,MAAM,CAAC,KAAG,OAAO,QAAQ,CAAC,EAAE,OAAQ,CAAC,CAAC,EAACA,EAAC,MAAI,CAAC,GAAG,gBAAgBA,EAAC,CAAE,EAAE,QAAS,CAAC,CAACD,IAAE,CAAC,MAAI,EAAE,aAAaA,IAAE,CAAC,CAAE;AAAC;AAAC,SAAS,cAAc,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,SAAS,cAAc,CAAC;AAAE,SAAO,GAAG,OAAO,CAAC,KAAG,cAAc,GAAE,CAAC,GAAE,GAAG,OAAO,CAAC,MAAI,EAAE,YAAU,IAAG;AAAC;AAAC,SAAS,YAAY,GAAE,GAAE;AAAC,KAAG,QAAQ,CAAC,KAAG,GAAG,QAAQ,CAAC,KAAG,EAAE,WAAW,aAAa,GAAE,EAAE,WAAW;AAAC;AAAC,SAAS,cAAc,GAAE,GAAE,GAAE,GAAE;AAAC,KAAG,QAAQ,CAAC,KAAG,EAAE,YAAY,cAAc,GAAE,GAAE,CAAC,CAAC;AAAC;AAAC,SAAS,cAAc,GAAE;AAAC,KAAG,SAAS,CAAC,KAAG,GAAG,MAAM,CAAC,IAAE,MAAM,KAAK,CAAC,EAAE,QAAQ,aAAa,IAAE,GAAG,QAAQ,CAAC,KAAG,GAAG,QAAQ,EAAE,UAAU,KAAG,EAAE,WAAW,YAAY,CAAC;AAAC;AAAC,SAAS,aAAa,GAAE;AAAC,MAAG,CAAC,GAAG,QAAQ,CAAC,EAAE;AAAO,MAAG,EAAC,QAAO,EAAC,IAAE,EAAE;AAAW,SAAK,IAAE,IAAG,GAAE,YAAY,EAAE,SAAS,GAAE,KAAG;AAAC;AAAC,SAAS,eAAe,GAAE,GAAE;AAAC,SAAO,GAAG,QAAQ,CAAC,KAAG,GAAG,QAAQ,EAAE,UAAU,KAAG,GAAG,QAAQ,CAAC,KAAG,EAAE,WAAW,aAAa,GAAE,CAAC,GAAE,KAAG;AAAI;AAAC,SAAS,0BAA0B,GAAE,GAAE;AAAC,MAAG,CAAC,GAAG,OAAO,CAAC,KAAG,GAAG,MAAM,CAAC,EAAE,QAAM,CAAC;AAAE,QAAM,IAAE,CAAC,GAAE,IAAE,OAAO,CAAC,GAAE,CAAC;AAAE,SAAO,EAAE,MAAM,GAAG,EAAE,QAAS,CAAAC,OAAG;AAAC,UAAMD,KAAEC,GAAE,KAAK,GAAE,IAAED,GAAE,QAAQ,KAAI,EAAE,GAAE,IAAEA,GAAE,QAAQ,UAAS,EAAE,EAAE,MAAM,GAAG,GAAE,CAAC,CAAC,IAAE,GAAE,IAAE,EAAE,SAAO,IAAE,EAAE,CAAC,EAAE,QAAQ,SAAQ,EAAE,IAAE;AAAG,YAAOA,GAAE,OAAO,CAAC,GAAE;AAAA,MAAC,KAAI;AAAI,WAAG,OAAO,EAAE,KAAK,IAAE,EAAE,QAAM,GAAG,EAAE,KAAK,IAAI,CAAC,KAAG,EAAE,QAAM;AAAE;AAAA,MAAM,KAAI;AAAI,UAAE,KAAGA,GAAE,QAAQ,KAAI,EAAE;AAAE;AAAA,MAAM,KAAI;AAAI,UAAE,CAAC,IAAE;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,OAAO,GAAE,CAAC;AAAC;AAAC,SAAS,aAAa,GAAE,GAAE;AAAC,MAAG,CAAC,GAAG,QAAQ,CAAC,EAAE;AAAO,MAAI,IAAE;AAAE,KAAG,QAAQ,CAAC,MAAI,IAAE,CAAC,EAAE,SAAQ,EAAE,SAAO;AAAC;AAAC,SAAS,YAAY,GAAE,GAAE,GAAE;AAAC,MAAG,GAAG,SAAS,CAAC,EAAE,QAAO,MAAM,KAAK,CAAC,EAAE,IAAK,CAAAC,OAAG,YAAYA,IAAE,GAAE,CAAC,CAAE;AAAE,MAAG,GAAG,QAAQ,CAAC,GAAE;AAAC,QAAI,IAAE;AAAS,WAAO,WAAS,MAAI,IAAE,IAAE,QAAM,WAAU,EAAE,UAAU,CAAC,EAAE,CAAC,GAAE,EAAE,UAAU,SAAS,CAAC;AAAA,EAAC;AAAC,SAAM;AAAE;AAAC,SAAS,SAAS,GAAE,GAAE;AAAC,SAAO,GAAG,QAAQ,CAAC,KAAG,EAAE,UAAU,SAAS,CAAC;AAAC;AAAC,SAAS,QAAQ,GAAE,GAAE;AAAC,QAAK,EAAC,WAAU,EAAC,IAAE;AAAQ,UAAO,EAAE,WAAS,EAAE,yBAAuB,EAAE,sBAAoB,EAAE,qBAAmB,WAAU;AAAC,WAAO,MAAM,KAAK,SAAS,iBAAiB,CAAC,CAAC,EAAE,SAAS,IAAI;AAAA,EAAC,GAAG,KAAK,GAAE,CAAC;AAAC;AAAC,SAAS,UAAU,GAAE,GAAE;AAAC,QAAK,EAAC,WAAU,EAAC,IAAE;AAAQ,UAAO,EAAE,WAAS,WAAU;AAAC,QAAIA,KAAE;AAAK,OAAE;AAAC,UAAG,QAAQ,QAAQA,IAAE,CAAC,EAAE,QAAOA;AAAE,MAAAA,KAAEA,GAAE,iBAAeA,GAAE;AAAA,IAAU,SAAO,SAAOA,MAAG,MAAIA,GAAE;AAAU,WAAO;AAAA,EAAI,GAAG,KAAK,GAAE,CAAC;AAAC;AAAC,SAAS,YAAY,GAAE;AAAC,SAAO,KAAK,SAAS,UAAU,iBAAiB,CAAC;AAAC;AAAC,SAAS,WAAW,GAAE;AAAC,SAAO,KAAK,SAAS,UAAU,cAAc,CAAC;AAAC;AAAC,SAAS,SAAS,IAAE,MAAK,IAAE,OAAG;AAAC,KAAG,QAAQ,CAAC,KAAG,EAAE,MAAM,EAAC,eAAc,MAAG,cAAa,EAAC,CAAC;AAAC;AAAC,IAAM,gBAAc,EAAC,aAAY,UAAS,aAAY,KAAI,cAAa,eAAc,aAAY,0BAAyB,aAAY,SAAQ;AAA9I,IAAgJ,UAAQ,EAAC,OAAM,iBAAgB,SAAS,cAAc,OAAO,GAAE,OAAM,iBAAgB,SAAS,cAAc,OAAO,GAAE,MAAM,GAAE,GAAE;AAAC,QAAM,IAAE,QAAQ,CAAC,KAAG,YAAU;AAAE,SAAM,EAAC,KAAI,GAAE,IAAG,KAAG,QAAQ,WAAU;AAAC,GAAE,KAAI,EAAE,QAAQ,YAAU,CAAC,GAAG,SAAS,cAAc,OAAO,EAAE,yBAAyB,MAAI,CAAC,SAAS,2BAAyB,cAAc,OAAO,EAAE,2BAA0B,SAAQ,GAAG,SAAS,OAAO,qCAAqC,GAAE,aAAY,iBAAgB,SAAS,cAAc,OAAO,GAAE,KAAK,GAAE;AAAC,MAAG,GAAG,MAAM,CAAC,EAAE,QAAM;AAAG,QAAK,CAAC,CAAC,IAAE,EAAE,MAAM,GAAG;AAAE,MAAI,IAAE;AAAE,MAAG,CAAC,KAAK,WAAS,MAAI,KAAK,KAAK,QAAM;AAAG,SAAO,KAAK,aAAa,EAAE,SAAS,CAAC,MAAI,KAAG,aAAa,cAAc,CAAC,CAAC;AAAK,MAAG;AAAC,WAAO,QAAQ,KAAG,KAAK,MAAM,YAAY,CAAC,EAAE,QAAQ,MAAK,EAAE,CAAC;AAAA,EAAC,SAAOA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAC,GAAE,YAAW,gBAAe,SAAS,cAAc,OAAO,GAAE,aAAY,MAAI;AAAC,QAAM,IAAE,SAAS,cAAc,OAAO;AAAE,SAAO,EAAE,OAAK,SAAQ,YAAU,EAAE;AAAI,GAAG,GAAE,OAAM,kBAAiB,SAAS,iBAAgB,aAAY,UAAK,oBAAmB,eAAc,gBAAe,UAAQ,OAAO,WAAW,0BAA0B,EAAE,QAAO;AAA1tC,IAA4tC,4BAA0B,MAAI;AAAC,MAAI,IAAE;AAAG,MAAG;AAAC,UAAM,IAAE,OAAO,eAAe,CAAC,GAAE,WAAU,EAAC,KAAI,OAAK,IAAE,MAAG,MAAK,CAAC;AAAE,WAAO,iBAAiB,QAAO,MAAK,CAAC,GAAE,OAAO,oBAAoB,QAAO,MAAK,CAAC;AAAA,EAAC,SAAOA,IAAE;AAAA,EAAC;AAAC,SAAO;AAAC,GAAG;AAAE,SAAS,eAAe,GAAE,GAAE,GAAE,IAAE,OAAG,IAAE,MAAG,IAAE,OAAG;AAAC,MAAG,CAAC,KAAG,EAAE,sBAAqB,MAAI,GAAG,MAAM,CAAC,KAAG,CAAC,GAAG,SAAS,CAAC,EAAE;AAAO,QAAM,IAAE,EAAE,MAAM,GAAG;AAAE,MAAI,IAAE;AAAE,+BAA2B,IAAE,EAAC,SAAQ,GAAE,SAAQ,EAAC,IAAG,EAAE,QAAS,CAAAD,OAAG;AAAC,YAAM,KAAK,kBAAgB,KAAG,KAAK,eAAe,KAAK,EAAC,SAAQ,GAAE,MAAKA,IAAE,UAAS,GAAE,SAAQ,EAAC,CAAC,GAAE,EAAE,IAAE,qBAAmB,qBAAqB,EAAEA,IAAE,GAAE,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAG,GAAE,IAAE,IAAG,GAAE,IAAE,MAAG,IAAE,OAAG;AAAC,iBAAe,KAAK,MAAK,GAAE,GAAE,GAAE,MAAG,GAAE,CAAC;AAAC;AAAC,SAAS,IAAI,GAAE,IAAE,IAAG,GAAE,IAAE,MAAG,IAAE,OAAG;AAAC,iBAAe,KAAK,MAAK,GAAE,GAAE,GAAE,OAAG,GAAE,CAAC;AAAC;AAAC,SAAS,KAAK,GAAE,IAAE,IAAG,GAAE,IAAE,MAAG,IAAE,OAAG;AAAC,QAAM,IAAE,IAAI,MAAI;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,MAAM,MAAK,CAAC;AAAA,EAAC;AAAE,iBAAe,KAAK,MAAK,GAAE,GAAE,GAAE,MAAG,GAAE,CAAC;AAAC;AAAC,SAAS,aAAa,GAAE,IAAE,IAAG,IAAE,OAAG,IAAE,CAAC,GAAE;AAAC,MAAG,CAAC,GAAG,QAAQ,CAAC,KAAG,GAAG,MAAM,CAAC,EAAE;AAAO,QAAM,IAAE,IAAI,YAAY,GAAE,EAAC,SAAQ,GAAE,QAAO,EAAC,GAAG,GAAE,MAAK,KAAI,EAAC,CAAC;AAAE,IAAE,cAAc,CAAC;AAAC;AAAC,SAAS,kBAAiB;AAAC,UAAM,KAAK,mBAAiB,KAAK,eAAe,QAAS,OAAG;AAAC,UAAK,EAAC,SAAQ,GAAE,MAAK,GAAE,UAAS,GAAE,SAAQ,EAAC,IAAE;AAAE,MAAE,oBAAoB,GAAE,GAAE,CAAC;AAAA,EAAC,CAAE,GAAE,KAAK,iBAAe,CAAC;AAAE;AAAC,SAAS,QAAO;AAAC,SAAO,IAAI,QAAS,OAAG,KAAK,QAAM,WAAW,GAAE,CAAC,IAAE,GAAG,KAAK,MAAK,KAAK,SAAS,WAAU,SAAQ,CAAC,CAAE,EAAE,KAAM,MAAI;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,eAAe,GAAE;AAAC,KAAG,QAAQ,CAAC,KAAG,EAAE,KAAK,MAAM,MAAI;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,OAAO,GAAE;AAAC,SAAO,GAAG,MAAM,CAAC,IAAE,EAAE,OAAQ,CAAC,GAAE,MAAI,EAAE,QAAQ,CAAC,MAAI,CAAE,IAAE;AAAC;AAAC,SAAS,QAAQ,GAAE,GAAE;AAAC,SAAO,GAAG,MAAM,CAAC,KAAG,EAAE,SAAO,EAAE,OAAQ,CAACC,IAAE,MAAI,KAAK,IAAI,IAAE,CAAC,IAAE,KAAK,IAAIA,KAAE,CAAC,IAAE,IAAEA,EAAE,IAAE;AAAI;AAAC,SAAS,YAAY,GAAE;AAAC,SAAM,EAAE,CAAC,UAAQ,CAAC,OAAO,QAAM,OAAO,IAAI,SAAS,CAAC;AAAC;AAAC,IAAM,iBAAe,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,CAAC,EAAE,OAAQ,CAAC,GAAE,CAAC,GAAE,CAAC,OAAK,EAAC,GAAG,GAAE,CAAC,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,EAAC,IAAI,CAAC,CAAC;AAAE,SAAS,oBAAoB,GAAE;AAAC,MAAG,EAAE,GAAG,MAAM,CAAC,KAAG,GAAG,OAAO,CAAC,KAAG,EAAE,SAAS,GAAG,GAAG,QAAM;AAAG,UAAO,GAAG,MAAM,CAAC,IAAE,IAAE,EAAE,MAAM,GAAG,GAAG,IAAI,MAAM,EAAE,MAAM,GAAG,MAAM;AAAC;AAAC,SAAS,kBAAkB,GAAE;AAAC,MAAG,CAAC,GAAG,MAAM,CAAC,KAAG,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,QAAO;AAAK,QAAK,CAAC,GAAE,CAAC,IAAE,GAAE,IAAE,CAACA,IAAED,OAAI,MAAIA,KAAEC,KAAE,EAAED,IAAEC,KAAED,EAAC,GAAE,IAAE,EAAE,GAAE,CAAC;AAAE,SAAM,CAAC,IAAE,GAAE,IAAE,CAAC;AAAC;AAAC,SAAS,eAAe,GAAE;AAAC,QAAM,IAAE,CAAAC,OAAG,oBAAoBA,EAAC,IAAEA,GAAE,MAAM,GAAG,EAAE,IAAI,MAAM,IAAE;AAAK,MAAI,IAAE,EAAE,CAAC;AAAE,MAAG,SAAO,MAAI,IAAE,EAAE,KAAK,OAAO,KAAK,IAAG,SAAO,KAAG,CAAC,GAAG,MAAM,KAAK,KAAK,KAAG,GAAG,MAAM,KAAK,MAAM,KAAK,MAAI,EAAC,OAAM,EAAC,IAAE,KAAK,QAAO,SAAO,KAAG,KAAK,SAAQ;AAAC,UAAK,EAAC,YAAWA,IAAE,aAAYD,GAAC,IAAE,KAAK;AAAM,QAAE,CAACC,IAAED,EAAC;AAAA,EAAC;AAAC,SAAO,kBAAkB,CAAC;AAAC;AAAC,SAAS,eAAe,GAAE;AAAC,MAAG,CAAC,KAAK,QAAQ,QAAM,CAAC;AAAE,QAAK,EAAC,SAAQ,EAAC,IAAE,KAAK,UAAS,IAAE,eAAe,KAAK,MAAK,CAAC;AAAE,MAAG,CAAC,GAAG,MAAM,CAAC,EAAE,QAAM,CAAC;AAAE,QAAK,CAAC,GAAE,CAAC,IAAE,kBAAkB,CAAC,GAAE,IAAE,MAAI,IAAE;AAAE,MAAG,YAAY,iBAAiB,CAAC,IAAI,CAAC,EAAE,IAAE,EAAE,MAAM,cAAY,GAAG,CAAC,IAAI,CAAC,KAAG,EAAE,MAAM,gBAAc,GAAG,CAAC,KAAI,KAAK,WAAS,CAAC,KAAK,OAAO,MAAM,WAAS,KAAK,UAAU,IAAG;AAAC,UAAMC,KAAE,MAAI,KAAK,MAAM,cAAY,SAAS,OAAO,iBAAiB,KAAK,KAAK,EAAE,eAAc,EAAE,GAAEC,MAAGD,KAAE,MAAIA,KAAE;AAAI,SAAK,WAAW,SAAO,EAAE,MAAM,gBAAc,OAAK,KAAK,MAAM,MAAM,YAAU,eAAeC,EAAC;AAAA,EAAI,MAAM,MAAK,WAAS,EAAE,UAAU,IAAI,KAAK,OAAO,WAAW,eAAe;AAAE,SAAM,EAAC,SAAQ,GAAE,OAAM,EAAC;AAAC;AAAC,SAAS,iBAAiB,GAAE,GAAE,IAAE,MAAI;AAAC,QAAM,IAAE,IAAE,GAAE,IAAE,QAAQ,OAAO,KAAK,cAAc,GAAE,CAAC;AAAE,SAAO,KAAK,IAAI,IAAE,CAAC,KAAG,IAAE,eAAe,CAAC,IAAE,CAAC,GAAE,CAAC;AAAC;AAAC,SAAS,kBAAiB;AAAC,SAAM,CAAC,KAAK,IAAI,SAAS,gBAAgB,eAAa,GAAE,OAAO,cAAY,CAAC,GAAE,KAAK,IAAI,SAAS,gBAAgB,gBAAc,GAAE,OAAO,eAAa,CAAC,CAAC;AAAC;AAAC,IAAM,QAAM,EAAC,aAAY;AAAC,MAAG,CAAC,KAAK,QAAQ,QAAM,CAAC;AAAE,SAAO,MAAM,KAAK,KAAK,MAAM,iBAAiB,QAAQ,CAAC,EAAE,OAAQ,OAAG;AAAC,UAAM,IAAE,EAAE,aAAa,MAAM;AAAE,WAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAG,QAAQ,KAAK,KAAK,MAAK,CAAC;AAAA,EAAC,CAAE;AAAC,GAAE,oBAAmB;AAAC,SAAO,KAAK,OAAO,QAAQ,SAAO,KAAK,OAAO,QAAQ,UAAQ,MAAM,WAAW,KAAK,IAAI,EAAE,IAAK,OAAG,OAAO,EAAE,aAAa,MAAM,CAAC,CAAE,EAAE,OAAO,OAAO;AAAC,GAAE,QAAO;AAAC,MAAG,CAAC,KAAK,QAAQ;AAAO,QAAM,IAAE;AAAK,IAAE,QAAQ,QAAM,EAAE,OAAO,MAAM,SAAQ,GAAG,MAAM,KAAK,OAAO,KAAK,KAAG,eAAe,KAAK,CAAC,GAAE,OAAO,eAAe,EAAE,OAAM,WAAU,EAAC,MAAK;AAAC,UAAM,IAAE,MAAM,WAAW,KAAK,CAAC,EAAE,KAAM,CAAAF,OAAGA,GAAE,aAAa,KAAK,MAAI,EAAE,MAAO;AAAE,WAAO,KAAG,OAAO,EAAE,aAAa,MAAM,CAAC;AAAA,EAAC,GAAE,IAAI,GAAE;AAAC,QAAG,EAAE,YAAU,GAAE;AAAC,UAAG,EAAE,OAAO,QAAQ,UAAQ,GAAG,SAAS,EAAE,OAAO,QAAQ,QAAQ,EAAE,GAAE,OAAO,QAAQ,SAAS,CAAC;AAAA,WAAM;AAAC,cAAM,IAAE,MAAM,WAAW,KAAK,CAAC,EAAE,KAAM,CAAAC,OAAG,OAAOA,GAAE,aAAa,MAAM,CAAC,MAAI,CAAE;AAAE,YAAG,CAAC,EAAE;AAAO,cAAK,EAAC,aAAY,GAAE,QAAO,GAAE,SAAQ,GAAE,YAAW,GAAE,cAAa,EAAC,IAAE,EAAE;AAAM,UAAE,MAAM,MAAI,EAAE,aAAa,KAAK,IAAG,WAAS,KAAG,OAAK,EAAE,KAAK,kBAAkB,MAAI;AAAC,YAAE,QAAM,GAAE,EAAE,cAAY,GAAE,KAAG,eAAe,EAAE,KAAK,CAAC;AAAA,QAAC,CAAE,GAAE,EAAE,MAAM,KAAK;AAAA,MAAE;AAAC,mBAAa,KAAK,GAAE,EAAE,OAAM,iBAAgB,OAAG,EAAC,SAAQ,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC,EAAC,CAAC;AAAC,GAAE,iBAAgB;AAAC,OAAK,YAAU,cAAc,MAAM,WAAW,KAAK,IAAI,CAAC,GAAE,KAAK,MAAM,aAAa,OAAM,KAAK,OAAO,UAAU,GAAE,KAAK,MAAM,KAAK,GAAE,KAAK,MAAM,IAAI,4BAA4B;AAAE,EAAC;AAAE,SAAS,WAAW,GAAE;AAAC,SAAM,GAAG,CAAC,IAAI,KAAK,MAAM,MAAI,KAAK,OAAO,CAAC,CAAC;AAAE;AAAC,SAAS,OAAO,MAAK,GAAE;AAAC,SAAO,GAAG,MAAM,CAAC,IAAE,IAAE,EAAE,SAAS,EAAE,QAAQ,YAAY,CAACA,IAAE,MAAI,EAAE,CAAC,EAAE,SAAS,CAAE;AAAC;AAAC,SAAS,cAAc,GAAE,GAAE;AAAC,SAAO,MAAI,KAAG,MAAI,KAAG,OAAO,MAAM,CAAC,KAAG,OAAO,MAAM,CAAC,IAAE,KAAG,IAAE,IAAE,KAAK,QAAQ,CAAC;AAAC;AAAC,IAAM,aAAW,CAAC,IAAE,IAAG,IAAE,IAAG,IAAE,OAAK,EAAE,QAAQ,IAAI,OAAO,EAAE,SAAS,EAAE,QAAQ,6BAA4B,MAAM,GAAE,GAAG,GAAE,EAAE,SAAS,CAAC;AAAlI,IAAoI,cAAY,CAAC,IAAE,OAAK,EAAE,SAAS,EAAE,QAAQ,UAAU,CAAAA,OAAGA,GAAE,OAAO,CAAC,EAAE,YAAY,IAAEA,GAAE,MAAM,CAAC,EAAE,YAAY,CAAE;AAAE,SAAS,aAAa,IAAE,IAAG;AAAC,MAAI,IAAE,EAAE,SAAS;AAAE,SAAO,IAAE,WAAW,GAAE,KAAI,GAAG,GAAE,IAAE,WAAW,GAAE,KAAI,GAAG,GAAE,IAAE,YAAY,CAAC,GAAE,WAAW,GAAE,KAAI,EAAE;AAAC;AAAC,SAAS,YAAY,IAAE,IAAG;AAAC,MAAI,IAAE,EAAE,SAAS;AAAE,SAAO,IAAE,aAAa,CAAC,GAAE,EAAE,OAAO,CAAC,EAAE,YAAY,IAAE,EAAE,MAAM,CAAC;AAAC;AAAC,SAAS,UAAU,GAAE;AAAC,QAAM,IAAE,SAAS,uBAAuB,GAAE,IAAE,SAAS,cAAc,KAAK;AAAE,SAAO,EAAE,YAAY,CAAC,GAAE,EAAE,YAAU,GAAE,EAAE,WAAW;AAAS;AAAC,SAAS,QAAQ,GAAE;AAAC,QAAM,IAAE,SAAS,cAAc,KAAK;AAAE,SAAO,EAAE,YAAY,CAAC,GAAE,EAAE;AAAS;AAAC,IAAM,YAAU,EAAC,KAAI,OAAM,SAAQ,WAAU,OAAM,SAAQ,OAAM,SAAQ,SAAQ,UAAS;AAA1F,IAA4F,OAAK,EAAC,IAAI,IAAE,IAAG,IAAE,CAAC,GAAE;AAAC,MAAG,GAAG,MAAM,CAAC,KAAG,GAAG,MAAM,CAAC,EAAE,QAAM;AAAG,MAAI,IAAE,QAAQ,EAAE,MAAK,CAAC;AAAE,MAAG,GAAG,MAAM,CAAC,EAAE,QAAO,OAAO,KAAK,SAAS,EAAE,SAAS,CAAC,IAAE,UAAU,CAAC,IAAE;AAAG,QAAM,IAAE,EAAC,cAAa,EAAE,UAAS,WAAU,EAAE,MAAK;AAAE,SAAO,OAAO,QAAQ,CAAC,EAAE,QAAS,CAAC,CAACA,IAAED,EAAC,MAAI;AAAC,QAAE,WAAW,GAAEC,IAAED,EAAC;AAAA,EAAC,CAAE,GAAE;AAAC,EAAC;AAAE,IAAM,UAAN,MAAM,SAAO;AAAA,EAAC,YAAY,GAAE;AAAC,sBAAkB,MAAK,OAAO,CAAAC,OAAG;AAAC,UAAG,CAAC,SAAQ,aAAW,CAAC,KAAK,QAAQ,QAAO;AAAK,YAAM,IAAE,OAAO,aAAa,QAAQ,KAAK,GAAG;AAAE,UAAG,GAAG,MAAM,CAAC,EAAE,QAAO;AAAK,YAAM,IAAE,KAAK,MAAM,CAAC;AAAE,aAAO,GAAG,OAAOA,EAAC,KAAGA,GAAE,SAAO,EAAEA,EAAC,IAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,OAAO,CAAAA,OAAG;AAAC,UAAG,CAAC,SAAQ,aAAW,CAAC,KAAK,QAAQ;AAAO,UAAG,CAAC,GAAG,OAAOA,EAAC,EAAE;AAAO,UAAI,IAAE,KAAK,IAAI;AAAE,SAAG,MAAM,CAAC,MAAI,IAAE,CAAC,IAAG,OAAO,GAAEA,EAAC;AAAE,UAAG;AAAC,eAAO,aAAa,QAAQ,KAAK,KAAI,KAAK,UAAU,CAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,KAAK,UAAQ,EAAE,OAAO,QAAQ,SAAQ,KAAK,MAAI,EAAE,OAAO,QAAQ;AAAA,EAAG;AAAA,EAAC,WAAW,YAAW;AAAC,QAAG;AAAC,UAAG,EAAE,kBAAiB,QAAQ,QAAM;AAAG,YAAM,IAAE;AAAU,aAAO,OAAO,aAAa,QAAQ,GAAE,CAAC,GAAE,OAAO,aAAa,WAAW,CAAC,GAAE;AAAA,IAAE,SAAO,GAAE;AAAC,aAAM;AAAA,IAAE;AAAA,EAAC;AAAC;AAAC,SAAS,MAAM,GAAE,IAAE,QAAO;AAAC,SAAO,IAAI,QAAS,CAAC,GAAE,MAAI;AAAC,QAAG;AAAC,YAAME,KAAE,IAAI;AAAe,UAAG,EAAE,qBAAoBA,IAAG;AAAO,MAAAA,GAAE,iBAAiB,QAAQ,MAAI;AAAC,YAAG,WAAS,EAAE,KAAG;AAAC,YAAE,KAAK,MAAMA,GAAE,YAAY,CAAC;AAAA,QAAC,SAAOF,IAAE;AAAC,YAAEE,GAAE,YAAY;AAAA,QAAC;AAAA,YAAM,GAAEA,GAAE,QAAQ;AAAA,MAAC,CAAE,GAAEA,GAAE,iBAAiB,SAAS,MAAI;AAAC,cAAM,IAAI,MAAMA,GAAE,MAAM;AAAA,MAAC,CAAE,GAAEA,GAAE,KAAK,OAAM,GAAE,IAAE,GAAEA,GAAE,eAAa,GAAEA,GAAE,KAAK;AAAA,IAAC,SAAOF,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,WAAW,GAAE,GAAE;AAAC,MAAG,CAAC,GAAG,OAAO,CAAC,EAAE;AAAO,QAAM,IAAE,SAAQ,IAAE,GAAG,OAAO,CAAC;AAAE,MAAI,IAAE;AAAG,QAAM,IAAE,MAAI,SAAO,SAAS,eAAe,CAAC,GAAE,IAAE,CAACA,IAAED,OAAI;AAAC,IAAAC,GAAE,YAAUD,IAAE,KAAG,EAAE,KAAG,SAAS,KAAK,sBAAsB,cAAaC,EAAC;AAAA,EAAC;AAAE,MAAG,CAAC,KAAG,CAAC,EAAE,GAAE;AAAC,UAAMG,KAAE,QAAQ,WAAU,IAAE,SAAS,cAAc,KAAK;AAAE,QAAG,EAAE,aAAa,UAAS,EAAE,GAAE,KAAG,EAAE,aAAa,MAAK,CAAC,GAAEA,IAAE;AAAC,YAAMH,KAAE,OAAO,aAAa,QAAQ,GAAG,CAAC,IAAI,CAAC,EAAE;AAAE,UAAG,IAAE,SAAOA,IAAE,GAAE;AAAC,cAAMD,KAAE,KAAK,MAAMC,EAAC;AAAE,UAAE,GAAED,GAAE,OAAO;AAAA,MAAC;AAAA,IAAC;AAAC,UAAM,CAAC,EAAE,KAAM,CAAAC,OAAG;AAAC,UAAG,CAAC,GAAG,MAAMA,EAAC,GAAE;AAAC,YAAGG,GAAE,KAAG;AAAC,iBAAO,aAAa,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAG,KAAK,UAAU,EAAC,SAAQH,GAAC,CAAC,CAAC;AAAA,QAAC,SAAOA,IAAE;AAAA,QAAC;AAAC,UAAE,GAAEA,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE,EAAE,MAAO,MAAI;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;AAAC,IAAM,WAAS,OAAG,KAAK,MAAM,IAAE,KAAG,KAAG,IAAG,EAAE;AAA1C,IAA4C,aAAW,OAAG,KAAK,MAAM,IAAE,KAAG,IAAG,EAAE;AAA/E,IAAiF,aAAW,OAAG,KAAK,MAAM,IAAE,IAAG,EAAE;AAAE,SAAS,WAAW,IAAE,GAAE,IAAE,OAAG,IAAE,OAAG;AAAC,MAAG,CAAC,GAAG,OAAO,CAAC,EAAE,QAAO,WAAW,QAAO,GAAE,CAAC;AAAE,QAAM,IAAE,CAAAA,OAAG,IAAIA,EAAC,GAAG,MAAM,EAAE;AAAE,MAAI,IAAE,SAAS,CAAC;AAAE,QAAM,IAAE,WAAW,CAAC,GAAE,IAAE,WAAW,CAAC;AAAE,SAAO,IAAE,KAAG,IAAE,IAAE,GAAG,CAAC,MAAI,IAAG,GAAG,KAAG,IAAE,IAAE,MAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAAE;AAAC,IAAM,WAAS,EAAC,aAAY;AAAC,QAAM,IAAE,IAAI,IAAI,KAAK,OAAO,SAAQ,OAAO,QAAQ,GAAE,IAAE,OAAO,SAAS,OAAK,OAAO,SAAS,OAAK,OAAO,IAAI,SAAS,MAAK,IAAE,EAAE,SAAO,KAAG,QAAQ,QAAM,CAAC,OAAO;AAAc,SAAM,EAAC,KAAI,KAAK,OAAO,SAAQ,MAAK,EAAC;AAAC,GAAE,eAAc;AAAC,MAAG;AAAC,WAAO,KAAK,SAAS,WAAS,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,SAAS,OAAO,GAAE,KAAK,SAAS,UAAQ,EAAC,MAAK,YAAY,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,IAAI,GAAE,OAAM,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,KAAK,GAAE,SAAQ,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,OAAO,GAAE,QAAO,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,MAAM,GAAE,aAAY,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,WAAW,GAAE,MAAK,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,IAAI,GAAE,KAAI,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,GAAG,GAAE,SAAQ,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,OAAO,GAAE,UAAS,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,QAAQ,GAAE,UAAS,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,QAAQ,GAAE,YAAW,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,UAAU,EAAC,GAAE,KAAK,SAAS,WAAS,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,GAAE,KAAK,SAAS,SAAO,EAAC,MAAK,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,OAAO,IAAI,GAAE,QAAO,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,OAAO,MAAM,EAAC,GAAE,KAAK,SAAS,UAAQ,EAAC,QAAO,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,MAAM,GAAE,aAAY,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,WAAW,GAAE,UAAS,WAAW,KAAK,MAAK,KAAK,OAAO,UAAU,QAAQ,QAAQ,EAAC,GAAE,GAAG,QAAQ,KAAK,SAAS,QAAQ,MAAI,KAAK,SAAS,QAAQ,cAAY,KAAK,SAAS,SAAS,cAAc,IAAI,KAAK,OAAO,WAAW,OAAO,EAAE,IAAG;AAAA,EAAE,SAAO,GAAE;AAAC,WAAO,KAAK,MAAM,KAAK,mEAAkE,CAAC,GAAE,KAAK,qBAAqB,IAAE,GAAE;AAAA,EAAE;AAAC,GAAE,WAAW,GAAE,GAAE;AAAC,QAAM,IAAE,8BAA6B,IAAE,SAAS,WAAW,KAAK,IAAI,GAAE,IAAE,GAAG,EAAE,OAAK,KAAG,EAAE,GAAG,IAAI,KAAK,OAAO,UAAU,IAAG,IAAE,SAAS,gBAAgB,GAAE,KAAK;AAAE,gBAAc,GAAE,OAAO,GAAE,EAAC,eAAc,QAAO,WAAU,QAAO,CAAC,CAAC;AAAE,QAAM,IAAE,SAAS,gBAAgB,GAAE,KAAK,GAAE,IAAE,GAAG,CAAC,IAAI,CAAC;AAAG,SAAM,UAAS,KAAG,EAAE,eAAe,gCAA+B,QAAO,CAAC,GAAE,EAAE,eAAe,gCAA+B,cAAa,CAAC,GAAE,EAAE,YAAY,CAAC,GAAE;AAAC,GAAE,YAAY,GAAE,IAAE,CAAC,GAAE;AAAC,QAAM,IAAE,KAAK,IAAI,GAAE,KAAK,MAAM;AAAE,SAAO,cAAc,QAAO,EAAC,GAAG,GAAE,OAAM,CAAC,EAAE,OAAM,KAAK,OAAO,WAAW,MAAM,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG,EAAC,GAAE,CAAC;AAAC,GAAE,YAAY,GAAE;AAAC,MAAG,GAAG,MAAM,CAAC,EAAE,QAAO;AAAK,QAAM,IAAE,cAAc,QAAO,EAAC,OAAM,KAAK,OAAO,WAAW,KAAK,MAAK,CAAC;AAAE,SAAO,EAAE,YAAY,cAAc,QAAO,EAAC,OAAM,KAAK,OAAO,WAAW,KAAK,MAAK,GAAE,CAAC,CAAC,GAAE;AAAC,GAAE,aAAa,GAAE,GAAE;AAAC,QAAM,IAAE,OAAO,CAAC,GAAE,CAAC;AAAE,MAAI,IAAE,YAAY,CAAC;AAAE,QAAM,IAAE,EAAC,SAAQ,UAAS,QAAO,OAAG,OAAM,MAAK,MAAK,MAAK,cAAa,MAAK,aAAY,KAAI;AAAE,UAAO,CAAC,WAAU,QAAO,OAAO,EAAE,QAAS,CAAAA,OAAG;AAAC,WAAO,KAAK,CAAC,EAAE,SAASA,EAAC,MAAI,EAAEA,EAAC,IAAE,EAAEA,EAAC,GAAE,OAAO,EAAEA,EAAC;AAAA,EAAE,CAAE,GAAE,aAAW,EAAE,WAAS,OAAO,KAAK,CAAC,EAAE,SAAS,MAAM,MAAI,EAAE,OAAK,WAAU,OAAO,KAAK,CAAC,EAAE,SAAS,OAAO,IAAE,EAAE,MAAM,MAAM,GAAG,EAAE,KAAM,CAAAA,OAAGA,OAAI,KAAK,OAAO,WAAW,OAAQ,KAAG,OAAO,GAAE,EAAC,OAAM,GAAG,EAAE,KAAK,IAAI,KAAK,OAAO,WAAW,OAAO,GAAE,CAAC,IAAE,EAAE,QAAM,KAAK,OAAO,WAAW,SAAQ,GAAE;AAAA,IAAC,KAAI;AAAO,QAAE,SAAO,MAAG,EAAE,QAAM,QAAO,EAAE,eAAa,SAAQ,EAAE,OAAK,QAAO,EAAE,cAAY;AAAQ;AAAA,IAAM,KAAI;AAAO,QAAE,SAAO,MAAG,EAAE,QAAM,QAAO,EAAE,eAAa,UAAS,EAAE,OAAK,UAAS,EAAE,cAAY;AAAQ;AAAA,IAAM,KAAI;AAAW,QAAE,SAAO,MAAG,EAAE,QAAM,kBAAiB,EAAE,eAAa,mBAAkB,EAAE,OAAK,gBAAe,EAAE,cAAY;AAAc;AAAA,IAAM,KAAI;AAAa,QAAE,SAAO,MAAG,EAAE,QAAM,mBAAkB,EAAE,eAAa,kBAAiB,EAAE,OAAK,oBAAmB,EAAE,cAAY;AAAkB;AAAA,IAAM,KAAI;AAAa,QAAE,SAAO,IAAI,KAAK,OAAO,WAAW,OAAO,cAAa,IAAE,QAAO,EAAE,QAAM,QAAO,EAAE,OAAK;AAAO;AAAA,IAAM;AAAQ,SAAG,MAAM,EAAE,KAAK,MAAI,EAAE,QAAM,IAAG,GAAG,MAAM,EAAE,IAAI,MAAI,EAAE,OAAK;AAAA,EAAE;AAAC,QAAM,IAAE,cAAc,EAAE,OAAO;AAAE,SAAO,EAAE,UAAQ,EAAE,YAAY,SAAS,WAAW,KAAK,MAAK,EAAE,aAAY,EAAC,OAAM,gBAAe,CAAC,CAAC,GAAE,EAAE,YAAY,SAAS,WAAW,KAAK,MAAK,EAAE,MAAK,EAAC,OAAM,oBAAmB,CAAC,CAAC,GAAE,EAAE,YAAY,SAAS,YAAY,KAAK,MAAK,EAAE,cAAa,EAAC,OAAM,iBAAgB,CAAC,CAAC,GAAE,EAAE,YAAY,SAAS,YAAY,KAAK,MAAK,EAAE,OAAM,EAAC,OAAM,qBAAoB,CAAC,CAAC,MAAI,EAAE,YAAY,SAAS,WAAW,KAAK,MAAK,EAAE,IAAI,CAAC,GAAE,EAAE,YAAY,SAAS,YAAY,KAAK,MAAK,EAAE,KAAK,CAAC,IAAG,OAAO,GAAE,0BAA0B,KAAK,OAAO,UAAU,QAAQ,CAAC,GAAE,CAAC,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,WAAS,KAAG,GAAG,MAAM,KAAK,SAAS,QAAQ,CAAC,CAAC,MAAI,KAAK,SAAS,QAAQ,CAAC,IAAE,CAAC,IAAG,KAAK,SAAS,QAAQ,CAAC,EAAE,KAAK,CAAC,KAAG,KAAK,SAAS,QAAQ,CAAC,IAAE,GAAE;AAAC,GAAE,YAAY,GAAE,GAAE;AAAC,QAAM,IAAE,cAAc,SAAQ,OAAO,0BAA0B,KAAK,OAAO,UAAU,OAAO,CAAC,CAAC,GAAE,EAAC,MAAK,SAAQ,KAAI,GAAE,KAAI,KAAI,MAAK,MAAI,OAAM,GAAE,cAAa,OAAM,MAAK,UAAS,cAAa,KAAK,IAAI,GAAE,KAAK,MAAM,GAAE,iBAAgB,GAAE,iBAAgB,KAAI,iBAAgB,EAAC,GAAE,CAAC,CAAC;AAAE,SAAO,KAAK,SAAS,OAAO,CAAC,IAAE,GAAE,SAAS,gBAAgB,KAAK,MAAK,CAAC,GAAE,WAAW,MAAM,CAAC,GAAE;AAAC,GAAE,eAAe,GAAE,GAAE;AAAC,QAAM,IAAE,cAAc,YAAW,OAAO,0BAA0B,KAAK,OAAO,UAAU,QAAQ,CAAC,CAAC,GAAE,EAAC,KAAI,GAAE,KAAI,KAAI,OAAM,GAAE,MAAK,eAAc,eAAc,KAAE,GAAE,CAAC,CAAC;AAAE,MAAG,aAAW,GAAE;AAAC,MAAE,YAAY,cAAc,QAAO,MAAK,GAAG,CAAC;AAAE,UAAMD,KAAE,EAAC,QAAO,UAAS,QAAO,WAAU,EAAE,CAAC,GAAE,IAAEA,KAAE,KAAK,IAAIA,IAAE,KAAK,MAAM,IAAE;AAAG,MAAE,YAAU,KAAK,EAAE,YAAY,CAAC;AAAA,EAAE;AAAC,SAAO,KAAK,SAAS,QAAQ,CAAC,IAAE,GAAE;AAAC,GAAE,WAAW,GAAE,GAAE;AAAC,QAAM,IAAE,0BAA0B,KAAK,OAAO,UAAU,QAAQ,CAAC,GAAE,CAAC,GAAE,IAAE,cAAc,OAAM,OAAO,GAAE,EAAC,OAAM,GAAG,EAAE,QAAM,EAAE,QAAM,EAAE,IAAI,KAAK,OAAO,WAAW,QAAQ,IAAI,IAAI,KAAK,GAAE,cAAa,KAAK,IAAI,GAAE,KAAK,MAAM,GAAE,MAAK,QAAO,CAAC,GAAE,OAAO;AAAE,SAAO,KAAK,SAAS,QAAQ,CAAC,IAAE,GAAE;AAAC,GAAE,sBAAsB,GAAE,GAAE;AAAC,KAAG,KAAK,MAAK,GAAE,iBAAiB,OAAG;AAAC,QAAG,CAAC,CAAC,KAAI,WAAU,aAAY,YAAY,EAAE,SAAS,EAAE,GAAG,EAAE;AAAO,QAAG,EAAE,eAAe,GAAE,EAAE,gBAAgB,GAAE,cAAY,EAAE,KAAK;AAAO,UAAM,IAAE,QAAQ,GAAE,wBAAwB;AAAE,QAAG,CAAC,KAAG,CAAC,KAAI,YAAY,EAAE,SAAS,EAAE,GAAG,EAAE,UAAS,cAAc,KAAK,MAAK,GAAE,IAAE;AAAA,SAAM;AAAC,UAAIA;AAAE,cAAM,EAAE,QAAM,gBAAc,EAAE,OAAK,KAAG,iBAAe,EAAE,OAAKA,KAAE,EAAE,oBAAmB,GAAG,QAAQA,EAAC,MAAIA,KAAE,EAAE,WAAW,uBAAqBA,KAAE,EAAE,wBAAuB,GAAG,QAAQA,EAAC,MAAIA,KAAE,EAAE,WAAW,oBAAmB,SAAS,KAAK,MAAKA,IAAE,IAAE;AAAA,IAAE;AAAA,EAAC,GAAG,KAAE,GAAE,GAAG,KAAK,MAAK,GAAE,SAAS,CAAAC,OAAG;AAAC,iBAAWA,GAAE,OAAK,SAAS,mBAAmB,KAAK,MAAK,MAAK,IAAE;AAAA,EAAC,CAAE;AAAC,GAAE,eAAe,EAAC,OAAM,GAAE,MAAK,GAAE,MAAK,GAAE,OAAM,GAAE,OAAM,IAAE,MAAK,SAAQ,IAAE,MAAE,GAAE;AAAC,QAAM,IAAE,0BAA0B,KAAK,OAAO,UAAU,OAAO,CAAC,CAAC,GAAE,IAAE,cAAc,UAAS,OAAO,GAAE,EAAC,MAAK,UAAS,MAAK,iBAAgB,OAAM,GAAG,KAAK,OAAO,WAAW,OAAO,IAAI,EAAE,QAAM,EAAE,QAAM,EAAE,GAAG,KAAK,GAAE,gBAAe,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE,IAAE,cAAc,MAAM;AAAE,IAAE,YAAU,GAAE,GAAG,QAAQ,CAAC,KAAG,EAAE,YAAY,CAAC,GAAE,EAAE,YAAY,CAAC,GAAE,OAAO,eAAe,GAAE,WAAU,EAAC,YAAW,MAAG,KAAI,MAAI,WAAS,EAAE,aAAa,cAAc,GAAE,IAAIA,IAAE;AAAC,IAAAA,MAAG,MAAM,KAAK,EAAE,WAAW,QAAQ,EAAE,OAAQ,CAAAA,OAAG,QAAQA,IAAE,wBAAwB,CAAE,EAAE,QAAS,CAAAA,OAAGA,GAAE,aAAa,gBAAe,OAAO,CAAE,GAAE,EAAE,aAAa,gBAAeA,KAAE,SAAO,OAAO;AAAA,EAAC,EAAC,CAAC,GAAE,KAAK,UAAU,KAAK,GAAE,eAAe,CAAAD,OAAG;AAAC,QAAG,CAAC,GAAG,cAAcA,EAAC,KAAG,QAAMA,GAAE,KAAI;AAAC,cAAOA,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAE,UAAQ,MAAG,GAAE;AAAA,QAAC,KAAI;AAAW,eAAK,eAAa,OAAO,CAAC;AAAE;AAAA,QAAM,KAAI;AAAU,eAAK,UAAQ;AAAE;AAAA,QAAM,KAAI;AAAQ,eAAK,QAAM,WAAW,CAAC;AAAA,MAAC;AAAC,eAAS,cAAc,KAAK,MAAK,QAAO,GAAG,cAAcA,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC,GAAG,GAAE,KAAE,GAAE,SAAS,sBAAsB,KAAK,MAAK,GAAE,CAAC,GAAE,EAAE,YAAY,CAAC;AAAC,GAAE,WAAW,IAAE,GAAE,IAAE,OAAG;AAAC,MAAG,CAAC,GAAG,OAAO,CAAC,EAAE,QAAO;AAAE,SAAO,WAAW,GAAE,SAAS,KAAK,QAAQ,IAAE,GAAE,CAAC;AAAC,GAAE,kBAAkB,IAAE,MAAK,IAAE,GAAE,IAAE,OAAG;AAAC,KAAG,QAAQ,CAAC,KAAG,GAAG,OAAO,CAAC,MAAI,EAAE,YAAU,SAAS,WAAW,GAAE,CAAC;AAAE,GAAE,eAAc;AAAC,OAAK,UAAU,OAAK,GAAG,QAAQ,KAAK,SAAS,OAAO,MAAM,KAAG,SAAS,SAAS,KAAK,MAAK,KAAK,SAAS,OAAO,QAAO,KAAK,QAAM,IAAE,KAAK,MAAM,GAAE,GAAG,QAAQ,KAAK,SAAS,QAAQ,IAAI,MAAI,KAAK,SAAS,QAAQ,KAAK,UAAQ,KAAK,SAAO,MAAI,KAAK;AAAQ,GAAE,SAAS,GAAE,IAAE,GAAE;AAAC,KAAG,QAAQ,CAAC,MAAI,EAAE,QAAM,GAAE,SAAS,gBAAgB,KAAK,MAAK,CAAC;AAAE,GAAE,eAAe,GAAE;AAAC,MAAG,CAAC,KAAK,UAAU,MAAI,CAAC,GAAG,MAAM,CAAC,EAAE;AAAO,MAAI,IAAE;AAAE,QAAM,IAAE,CAACC,IAAED,OAAI;AAAC,UAAME,KAAE,GAAG,OAAOF,EAAC,IAAEA,KAAE,GAAE,IAAE,GAAG,QAAQC,EAAC,IAAEA,KAAE,KAAK,SAAS,QAAQ;AAAO,QAAG,GAAG,QAAQ,CAAC,GAAE;AAAC,QAAE,QAAMC;AAAE,YAAMD,KAAE,EAAE,qBAAqB,MAAM,EAAE,CAAC;AAAE,SAAG,QAAQA,EAAC,MAAIA,GAAE,WAAW,CAAC,EAAE,YAAUC;AAAA,IAAE;AAAA,EAAC;AAAE,MAAG,EAAE,SAAO,EAAE,MAAK;AAAA,IAAC,KAAI;AAAA,IAAa,KAAI;AAAA,IAAU,KAAI;AAAS,UAAE,cAAc,KAAK,aAAY,KAAK,QAAQ,GAAE,iBAAe,EAAE,QAAM,SAAS,SAAS,KAAK,MAAK,KAAK,SAAS,OAAO,MAAK,CAAC;AAAE;AAAA,IAAM,KAAI;AAAA,IAAU,KAAI;AAAW,QAAE,KAAK,SAAS,QAAQ,QAAO,MAAI,KAAK,QAAQ;AAAA,EAAC;AAAC,GAAE,gBAAgB,GAAE;AAAC,QAAM,IAAE,GAAG,MAAM,CAAC,IAAE,EAAE,SAAO;AAAE,MAAG,GAAG,QAAQ,CAAC,KAAG,YAAU,EAAE,aAAa,MAAM,GAAE;AAAC,QAAG,QAAQ,GAAE,KAAK,OAAO,UAAU,OAAO,IAAI,GAAE;AAAC,QAAE,aAAa,iBAAgB,KAAK,WAAW;AAAE,YAAMD,KAAE,SAAS,WAAW,KAAK,WAAW,GAAE,IAAE,SAAS,WAAW,KAAK,QAAQ,GAAE,IAAE,KAAK,IAAI,aAAY,KAAK,MAAM;AAAE,QAAE,aAAa,kBAAiB,EAAE,QAAQ,iBAAgBA,EAAC,EAAE,QAAQ,cAAa,CAAC,CAAC;AAAA,IAAC,WAAS,QAAQ,GAAE,KAAK,OAAO,UAAU,OAAO,MAAM,GAAE;AAAC,YAAMA,KAAE,MAAI,EAAE;AAAM,QAAE,aAAa,iBAAgBA,EAAC,GAAE,EAAE,aAAa,kBAAiB,GAAGA,GAAE,QAAQ,CAAC,CAAC,GAAG;AAAA,IAAC,MAAM,GAAE,aAAa,iBAAgB,EAAE,KAAK;AAAE,KAAC,QAAQ,YAAU,QAAQ,aAAW,EAAE,MAAM,YAAY,WAAU,EAAE,QAAM,EAAE,MAAI,MAAI,GAAG;AAAA,EAAC;AAAC,GAAE,kBAAkB,GAAE;AAAC,MAAI,GAAE;AAAE,MAAG,CAAC,KAAK,OAAO,SAAS,QAAM,CAAC,GAAG,QAAQ,KAAK,SAAS,OAAO,IAAI,KAAG,CAAC,GAAG,QAAQ,KAAK,SAAS,QAAQ,WAAW,KAAG,MAAI,KAAK,SAAS;AAAO,QAAM,IAAE,KAAK,SAAS,QAAQ,aAAY,IAAE,GAAG,KAAK,OAAO,WAAW,OAAO,aAAY,IAAE,CAAAA,OAAG,YAAY,GAAE,GAAEA,EAAC;AAAE,MAAG,KAAK,MAAM,QAAO,KAAK,EAAE,KAAE;AAAE,MAAI,IAAE;AAAE,QAAM,IAAE,KAAK,SAAS,SAAS,sBAAsB;AAAE,MAAG,GAAG,MAAM,CAAC,EAAE,KAAE,MAAI,EAAE,SAAO,EAAE,QAAM,EAAE;AAAA,OAAU;AAAC,QAAG,CAAC,SAAS,GAAE,CAAC,EAAE;AAAO,QAAE,WAAW,EAAE,MAAM,MAAK,EAAE;AAAA,EAAC;AAAC,MAAE,IAAE,IAAE,IAAE,IAAE,QAAM,IAAE;AAAK,QAAM,IAAE,KAAK,WAAS,MAAI;AAAE,IAAE,YAAU,SAAS,WAAW,CAAC;AAAE,QAAM,IAAE,UAAQ,IAAE,KAAK,OAAO,YAAU,WAAS,KAAG,UAAQ,IAAE,EAAE,WAAS,WAAS,IAAE,SAAO,EAAE,KAAM,CAAC,EAAC,MAAKA,GAAC,MAAIA,OAAI,KAAK,MAAM,CAAC,CAAE;AAAE,OAAG,EAAE,mBAAmB,cAAa,GAAG,EAAE,KAAK,MAAM,GAAE,EAAE,MAAM,OAAK,GAAG,CAAC,KAAI,GAAG,MAAM,CAAC,KAAG,CAAC,cAAa,YAAY,EAAE,SAAS,EAAE,IAAI,KAAG,EAAE,iBAAe,EAAE,IAAI;AAAC,GAAE,WAAW,GAAE;AAAC,QAAM,IAAE,CAAC,GAAG,QAAQ,KAAK,SAAS,QAAQ,QAAQ,KAAG,KAAK,OAAO;AAAW,WAAS,kBAAkB,KAAK,MAAK,KAAK,SAAS,QAAQ,aAAY,IAAE,KAAK,WAAS,KAAK,cAAY,KAAK,aAAY,CAAC,GAAE,KAAG,iBAAe,EAAE,QAAM,KAAK,MAAM,WAAS,SAAS,eAAe,KAAK,MAAK,CAAC;AAAC,GAAE,iBAAgB;AAAC,MAAG,CAAC,KAAK,UAAU,MAAI,CAAC,KAAK,OAAO,cAAY,KAAK,YAAY;AAAO,MAAG,KAAK,YAAU,KAAG,GAAG,QAAO,aAAa,KAAK,SAAS,QAAQ,aAAY,IAAE,GAAE,KAAK,aAAa,KAAK,SAAS,UAAS,IAAE;AAAE,KAAG,QAAQ,KAAK,SAAS,OAAO,IAAI,KAAG,KAAK,SAAS,OAAO,KAAK,aAAa,iBAAgB,KAAK,QAAQ;AAAE,QAAM,IAAE,GAAG,QAAQ,KAAK,SAAS,QAAQ,QAAQ;AAAE,GAAC,KAAG,KAAK,OAAO,mBAAiB,KAAK,UAAQ,SAAS,kBAAkB,KAAK,MAAK,KAAK,SAAS,QAAQ,aAAY,KAAK,QAAQ,GAAE,KAAG,SAAS,kBAAkB,KAAK,MAAK,KAAK,SAAS,QAAQ,UAAS,KAAK,QAAQ,GAAE,KAAK,OAAO,QAAQ,WAAS,SAAS,WAAW,KAAK,IAAI,GAAE,SAAS,kBAAkB,KAAK,IAAI;AAAC,GAAE,iBAAiB,GAAE,GAAE;AAAC,eAAa,KAAK,SAAS,SAAS,QAAQ,CAAC,GAAE,CAAC,CAAC;AAAC,GAAE,cAAc,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,KAAK,SAAS,SAAS,OAAO,CAAC;AAAE,MAAI,IAAE,MAAK,IAAE;AAAE,MAAG,eAAa,EAAE,KAAE,KAAK;AAAA,OAAiB;AAAC,QAAG,IAAE,GAAG,MAAM,CAAC,IAAE,KAAK,CAAC,IAAE,GAAE,GAAG,MAAM,CAAC,MAAI,IAAE,KAAK,OAAO,CAAC,EAAE,UAAS,CAAC,GAAG,MAAM,KAAK,QAAQ,CAAC,CAAC,KAAG,CAAC,KAAK,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE,QAAO,KAAK,KAAK,MAAM,KAAK,yBAAyB,CAAC,SAAS,CAAC,EAAE;AAAE,QAAG,CAAC,KAAK,OAAO,CAAC,EAAE,QAAQ,SAAS,CAAC,EAAE,QAAO,KAAK,KAAK,MAAM,KAAK,sBAAsB,CAAC,SAAS,CAAC,EAAE;AAAA,EAAC;AAAC,MAAG,GAAG,QAAQ,CAAC,MAAI,IAAE,KAAG,EAAE,cAAc,eAAe,IAAG,CAAC,GAAG,QAAQ,CAAC,EAAE;AAAO,OAAK,SAAS,SAAS,QAAQ,CAAC,EAAE,cAAc,IAAI,KAAK,OAAO,WAAW,KAAK,KAAK,EAAE,EAAE,YAAU,SAAS,SAAS,KAAK,MAAK,GAAE,CAAC;AAAE,QAAM,IAAE,KAAG,EAAE,cAAc,WAAW,CAAC,IAAI;AAAE,KAAG,QAAQ,CAAC,MAAI,EAAE,UAAQ;AAAG,GAAE,SAAS,GAAE,GAAE;AAAC,UAAO,GAAE;AAAA,IAAC,KAAI;AAAQ,aAAO,MAAI,IAAE,KAAK,IAAI,UAAS,KAAK,MAAM,IAAE,GAAG,CAAC;AAAA,IAAU,KAAI;AAAU,UAAG,GAAG,OAAO,CAAC,GAAE;AAAC,cAAMA,KAAE,KAAK,IAAI,gBAAgB,CAAC,IAAG,KAAK,MAAM;AAAE,eAAOA,GAAE,SAAOA,KAAE,GAAG,CAAC;AAAA,MAAG;AAAC,aAAO,YAAY,CAAC;AAAA,IAAE,KAAI;AAAW,aAAO,SAAS,SAAS,KAAK,IAAI;AAAA,IAAE;AAAQ,aAAO;AAAA,EAAI;AAAC,GAAE,eAAe,GAAE;AAAC,MAAG,CAAC,GAAG,QAAQ,KAAK,SAAS,SAAS,OAAO,OAAO,EAAE;AAAO,QAAM,IAAE,WAAU,IAAE,KAAK,SAAS,SAAS,OAAO,QAAQ,cAAc,eAAe;AAAE,KAAG,MAAM,CAAC,MAAI,KAAK,QAAQ,UAAQ,OAAO,CAAC,EAAE,OAAQ,CAAAA,OAAG,KAAK,OAAO,QAAQ,QAAQ,SAASA,EAAC,CAAE;AAAG,QAAM,IAAE,CAAC,GAAG,MAAM,KAAK,QAAQ,OAAO,KAAG,KAAK,QAAQ,QAAQ,SAAO;AAAE,MAAG,SAAS,iBAAiB,KAAK,MAAK,GAAE,CAAC,GAAE,aAAa,CAAC,GAAE,SAAS,UAAU,KAAK,IAAI,GAAE,CAAC,EAAE;AAAO,QAAM,IAAE,CAAAA,OAAG;AAAC,UAAMD,KAAE,KAAK,IAAI,gBAAgBC,EAAC,IAAG,KAAK,MAAM;AAAE,WAAOD,GAAE,SAAO,SAAS,YAAY,KAAK,MAAKA,EAAC,IAAE;AAAA,EAAI;AAAE,OAAK,QAAQ,QAAQ,KAAM,CAACC,IAAED,OAAI;AAAC,UAAME,KAAE,KAAK,OAAO,QAAQ;AAAQ,WAAOA,GAAE,QAAQD,EAAC,IAAEC,GAAE,QAAQF,EAAC,IAAE,IAAE;AAAA,EAAE,CAAE,EAAE,QAAS,CAAAC,OAAG;AAAC,aAAS,eAAe,KAAK,MAAK,EAAC,OAAMA,IAAE,MAAK,GAAE,MAAK,GAAE,OAAM,SAAS,SAAS,KAAK,MAAK,WAAUA,EAAC,GAAE,OAAM,EAAEA,EAAC,EAAC,CAAC;AAAA,EAAC,CAAE,GAAE,SAAS,cAAc,KAAK,MAAK,GAAE,CAAC;AAAC,GAAE,kBAAiB;AAAC,MAAG,CAAC,GAAG,QAAQ,KAAK,SAAS,SAAS,OAAO,QAAQ,EAAE;AAAO,QAAM,IAAE,YAAW,IAAE,KAAK,SAAS,SAAS,OAAO,SAAS,cAAc,eAAe,GAAE,IAAE,SAAS,UAAU,KAAK,IAAI,GAAE,IAAE,QAAQ,EAAE,MAAM;AAAE,MAAG,SAAS,iBAAiB,KAAK,MAAK,GAAE,CAAC,GAAE,aAAa,CAAC,GAAE,SAAS,UAAU,KAAK,IAAI,GAAE,CAAC,EAAE;AAAO,QAAM,IAAE,EAAE,IAAK,CAACA,IAAEC,QAAK,EAAC,OAAMA,IAAE,SAAQ,KAAK,SAAS,WAAS,KAAK,iBAAeA,IAAE,OAAM,SAAS,SAAS,KAAK,MAAKD,EAAC,GAAE,OAAMA,GAAE,YAAU,SAAS,YAAY,KAAK,MAAKA,GAAE,SAAS,YAAY,CAAC,GAAE,MAAK,GAAE,MAAK,WAAU,EAAG;AAAE,IAAE,QAAQ,EAAC,OAAM,IAAG,SAAQ,CAAC,KAAK,SAAS,SAAQ,OAAM,KAAK,IAAI,YAAW,KAAK,MAAM,GAAE,MAAK,GAAE,MAAK,WAAU,CAAC,GAAE,EAAE,QAAQ,SAAS,eAAe,KAAK,IAAI,CAAC,GAAE,SAAS,cAAc,KAAK,MAAK,GAAE,CAAC;AAAC,GAAE,eAAc;AAAC,MAAG,CAAC,GAAG,QAAQ,KAAK,SAAS,SAAS,OAAO,KAAK,EAAE;AAAO,QAAM,IAAE,SAAQ,IAAE,KAAK,SAAS,SAAS,OAAO,MAAM,cAAc,eAAe;AAAE,OAAK,QAAQ,QAAM,KAAK,QAAQ,MAAM,OAAQ,CAAAA,OAAGA,MAAG,KAAK,gBAAcA,MAAG,KAAK,YAAa;AAAE,QAAM,IAAE,CAAC,GAAG,MAAM,KAAK,QAAQ,KAAK,KAAG,KAAK,QAAQ,MAAM,SAAO;AAAE,WAAS,iBAAiB,KAAK,MAAK,GAAE,CAAC,GAAE,aAAa,CAAC,GAAE,SAAS,UAAU,KAAK,IAAI,GAAE,MAAI,KAAK,QAAQ,MAAM,QAAS,CAAAC,OAAG;AAAC,aAAS,eAAe,KAAK,MAAK,EAAC,OAAMA,IAAE,MAAK,GAAE,MAAK,GAAE,OAAM,SAAS,SAAS,KAAK,MAAK,SAAQA,EAAC,EAAC,CAAC;AAAA,EAAC,CAAE,GAAE,SAAS,cAAc,KAAK,MAAK,GAAE,CAAC;AAAE,GAAE,YAAW;AAAC,QAAK,EAAC,SAAQ,EAAC,IAAE,KAAK,SAAS,UAAS,IAAE,CAAC,GAAG,MAAM,CAAC,KAAG,OAAO,OAAO,CAAC,EAAE,KAAM,CAAAD,OAAG,CAACA,GAAE,MAAO;AAAE,eAAa,KAAK,SAAS,SAAS,MAAK,CAAC,CAAC;AAAC,GAAE,mBAAmB,GAAE,IAAE,OAAG;AAAC,MAAG,KAAK,SAAS,SAAS,MAAM,OAAO;AAAO,MAAI,IAAE;AAAE,KAAG,QAAQ,CAAC,MAAI,IAAE,OAAO,OAAO,KAAK,SAAS,SAAS,MAAM,EAAE,KAAM,CAAAA,OAAG,CAACA,GAAE,MAAO;AAAG,QAAM,IAAE,EAAE,cAAc,oBAAoB;AAAE,WAAS,KAAK,MAAK,GAAE,CAAC;AAAC,GAAE,WAAW,GAAE;AAAC,QAAK,EAAC,OAAM,EAAC,IAAE,KAAK,SAAS,UAAS,IAAE,KAAK,SAAS,QAAQ;AAAS,MAAG,CAAC,GAAG,QAAQ,CAAC,KAAG,CAAC,GAAG,QAAQ,CAAC,EAAE;AAAO,QAAK,EAAC,QAAO,EAAC,IAAE;AAAE,MAAI,IAAE;AAAE,MAAG,GAAG,QAAQ,CAAC,EAAE,KAAE;AAAA,WAAU,GAAG,cAAc,CAAC,KAAG,aAAW,EAAE,IAAI,KAAE;AAAA,WAAW,GAAG,MAAM,CAAC,GAAE;AAAC,UAAME,KAAE,GAAG,SAAS,EAAE,YAAY,IAAE,EAAE,aAAa,EAAE,CAAC,IAAE,EAAE,QAAO,IAAE,EAAE,SAASA,EAAC;AAAE,QAAG,KAAG,CAAC,KAAG,EAAE,WAAS,KAAG,EAAE;AAAA,EAAM;AAAC,IAAE,aAAa,iBAAgB,CAAC,GAAE,aAAa,GAAE,CAAC,CAAC,GAAE,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,KAAK,MAAK,CAAC,GAAE,KAAG,GAAG,cAAc,CAAC,IAAE,SAAS,mBAAmB,KAAK,MAAK,MAAK,IAAE,IAAE,KAAG,KAAG,SAAS,KAAK,MAAK,GAAE,GAAG,cAAc,CAAC,CAAC;AAAC,GAAE,YAAY,GAAE;AAAC,QAAM,IAAE,EAAE,UAAU,IAAE;AAAE,IAAE,MAAM,WAAS,YAAW,EAAE,MAAM,UAAQ,GAAE,EAAE,gBAAgB,QAAQ,GAAE,EAAE,WAAW,YAAY,CAAC;AAAE,QAAM,IAAE,EAAE,aAAY,IAAE,EAAE;AAAa,SAAO,cAAc,CAAC,GAAE,EAAC,OAAM,GAAE,QAAO,EAAC;AAAC,GAAE,cAAc,IAAE,IAAG,IAAE,OAAG;AAAC,QAAM,IAAE,KAAK,SAAS,UAAU,cAAc,kBAAkB,KAAK,EAAE,IAAI,CAAC,EAAE;AAAE,MAAG,CAAC,GAAG,QAAQ,CAAC,EAAE;AAAO,QAAM,IAAE,EAAE,YAAW,IAAE,MAAM,KAAK,EAAE,QAAQ,EAAE,KAAM,CAAAF,OAAG,CAACA,GAAE,MAAO;AAAE,MAAG,QAAQ,eAAa,CAAC,QAAQ,eAAc;AAAC,MAAE,MAAM,QAAM,GAAG,EAAE,WAAW,MAAK,EAAE,MAAM,SAAO,GAAG,EAAE,YAAY;AAAK,UAAMA,KAAE,SAAS,YAAY,KAAK,MAAK,CAAC,GAAED,KAAE,CAAAC,OAAG;AAAC,MAAAA,GAAE,WAAS,KAAG,CAAC,SAAQ,QAAQ,EAAE,SAASA,GAAE,YAAY,MAAI,EAAE,MAAM,QAAM,IAAG,EAAE,MAAM,SAAO,IAAG,IAAI,KAAK,MAAK,GAAE,oBAAmBD,EAAC;AAAA,IAAE;AAAE,OAAG,KAAK,MAAK,GAAE,oBAAmBA,EAAC,GAAE,EAAE,MAAM,QAAM,GAAGC,GAAE,KAAK,MAAK,EAAE,MAAM,SAAO,GAAGA,GAAE,MAAM;AAAA,EAAI;AAAC,eAAa,GAAE,IAAE,GAAE,aAAa,GAAE,KAAE,GAAE,SAAS,mBAAmB,KAAK,MAAK,GAAE,CAAC;AAAC,GAAE,iBAAgB;AAAC,QAAM,IAAE,KAAK,SAAS,QAAQ;AAAS,KAAG,QAAQ,CAAC,KAAG,EAAE,aAAa,QAAO,KAAK,QAAQ;AAAC,GAAE,OAAO,GAAE;AAAC,QAAK,EAAC,uBAAsB,GAAE,cAAa,GAAE,gBAAe,GAAE,aAAY,GAAE,YAAW,GAAE,gBAAe,GAAE,cAAa,GAAE,eAAc,EAAC,IAAE;AAAS,OAAK,SAAS,WAAS,MAAK,GAAG,MAAM,KAAK,OAAO,QAAQ,KAAG,KAAK,OAAO,SAAS,SAAS,YAAY,KAAG,KAAK,SAAS,UAAU,YAAY,EAAE,KAAK,MAAK,YAAY,CAAC;AAAE,QAAM,IAAE,cAAc,OAAM,0BAA0B,KAAK,OAAO,UAAU,SAAS,OAAO,CAAC;AAAE,OAAK,SAAS,WAAS;AAAE,QAAM,IAAE,EAAC,OAAM,uBAAsB;AAAE,SAAO,OAAO,GAAG,MAAM,KAAK,OAAO,QAAQ,IAAE,KAAK,OAAO,WAAS,CAAC,CAAC,EAAE,QAAS,CAAAI,OAAG;AAAC,QAAG,cAAYA,MAAG,EAAE,YAAY,EAAE,KAAK,MAAK,WAAU,CAAC,CAAC,GAAE,aAAWA,MAAG,EAAE,YAAY,EAAE,KAAK,MAAK,UAAS,CAAC,CAAC,GAAE,WAASA,MAAG,EAAE,YAAY,EAAE,KAAK,MAAK,QAAO,CAAC,CAAC,GAAE,mBAAiBA,MAAG,EAAE,YAAY,EAAE,KAAK,MAAK,gBAAe,CAAC,CAAC,GAAE,eAAaA,IAAE;AAAC,YAAML,KAAE,cAAc,OAAM,EAAC,OAAM,GAAG,EAAE,KAAK,6BAA4B,CAAC,GAAEE,KAAE,cAAc,OAAM,0BAA0B,KAAK,OAAO,UAAU,QAAQ,CAAC;AAAE,UAAGA,GAAE,YAAY,EAAE,KAAK,MAAK,QAAO,EAAC,IAAG,aAAa,EAAE,EAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,YAAY,EAAE,KAAK,MAAK,QAAQ,CAAC,GAAE,KAAK,OAAO,SAAS,MAAK;AAAC,cAAMD,KAAE,cAAc,QAAO,EAAC,OAAM,KAAK,OAAO,WAAW,QAAO,GAAE,OAAO;AAAE,QAAAC,GAAE,YAAYD,EAAC,GAAE,KAAK,SAAS,QAAQ,cAAYA;AAAA,MAAC;AAAC,WAAK,SAAS,WAASC,IAAEF,GAAE,YAAY,KAAK,SAAS,QAAQ,GAAE,EAAE,YAAYA,EAAC;AAAA,IAAC;AAAC,QAAG,mBAAiBK,MAAG,EAAE,YAAY,EAAE,KAAK,MAAK,eAAc,CAAC,CAAC,GAAE,eAAaA,MAAG,EAAE,YAAY,EAAE,KAAK,MAAK,YAAW,CAAC,CAAC,GAAE,WAASA,MAAG,aAAWA,IAAE;AAAC,UAAG,EAAC,QAAOL,GAAC,IAAE,KAAK;AAAS,UAAG,GAAG,QAAQA,EAAC,KAAG,EAAE,SAASA,EAAC,MAAIA,KAAE,cAAc,OAAM,OAAO,CAAC,GAAE,GAAE,EAAC,OAAM,GAAG,EAAE,KAAK,gBAAgB,KAAK,EAAC,CAAC,CAAC,GAAE,KAAK,SAAS,SAAOA,IAAE,EAAE,YAAYA,EAAC,IAAG,WAASK,MAAGL,GAAE,YAAY,EAAE,KAAK,MAAK,MAAM,CAAC,GAAE,aAAWK,MAAG,CAAC,QAAQ,SAAO,CAAC,QAAQ,UAAS;AAAC,cAAMH,KAAE,EAAC,KAAI,GAAE,MAAK,MAAI,OAAM,KAAK,OAAO,OAAM;AAAE,QAAAF,GAAE,YAAY,EAAE,KAAK,MAAK,UAAS,OAAOE,IAAE,EAAC,IAAG,eAAe,EAAE,EAAE,GAAE,CAAC,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,QAAG,eAAaG,MAAG,EAAE,YAAY,EAAE,KAAK,MAAK,YAAW,CAAC,CAAC,GAAE,eAAaA,MAAG,CAAC,GAAG,MAAM,KAAK,OAAO,QAAQ,GAAE;AAAC,YAAMF,KAAE,cAAc,OAAM,OAAO,CAAC,GAAE,GAAE,EAAC,OAAM,GAAG,EAAE,KAAK,cAAc,KAAK,GAAE,QAAO,GAAE,CAAC,CAAC;AAAE,MAAAA,GAAE,YAAY,EAAE,KAAK,MAAK,YAAW,EAAC,iBAAgB,MAAG,iBAAgB,iBAAiB,EAAE,EAAE,IAAG,iBAAgB,MAAE,CAAC,CAAC;AAAE,YAAMG,KAAE,cAAc,OAAM,EAAC,OAAM,yBAAwB,IAAG,iBAAiB,EAAE,EAAE,IAAG,QAAO,GAAE,CAAC,GAAEF,KAAE,cAAc,KAAK,GAAEC,KAAE,cAAc,OAAM,EAAC,IAAG,iBAAiB,EAAE,EAAE,QAAO,CAAC,GAAEE,KAAE,cAAc,OAAM,EAAC,MAAK,OAAM,CAAC;AAAE,MAAAF,GAAE,YAAYE,EAAC,GAAEH,GAAE,YAAYC,EAAC,GAAE,KAAK,SAAS,SAAS,OAAO,OAAKA,IAAE,KAAK,OAAO,SAAS,QAAS,CAAAH,OAAG;AAAC,cAAMC,KAAE,cAAc,UAAS,OAAO,0BAA0B,KAAK,OAAO,UAAU,QAAQ,QAAQ,GAAE,EAAC,MAAK,UAAS,OAAM,GAAG,KAAK,OAAO,WAAW,OAAO,IAAI,KAAK,OAAO,WAAW,OAAO,aAAY,MAAK,YAAW,iBAAgB,MAAG,QAAO,GAAE,CAAC,CAAC;AAAE,UAAE,KAAK,MAAKA,IAAED,EAAC,GAAE,GAAG,KAAK,MAAKC,IAAE,SAAS,MAAI;AAAC,YAAE,KAAK,MAAKD,IAAE,KAAE;AAAA,QAAC,CAAE;AAAE,cAAMI,KAAE,cAAc,QAAO,MAAK,KAAK,IAAIJ,IAAE,KAAK,MAAM,CAAC,GAAEG,KAAE,cAAc,QAAO,EAAC,OAAM,KAAK,OAAO,WAAW,KAAK,MAAK,CAAC;AAAE,QAAAA,GAAE,YAAU,EAAEH,EAAC,GAAEI,GAAE,YAAYD,EAAC,GAAEF,GAAE,YAAYG,EAAC,GAAEC,GAAE,YAAYJ,EAAC;AAAE,cAAMK,KAAE,cAAc,OAAM,EAAC,IAAG,iBAAiB,EAAE,EAAE,IAAIN,EAAC,IAAG,QAAO,GAAE,CAAC,GAAEO,KAAE,cAAc,UAAS,EAAC,MAAK,UAAS,OAAM,GAAG,KAAK,OAAO,WAAW,OAAO,IAAI,KAAK,OAAO,WAAW,OAAO,SAAQ,CAAC;AAAE,QAAAA,GAAE,YAAY,cAAc,QAAO,EAAC,eAAc,KAAE,GAAE,KAAK,IAAIP,IAAE,KAAK,MAAM,CAAC,CAAC,GAAEO,GAAE,YAAY,cAAc,QAAO,EAAC,OAAM,KAAK,OAAO,WAAW,OAAM,GAAE,KAAK,IAAI,YAAW,KAAK,MAAM,CAAC,CAAC,GAAE,GAAG,KAAK,MAAKD,IAAE,WAAW,CAAAP,OAAG;AAAC,0BAAcA,GAAE,QAAMA,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAE,KAAK,MAAK,QAAO,IAAE;AAAA,QAAE,GAAG,KAAE,GAAE,GAAG,KAAK,MAAKQ,IAAE,SAAS,MAAI;AAAC,YAAE,KAAK,MAAK,QAAO,KAAE;AAAA,QAAC,CAAE,GAAED,GAAE,YAAYC,EAAC,GAAED,GAAE,YAAY,cAAc,OAAM,EAAC,MAAK,OAAM,CAAC,CAAC,GAAEJ,GAAE,YAAYI,EAAC,GAAE,KAAK,SAAS,SAAS,QAAQN,EAAC,IAAEC,IAAE,KAAK,SAAS,SAAS,OAAOD,EAAC,IAAEM;AAAA,MAAC,CAAE,GAAEF,GAAE,YAAYF,EAAC,GAAED,GAAE,YAAYG,EAAC,GAAE,EAAE,YAAYH,EAAC,GAAE,KAAK,SAAS,SAAS,QAAMG,IAAE,KAAK,SAAS,SAAS,OAAKH;AAAA,IAAC;AAAC,QAAG,UAAQE,MAAG,QAAQ,OAAK,EAAE,YAAY,EAAE,KAAK,MAAK,OAAM,CAAC,CAAC,GAAE,cAAYA,MAAG,QAAQ,WAAS,EAAE,YAAY,EAAE,KAAK,MAAK,WAAU,CAAC,CAAC,GAAE,eAAaA,IAAE;AAAC,YAAMJ,KAAE,OAAO,CAAC,GAAE,GAAE,EAAC,SAAQ,KAAI,MAAK,KAAK,UAAS,QAAO,SAAQ,CAAC;AAAE,WAAK,YAAUA,GAAE,WAAS;AAAI,YAAK,EAAC,UAASD,GAAC,IAAE,KAAK,OAAO;AAAK,OAAC,GAAG,IAAIA,EAAC,KAAG,KAAK,WAAS,OAAOC,IAAE,EAAC,MAAK,QAAQ,KAAK,QAAQ,IAAG,OAAM,KAAK,SAAQ,CAAC,GAAE,EAAE,YAAY,EAAE,KAAK,MAAK,YAAWA,EAAC,CAAC;AAAA,IAAC;AAAC,qBAAeI,MAAG,EAAE,YAAY,EAAE,KAAK,MAAK,cAAa,CAAC,CAAC;AAAA,EAAC,CAAE,GAAE,KAAK,WAAS,EAAE,KAAK,MAAK,MAAM,kBAAkB,KAAK,IAAI,CAAC,GAAE,EAAE,KAAK,IAAI,GAAE;AAAC,GAAE,SAAQ;AAAC,MAAG,KAAK,OAAO,YAAW;AAAC,UAAMJ,KAAE,SAAS,WAAW,KAAK,IAAI;AAAE,IAAAA,GAAE,QAAM,WAAWA,GAAE,KAAI,aAAa;AAAA,EAAC;AAAC,OAAK,KAAG,KAAK,MAAM,MAAI,KAAK,OAAO,CAAC;AAAE,MAAI,IAAE;AAAK,OAAK,SAAS,WAAS;AAAK,QAAM,IAAE,EAAC,IAAG,KAAK,IAAG,UAAS,KAAK,OAAO,UAAS,OAAM,KAAK,OAAO,MAAK;AAAE,MAAI,IAAE;AAAG,KAAG,SAAS,KAAK,OAAO,QAAQ,MAAI,KAAK,OAAO,WAAS,KAAK,OAAO,SAAS,KAAK,MAAK,CAAC,IAAG,KAAK,OAAO,aAAW,KAAK,OAAO,WAAS,CAAC,IAAG,GAAG,QAAQ,KAAK,OAAO,QAAQ,KAAG,GAAG,OAAO,KAAK,OAAO,QAAQ,IAAE,IAAE,KAAK,OAAO,YAAU,IAAE,SAAS,OAAO,KAAK,MAAK,EAAC,IAAG,KAAK,IAAG,UAAS,KAAK,OAAO,UAAS,OAAM,KAAK,OAAM,SAAQ,KAAK,SAAQ,UAAS,SAAS,SAAS,KAAK,IAAI,EAAC,CAAC,GAAE,IAAE;AAAI,MAAI;AAAE,OAAG,GAAG,OAAO,KAAK,OAAO,QAAQ,MAAI,KAAG,CAAAA,OAAG;AAAC,QAAIC,KAAED;AAAE,WAAO,OAAO,QAAQ,CAAC,EAAE,QAAS,CAAC,CAACA,IAAED,EAAC,MAAI;AAAC,MAAAE,KAAE,WAAWA,IAAE,IAAID,EAAC,KAAID,EAAC;AAAA,IAAC,CAAE,GAAEE;AAAA,EAAC,GAAG,CAAC,IAAG,GAAG,OAAO,KAAK,OAAO,UAAU,SAAS,SAAS,MAAI,IAAE,SAAS,cAAc,KAAK,OAAO,UAAU,SAAS,SAAS,IAAG,GAAG,QAAQ,CAAC,MAAI,IAAE,KAAK,SAAS;AAAW,MAAG,EAAE,GAAG,QAAQ,CAAC,IAAE,0BAAwB,oBAAoB,EAAE,cAAa,CAAC,GAAE,GAAG,QAAQ,KAAK,SAAS,QAAQ,KAAG,SAAS,aAAa,KAAK,IAAI,GAAE,CAAC,GAAG,MAAM,KAAK,SAAS,OAAO,GAAE;AAAC,UAAMD,KAAE,CAAAA,OAAG;AAAC,YAAMD,KAAE,KAAK,OAAO,WAAW;AAAe,MAAAC,GAAE,aAAa,gBAAe,OAAO,GAAE,OAAO,eAAeA,IAAE,WAAU,EAAC,cAAa,MAAG,YAAW,MAAG,KAAI,MAAI,SAASA,IAAED,EAAC,GAAE,IAAIE,KAAE,OAAG;AAAC,oBAAYD,IAAED,IAAEE,EAAC,GAAED,GAAE,aAAa,gBAAeC,KAAE,SAAO,OAAO;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC;AAAE,WAAO,OAAO,KAAK,SAAS,OAAO,EAAE,OAAO,OAAO,EAAE,QAAS,CAAAF,OAAG;AAAC,SAAG,MAAMA,EAAC,KAAG,GAAG,SAASA,EAAC,IAAE,MAAM,KAAKA,EAAC,EAAE,OAAO,OAAO,EAAE,QAAQC,EAAC,IAAEA,GAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,MAAG,QAAQ,UAAQ,QAAQ,CAAC,GAAE,KAAK,OAAO,SAAS,UAAS;AAAC,UAAK,EAAC,YAAWC,IAAE,WAAUD,GAAC,IAAE,KAAK,QAAOE,KAAE,GAAGF,GAAE,SAAS,OAAO,IAAIA,GAAE,MAAM,KAAKC,GAAE,MAAM,IAAGE,KAAE,YAAY,KAAK,MAAKD,EAAC;AAAE,UAAM,KAAKC,EAAC,EAAE,QAAS,CAAAF,OAAG;AAAC,kBAAYA,IAAE,KAAK,OAAO,WAAW,QAAO,KAAE,GAAE,YAAYA,IAAE,KAAK,OAAO,WAAW,SAAQ,IAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,GAAE,mBAAkB;AAAC,MAAG;AAAC,sBAAiB,cAAY,UAAU,aAAa,WAAS,IAAI,OAAO,cAAc,EAAC,OAAM,KAAK,OAAO,cAAc,OAAM,QAAO,KAAK,OAAO,cAAc,QAAO,OAAM,KAAK,OAAO,cAAc,OAAM,SAAQ,KAAK,OAAO,cAAc,QAAO,CAAC;AAAA,EAAE,SAAO,GAAE;AAAA,EAAC;AAAC,GAAE,aAAY;AAAC,MAAI,GAAE;AAAE,MAAG,CAAC,KAAK,YAAU,KAAK,SAAS,QAAQ;AAAO,QAAM,IAAE,UAAQ,IAAE,KAAK,OAAO,YAAU,WAAS,KAAG,UAAQ,IAAE,EAAE,WAAS,WAAS,IAAE,SAAO,EAAE,OAAQ,CAAC,EAAC,MAAKA,GAAC,MAAIA,KAAE,KAAGA,KAAE,KAAK,QAAS;AAAE,MAAG,QAAM,KAAG,CAAC,EAAE,OAAO;AAAO,QAAM,IAAE,SAAS,uBAAuB,GAAE,IAAE,SAAS,uBAAuB;AAAE,MAAI,IAAE;AAAK,QAAM,IAAE,GAAG,KAAK,OAAO,WAAW,OAAO,aAAY,IAAE,CAAAA,OAAG,YAAY,GAAE,GAAEA,EAAC;AAAE,IAAE,QAAS,CAAAA,OAAG;AAAC,UAAMD,KAAE,cAAc,QAAO,EAAC,OAAM,KAAK,OAAO,WAAW,OAAM,GAAE,EAAE,GAAEE,KAAED,GAAE,OAAK,KAAK,WAAS,MAAI;AAAI,UAAID,GAAE,iBAAiB,cAAc,MAAI;AAAC,MAAAC,GAAE,UAAQ,EAAE,MAAM,OAAKC,IAAE,EAAE,YAAUD,GAAE,OAAM,EAAE,IAAE;AAAA,IAAE,CAAE,GAAED,GAAE,iBAAiB,cAAc,MAAI;AAAC,QAAE,KAAE;AAAA,IAAC,CAAE,IAAGA,GAAE,iBAAiB,SAAS,MAAI;AAAC,WAAK,cAAYC,GAAE;AAAA,IAAI,CAAE,GAAED,GAAE,MAAM,OAAKE,IAAE,EAAE,YAAYF,EAAC;AAAA,EAAC,CAAE,GAAE,EAAE,YAAY,CAAC,GAAE,KAAK,OAAO,SAAS,SAAO,IAAE,cAAc,QAAO,EAAC,OAAM,KAAK,OAAO,WAAW,QAAO,GAAE,EAAE,GAAE,EAAE,YAAY,CAAC,IAAG,KAAK,SAAS,UAAQ,EAAC,QAAO,GAAE,KAAI,EAAC,GAAE,KAAK,SAAS,SAAS,YAAY,CAAC;AAAC,EAAC;AAAE,SAAS,SAAS,GAAE,IAAE,MAAG;AAAC,MAAI,IAAE;AAAE,MAAG,GAAE;AAAC,UAAMC,KAAE,SAAS,cAAc,GAAG;AAAE,IAAAA,GAAE,OAAK,GAAE,IAAEA,GAAE;AAAA,EAAI;AAAC,MAAG;AAAC,WAAO,IAAI,IAAI,CAAC;AAAA,EAAC,SAAOA,IAAE;AAAC,WAAO;AAAA,EAAI;AAAC;AAAC,SAAS,eAAe,GAAE;AAAC,QAAM,IAAE,IAAI;AAAgB,SAAO,GAAG,OAAO,CAAC,KAAG,OAAO,QAAQ,CAAC,EAAE,QAAS,CAAC,CAACA,IAAE,CAAC,MAAI;AAAC,MAAE,IAAIA,IAAE,CAAC;AAAA,EAAC,CAAE,GAAE;AAAC;AAAC,IAAM,WAAS,EAAC,QAAO;AAAC,MAAG,CAAC,KAAK,UAAU,GAAG;AAAO,MAAG,CAAC,KAAK,WAAS,KAAK,aAAW,KAAK,WAAS,CAAC,QAAQ,WAAW,QAAO,MAAK,GAAG,MAAM,KAAK,OAAO,QAAQ,KAAG,KAAK,OAAO,SAAS,SAAS,UAAU,KAAG,KAAK,OAAO,SAAS,SAAS,UAAU,KAAG,SAAS,gBAAgB,KAAK,IAAI;AAAG,MAAG,GAAG,QAAQ,KAAK,SAAS,QAAQ,MAAI,KAAK,SAAS,WAAS,cAAc,OAAM,0BAA0B,KAAK,OAAO,UAAU,QAAQ,CAAC,GAAE,KAAK,SAAS,SAAS,aAAa,OAAM,MAAM,GAAE,YAAY,KAAK,SAAS,UAAS,KAAK,SAAS,OAAO,IAAG,QAAQ,QAAM,OAAO,KAAI;AAAC,UAAMA,KAAE,KAAK,MAAM,iBAAiB,OAAO;AAAE,UAAM,KAAKA,EAAC,EAAE,QAAS,CAAAA,OAAG;AAAC,YAAMD,KAAEC,GAAE,aAAa,KAAK,GAAEC,KAAE,SAASF,EAAC;AAAE,eAAOE,MAAGA,GAAE,aAAW,OAAO,SAAS,KAAK,YAAU,CAAC,SAAQ,QAAQ,EAAE,SAASA,GAAE,QAAQ,KAAG,MAAMF,IAAE,MAAM,EAAE,KAAM,CAAAA,OAAG;AAAC,QAAAC,GAAE,aAAa,OAAM,OAAO,IAAI,gBAAgBD,EAAC,CAAC;AAAA,MAAC,CAAE,EAAE,MAAO,MAAI;AAAC,sBAAcC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,QAAM,IAAE,QAAQ,UAAU,aAAW,CAAC,UAAU,YAAU,UAAU,gBAAc,IAAI,GAAG,IAAK,CAAAA,OAAGA,GAAE,MAAM,GAAG,EAAE,CAAC,CAAE,CAAC;AAAE,MAAI,KAAG,KAAK,QAAQ,IAAI,UAAU,KAAG,KAAK,OAAO,SAAS,YAAU,QAAQ,YAAY;AAAE,aAAS,MAAI,CAAC,CAAC,IAAE;AAAG,MAAI,IAAE,KAAK,QAAQ,IAAI,UAAU;AAAE,MAAG,GAAG,QAAQ,CAAC,MAAI,EAAC,QAAO,EAAC,IAAE,KAAK,OAAO,WAAU,OAAO,OAAO,KAAK,UAAS,EAAC,SAAQ,OAAG,QAAO,GAAE,UAAS,GAAE,WAAU,EAAC,CAAC,GAAE,KAAK,SAAQ;AAAC,UAAMA,KAAE,KAAK,OAAO,SAAS,SAAO,yBAAuB;AAAc,OAAG,KAAK,MAAK,KAAK,MAAM,YAAWA,IAAE,SAAS,OAAO,KAAK,IAAI,CAAC;AAAA,EAAC;AAAC,aAAW,SAAS,OAAO,KAAK,IAAI,GAAE,CAAC;AAAC,GAAE,SAAQ;AAAC,QAAM,IAAE,SAAS,UAAU,KAAK,MAAK,IAAE,GAAE,EAAC,QAAO,GAAE,UAAS,GAAE,MAAK,GAAE,kBAAiB,EAAC,IAAE,KAAK,UAAS,IAAE,QAAQ,EAAE,KAAM,CAAAA,OAAGA,GAAE,aAAW,CAAE,CAAC;AAAE,OAAK,WAAS,KAAK,WAAS,EAAE,OAAQ,CAAAA,OAAG,CAAC,EAAE,IAAIA,EAAC,CAAE,EAAE,QAAS,CAAAA,OAAG;AAAC,SAAK,MAAM,IAAI,eAAcA,EAAC,GAAE,EAAE,IAAIA,IAAE,EAAC,SAAQ,cAAYA,GAAE,KAAI,CAAC,GAAE,cAAYA,GAAE,SAAOA,GAAE,OAAK,WAAU,GAAG,KAAK,MAAKA,IAAE,aAAa,MAAI,SAAS,WAAW,KAAK,IAAI,CAAE;AAAA,EAAC,CAAE,IAAG,KAAG,KAAK,aAAW,KAAG,CAAC,EAAE,SAAS,CAAC,OAAK,SAAS,YAAY,KAAK,MAAK,CAAC,GAAE,SAAS,OAAO,KAAK,MAAK,KAAG,CAAC,IAAG,KAAK,YAAU,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,SAAS,SAAQ,CAAC,GAAG,MAAM,CAAC,CAAC,GAAE,GAAG,MAAM,KAAK,OAAO,QAAQ,KAAG,KAAK,OAAO,SAAS,SAAS,UAAU,KAAG,KAAK,OAAO,SAAS,SAAS,UAAU,KAAG,SAAS,gBAAgB,KAAK,IAAI;AAAC,GAAE,OAAO,GAAE,IAAE,MAAG;AAAC,MAAG,CAAC,KAAK,UAAU,GAAG;AAAO,QAAK,EAAC,SAAQ,EAAC,IAAE,KAAK,UAAS,IAAE,KAAK,OAAO,WAAW,SAAS,QAAO,IAAE,GAAG,gBAAgB,CAAC,IAAE,CAAC,IAAE;AAAE,MAAG,MAAI,GAAE;AAAC,QAAG,MAAI,KAAK,SAAS,SAAO,GAAE,KAAK,QAAQ,IAAI,EAAC,UAAS,EAAC,CAAC,IAAG,CAAC,KAAK,YAAU,KAAG,CAAC,GAAE;AAAC,YAAMA,KAAE,SAAS,UAAU,KAAK,IAAI,GAAED,KAAE,SAAS,UAAU,KAAK,MAAK,CAAC,KAAK,SAAS,UAAS,GAAG,KAAK,SAAS,SAAS,GAAE,IAAE;AAAE,aAAO,KAAK,SAAS,WAASA,GAAE,UAAS,KAAK,SAAS,IAAI,KAAK,MAAKC,GAAE,QAAQD,EAAC,CAAC;AAAA,IAAC;AAAC,SAAK,SAAS,QAAQ,aAAW,KAAK,SAAS,QAAQ,SAAS,UAAQ,IAAG,YAAY,KAAK,SAAS,WAAU,GAAE,CAAC,GAAE,KAAK,SAAS,UAAQ,GAAE,SAAS,cAAc,KAAK,MAAK,UAAU,GAAE,aAAa,KAAK,MAAK,KAAK,OAAM,IAAE,oBAAkB,kBAAkB;AAAA,EAAC;AAAC,aAAY,MAAI;AAAC,SAAG,KAAK,SAAS,YAAU,KAAK,SAAS,iBAAiB,OAAK;AAAA,EAAS,CAAE;AAAC,GAAE,IAAI,GAAE,IAAE,MAAG;AAAC,QAAM,IAAE,SAAS,UAAU,KAAK,IAAI;AAAE,MAAG,OAAK,EAAE,KAAG,GAAG,OAAO,CAAC,EAAE,KAAG,KAAK,GAAE;AAAC,QAAG,KAAK,SAAS,iBAAe,GAAE;AAAC,WAAK,SAAS,eAAa;AAAE,YAAM,IAAE,EAAE,CAAC,GAAE,EAAC,UAAS,EAAC,IAAE,KAAG,CAAC;AAAE,WAAK,SAAS,mBAAiB,GAAE,SAAS,cAAc,KAAK,MAAK,UAAU,GAAE,MAAI,KAAK,SAAS,WAAS,GAAE,KAAK,QAAQ,IAAI,EAAC,UAAS,EAAC,CAAC,IAAG,KAAK,WAAS,KAAK,MAAM,gBAAgB,CAAC,GAAE,aAAa,KAAK,MAAK,KAAK,OAAM,gBAAgB;AAAA,IAAC;AAAC,aAAS,OAAO,KAAK,MAAK,MAAG,CAAC,GAAE,KAAK,WAAS,KAAK,WAAS,SAAS,WAAW,KAAK,IAAI;AAAA,EAAC,MAAM,MAAK,MAAM,KAAK,mBAAkB,CAAC;AAAA,MAAO,MAAK,MAAM,KAAK,4BAA2B,CAAC;AAAA,MAAO,UAAS,OAAO,KAAK,MAAK,OAAG,CAAC;AAAC,GAAE,YAAY,GAAE,IAAE,MAAG;AAAC,MAAG,CAAC,GAAG,OAAO,CAAC,EAAE,QAAO,KAAK,KAAK,MAAM,KAAK,6BAA4B,CAAC;AAAE,QAAM,IAAE,EAAE,YAAY;AAAE,OAAK,SAAS,WAAS;AAAE,QAAM,IAAE,SAAS,UAAU,KAAK,IAAI,GAAE,IAAE,SAAS,UAAU,KAAK,MAAK,CAAC,CAAC,CAAC;AAAE,WAAS,IAAI,KAAK,MAAK,EAAE,QAAQ,CAAC,GAAE,CAAC;AAAC,GAAE,UAAU,IAAE,OAAG;AAAC,SAAO,MAAM,MAAM,KAAK,SAAO,CAAC,GAAG,cAAY,CAAC,CAAC,EAAE,OAAQ,OAAG,CAAC,KAAK,WAAS,KAAG,KAAK,SAAS,KAAK,IAAI,CAAC,CAAE,EAAE,OAAQ,CAAAC,OAAG,CAAC,YAAW,WAAW,EAAE,SAASA,GAAE,IAAI,CAAE;AAAC,GAAE,UAAU,GAAE,IAAE,OAAG;AAAC,QAAM,IAAE,SAAS,UAAU,KAAK,IAAI,GAAE,IAAE,CAAAA,OAAG,QAAQ,KAAK,SAAS,KAAK,IAAIA,EAAC,KAAG,CAAC,GAAG,OAAO,GAAE,IAAE,MAAM,KAAK,CAAC,EAAE,KAAM,CAACA,IAAED,OAAI,EAAEA,EAAC,IAAE,EAAEC,EAAC,CAAE;AAAE,MAAI;AAAE,SAAO,EAAE,MAAO,CAAAA,QAAI,IAAE,EAAE,KAAM,CAAAD,OAAGA,GAAE,aAAWC,EAAE,GAAE,CAAC,EAAG,GAAE,MAAI,IAAE,EAAE,CAAC,IAAE;AAAO,GAAE,kBAAiB;AAAC,SAAO,SAAS,UAAU,KAAK,IAAI,EAAE,KAAK,YAAY;AAAC,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE;AAAE,SAAM,CAAC,GAAG,MAAM,CAAC,KAAG,QAAQ,cAAY,KAAK,SAAS,YAAU,IAAE,SAAS,gBAAgB,KAAK,IAAI,IAAG,GAAG,MAAM,CAAC,IAAE,GAAG,MAAM,EAAE,KAAK,IAAE,GAAG,MAAM,EAAE,QAAQ,IAAE,KAAK,IAAI,WAAU,KAAK,MAAM,IAAE,EAAE,SAAS,YAAY,IAAE,EAAE,QAAM,KAAK,IAAI,YAAW,KAAK,MAAM;AAAC,GAAE,WAAW,GAAE;AAAC,MAAG,CAAC,KAAK,UAAU,GAAG;AAAO,MAAG,CAAC,GAAG,QAAQ,KAAK,SAAS,QAAQ,EAAE,QAAO,KAAK,KAAK,MAAM,KAAK,kCAAkC;AAAE,MAAG,CAAC,GAAG,gBAAgB,CAAC,KAAG,CAAC,MAAM,QAAQ,CAAC,EAAE,QAAO,KAAK,KAAK,MAAM,KAAK,6BAA4B,CAAC;AAAE,MAAI,IAAE;AAAE,MAAG,CAAC,GAAE;AAAC,UAAMA,KAAE,SAAS,gBAAgB,KAAK,IAAI;AAAE,QAAE,MAAM,MAAMA,MAAG,CAAC,GAAG,cAAY,CAAC,CAAC,EAAE,IAAK,CAAAA,OAAGA,GAAE,aAAa,CAAE,EAAE,IAAI,OAAO;AAAA,EAAC;AAAC,QAAM,IAAE,EAAE,IAAK,CAAAA,OAAGA,GAAE,KAAK,CAAE,EAAE,KAAK,IAAI;AAAE,MAAG,MAAI,KAAK,SAAS,SAAS,WAAU;AAAC,iBAAa,KAAK,SAAS,QAAQ;AAAE,UAAMA,KAAE,cAAc,QAAO,0BAA0B,KAAK,OAAO,UAAU,OAAO,CAAC;AAAE,IAAAA,GAAE,YAAU,GAAE,KAAK,SAAS,SAAS,YAAYA,EAAC,GAAE,aAAa,KAAK,MAAK,KAAK,OAAM,WAAW;AAAA,EAAC;AAAC,EAAC;AAAzwK,IAA2wK,WAAS,EAAC,SAAQ,MAAG,OAAM,IAAG,OAAM,OAAG,UAAS,OAAG,WAAU,MAAG,aAAY,MAAG,UAAS,IAAG,QAAO,GAAE,OAAM,OAAG,UAAS,MAAK,iBAAgB,MAAG,YAAW,MAAG,cAAa,MAAG,OAAM,MAAK,aAAY,MAAG,cAAa,MAAG,YAAW,OAAG,oBAAmB,MAAG,YAAW,MAAG,YAAW,QAAO,SAAQ,sCAAqC,YAAW,wCAAuC,SAAQ,EAAC,SAAQ,KAAI,SAAQ,CAAC,MAAK,MAAK,MAAK,MAAK,MAAK,KAAI,KAAI,KAAI,KAAI,GAAG,GAAE,QAAO,OAAG,UAAS,KAAI,GAAE,MAAK,EAAC,QAAO,MAAE,GAAE,OAAM,EAAC,UAAS,GAAE,SAAQ,CAAC,KAAG,MAAI,GAAE,MAAK,KAAI,MAAK,GAAE,CAAC,EAAC,GAAE,UAAS,EAAC,SAAQ,MAAG,QAAO,MAAE,GAAE,UAAS,EAAC,UAAS,OAAG,MAAK,KAAE,GAAE,UAAS,EAAC,QAAO,OAAG,UAAS,QAAO,QAAO,MAAE,GAAE,YAAW,EAAC,SAAQ,MAAG,UAAS,MAAG,WAAU,MAAE,GAAE,SAAQ,EAAC,SAAQ,MAAG,KAAI,OAAM,GAAE,UAAS,CAAC,cAAa,QAAO,YAAW,gBAAe,QAAO,UAAS,YAAW,YAAW,OAAM,WAAU,YAAY,GAAE,UAAS,CAAC,YAAW,WAAU,OAAO,GAAE,MAAK,EAAC,SAAQ,WAAU,QAAO,sBAAqB,MAAK,QAAO,OAAM,SAAQ,aAAY,uBAAsB,MAAK,QAAO,WAAU,+BAA8B,QAAO,UAAS,UAAS,YAAW,aAAY,gBAAe,UAAS,YAAW,QAAO,UAAS,MAAK,QAAO,QAAO,UAAS,gBAAe,mBAAkB,iBAAgB,oBAAmB,UAAS,YAAW,iBAAgB,oBAAmB,gBAAe,mBAAkB,YAAW,sBAAqB,UAAS,YAAW,UAAS,YAAW,KAAI,OAAM,UAAS,4BAA2B,OAAM,SAAQ,QAAO,UAAS,SAAQ,WAAU,MAAK,QAAO,OAAM,SAAQ,KAAI,OAAM,KAAI,OAAM,OAAM,SAAQ,UAAS,YAAW,SAAQ,WAAU,eAAc,MAAK,cAAa,EAAC,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,KAAI,EAAC,GAAE,MAAK,EAAC,UAAS,MAAK,OAAM,EAAC,KAAI,0CAAyC,QAAO,0CAAyC,KAAI,4CAA2C,GAAE,SAAQ,EAAC,KAAI,sCAAqC,KAAI,oEAAmE,GAAE,WAAU,EAAC,KAAI,qDAAoD,EAAC,GAAE,WAAU,EAAC,MAAK,MAAK,MAAK,MAAK,OAAM,MAAK,SAAQ,MAAK,QAAO,MAAK,aAAY,MAAK,MAAK,MAAK,QAAO,MAAK,UAAS,MAAK,UAAS,MAAK,YAAW,MAAK,KAAI,MAAK,SAAQ,MAAK,OAAM,MAAK,SAAQ,MAAK,MAAK,MAAK,UAAS,KAAI,GAAE,QAAO,CAAC,SAAQ,YAAW,WAAU,WAAU,WAAU,WAAU,kBAAiB,aAAY,cAAa,kBAAiB,cAAa,gBAAe,QAAO,SAAQ,SAAQ,WAAU,UAAS,WAAU,cAAa,aAAY,YAAW,mBAAkB,kBAAiB,mBAAkB,oBAAmB,kBAAiB,kBAAiB,iBAAgB,SAAQ,eAAc,iBAAgB,aAAY,mBAAkB,oBAAmB,aAAY,eAAc,eAAc,kBAAiB,iBAAgB,UAAU,GAAE,WAAU,EAAC,UAAS,8CAA6C,WAAU,SAAQ,UAAS,EAAC,WAAU,MAAK,SAAQ,kBAAiB,GAAE,QAAO,eAAc,SAAQ,EAAC,MAAK,sBAAqB,OAAM,uBAAsB,SAAQ,yBAAwB,QAAO,wBAAuB,aAAY,8BAA6B,MAAK,sBAAqB,UAAS,0BAAyB,UAAS,0BAAyB,YAAW,4BAA2B,KAAI,qBAAoB,SAAQ,yBAAwB,UAAS,0BAAyB,MAAK,qBAAoB,GAAE,QAAO,EAAC,MAAK,sBAAqB,QAAO,wBAAuB,OAAM,uBAAsB,UAAS,0BAAyB,SAAQ,wBAAuB,GAAE,SAAQ,EAAC,aAAY,wBAAuB,UAAS,yBAAwB,QAAO,2BAA0B,MAAK,yBAAwB,QAAO,yBAAwB,GAAE,UAAS,mBAAkB,UAAS,mBAAkB,SAAQ,iBAAgB,GAAE,YAAW,EAAC,MAAK,aAAY,UAAS,aAAY,OAAM,uBAAsB,OAAM,qBAAoB,iBAAgB,oCAAmC,gBAAe,gCAA+B,QAAO,gBAAe,eAAc,wBAAuB,KAAI,aAAY,SAAQ,iBAAgB,gBAAe,0BAAyB,SAAQ,iBAAgB,QAAO,gBAAe,SAAQ,iBAAgB,SAAQ,iBAAgB,OAAM,eAAc,SAAQ,iBAAgB,MAAK,cAAa,QAAO,0BAAyB,QAAO,iBAAgB,cAAa,uBAAsB,SAAQ,kBAAiB,aAAY,iBAAgB,cAAa,uBAAsB,SAAQ,EAAC,MAAK,aAAY,GAAE,MAAK,EAAC,OAAM,qBAAoB,OAAM,eAAc,MAAK,kBAAiB,GAAE,UAAS,EAAC,SAAQ,0BAAyB,QAAO,wBAAuB,GAAE,YAAW,EAAC,SAAQ,4BAA2B,UAAS,4BAA2B,GAAE,KAAI,EAAC,WAAU,uBAAsB,QAAO,mBAAkB,GAAE,SAAQ,EAAC,WAAU,2BAA0B,QAAO,uBAAsB,GAAE,mBAAkB,EAAC,gBAAe,uBAAsB,qBAAoB,iCAAgC,gBAAe,wCAAuC,eAAc,uCAAsC,oBAAmB,2BAA0B,yBAAwB,oCAAmC,EAAC,GAAE,YAAW,EAAC,OAAM,EAAC,UAAS,sBAAqB,IAAG,sBAAqB,MAAK,uBAAsB,EAAC,GAAE,KAAI,EAAC,SAAQ,OAAG,aAAY,IAAG,QAAO,GAAE,GAAE,mBAAkB,EAAC,SAAQ,OAAG,KAAI,GAAE,GAAE,OAAM,EAAC,QAAO,OAAG,UAAS,OAAG,OAAM,OAAG,OAAM,MAAG,aAAY,OAAG,gBAAe,MAAG,gBAAe,MAAK,SAAQ,MAAE,GAAE,SAAQ,EAAC,KAAI,GAAE,UAAS,GAAE,gBAAe,GAAE,gBAAe,GAAE,gBAAe,MAAG,UAAS,MAAE,GAAE,eAAc,EAAC,OAAM,IAAG,QAAO,IAAG,OAAM,IAAG,SAAQ,CAAC,EAAC,GAAE,SAAQ,EAAC,SAAQ,OAAG,QAAO,CAAC,EAAC,EAAC;AAA3zV,IAA6zV,MAAI,EAAC,QAAO,sBAAqB,UAAS,SAAQ;AAA/2V,IAAi3V,YAAU,EAAC,OAAM,SAAQ,SAAQ,WAAU,OAAM,QAAO;AAAz6V,IAA26V,QAAM,EAAC,OAAM,SAAQ,OAAM,QAAO;AAAE,SAAS,iBAAiB,GAAE;AAAC,SAAM,8EAA8E,KAAK,CAAC,IAAE,UAAU,UAAQ,wDAAwD,KAAK,CAAC,IAAE,UAAU,QAAM;AAAI;AAAC,IAAM,OAAK,MAAI;AAAC;AAAE,IAAM,UAAN,MAAa;AAAA,EAAC,YAAY,IAAE,OAAG;AAAC,SAAK,UAAQ,OAAO,WAAS,GAAE,KAAK,WAAS,KAAK,IAAI,mBAAmB;AAAA,EAAC;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK,UAAQ,SAAS,UAAU,KAAK,KAAK,QAAQ,KAAI,OAAO,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,UAAQ,SAAS,UAAU,KAAK,KAAK,QAAQ,MAAK,OAAO,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,UAAQ,SAAS,UAAU,KAAK,KAAK,QAAQ,OAAM,OAAO,IAAE;AAAA,EAAI;AAAC;AAAC,IAAM,aAAN,MAAM,YAAU;AAAA,EAAC,YAAY,GAAE;AAAC,sBAAkB,MAAK,YAAY,MAAI;AAAC,UAAG,CAAC,KAAK,UAAU;AAAO,YAAMA,KAAE,KAAK,OAAO,SAAS,QAAQ;AAAW,SAAG,QAAQA,EAAC,MAAIA,GAAE,UAAQ,KAAK;AAAQ,YAAM,IAAE,KAAK,WAAS,KAAK,OAAO,QAAM,KAAK,SAAO,KAAK,OAAO,SAAS;AAAU,mBAAa,KAAK,KAAK,QAAO,GAAE,KAAK,SAAO,oBAAkB,kBAAiB,IAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,kBAAkB,CAACA,KAAE,UAAK;AAAC,UAAGA,KAAE,KAAK,iBAAe,EAAC,GAAE,OAAO,WAAS,GAAE,GAAE,OAAO,WAAS,EAAC,IAAE,OAAO,SAAS,KAAK,eAAe,GAAE,KAAK,eAAe,CAAC,GAAE,SAAS,KAAK,MAAM,WAASA,KAAE,WAAS,IAAG,YAAY,KAAK,QAAO,KAAK,OAAO,OAAO,WAAW,WAAW,UAASA,EAAC,GAAE,QAAQ,OAAM;AAAC,YAAI,IAAE,SAAS,KAAK,cAAc,uBAAuB;AAAE,cAAM,IAAE;AAAqB,cAAI,IAAE,SAAS,cAAc,MAAM,GAAE,EAAE,aAAa,QAAO,UAAU;AAAG,cAAM,IAAE,GAAG,OAAO,EAAE,OAAO,KAAG,EAAE,QAAQ,SAAS,CAAC;AAAE,QAAAA,MAAG,KAAK,kBAAgB,CAAC,GAAE,MAAI,EAAE,WAAS,IAAI,CAAC,OAAK,KAAK,oBAAkB,EAAE,UAAQ,EAAE,QAAQ,MAAM,GAAG,EAAE,OAAQ,CAAAA,OAAGA,GAAE,KAAK,MAAI,CAAE,EAAE,KAAK,GAAG;AAAA,MAAE;AAAC,WAAK,SAAS;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,aAAa,CAAAA,OAAG;AAAC,UAAG,QAAQ,SAAO,QAAQ,YAAU,CAAC,KAAK,UAAQ,UAAQA,GAAE,IAAI;AAAO,YAAM,IAAE,SAAS,eAAc,IAAE,YAAY,KAAK,KAAK,QAAO,kEAAkE,GAAE,CAAC,CAAC,IAAE,GAAE,IAAE,EAAE,EAAE,SAAO,CAAC;AAAE,YAAI,KAAGA,GAAE,WAAS,MAAI,KAAGA,GAAE,aAAW,EAAE,MAAM,GAAEA,GAAE,eAAe,MAAI,EAAE,MAAM,GAAEA,GAAE,eAAe;AAAA,IAAE,CAAE,GAAE,kBAAkB,MAAK,UAAU,MAAI;AAAC,UAAG,KAAK,WAAU;AAAC,YAAIA;AAAE,QAAAA,KAAE,KAAK,gBAAc,sBAAoB,YAAW,kBAAgB,WAAS,YAAW,KAAK,OAAO,MAAM,IAAI,GAAGA,EAAC,qBAAqB;AAAA,MAAC,MAAM,MAAK,OAAO,MAAM,IAAI,gDAAgD;AAAE,kBAAY,KAAK,OAAO,SAAS,WAAU,KAAK,OAAO,OAAO,WAAW,WAAW,SAAQ,KAAK,SAAS;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,SAAS,MAAI;AAAC,WAAK,cAAY,QAAQ,SAAO,KAAK,OAAO,OAAO,WAAW,YAAU,KAAK,OAAO,UAAQ,KAAK,OAAO,MAAM,kBAAkB,IAAE,KAAK,OAAO,sBAAsB,IAAE,CAAC,YAAW,mBAAiB,KAAK,gBAAc,KAAK,eAAe,IAAE,IAAE,KAAK,SAAO,GAAG,MAAM,KAAK,MAAM,KAAG,KAAK,OAAO,GAAG,KAAK,MAAM,UAAU,KAAK,QAAQ,EAAE,EAAE,IAAE,KAAK,OAAO,kBAAkB,EAAC,cAAa,OAAM,CAAC;AAAA,IAAE,CAAE,GAAE,kBAAkB,MAAK,QAAQ,MAAI;AAAC,UAAG,KAAK,UAAU,KAAG,QAAQ,SAAO,KAAK,OAAO,OAAO,WAAW,UAAU,MAAK,OAAO,UAAQ,KAAK,OAAO,MAAM,eAAe,IAAE,KAAK,OAAO,sBAAsB,GAAE,eAAe,KAAK,OAAO,KAAK,CAAC;AAAA,eAAU,CAAC,YAAW,mBAAiB,KAAK,cAAc,MAAK,eAAe,KAAE;AAAA,eAAU,KAAK,QAAO;AAAC,YAAG,CAAC,GAAG,MAAM,KAAK,MAAM,GAAE;AAAC,gBAAMA,KAAE,UAAQ,KAAK,SAAO,WAAS;AAAO,mBAAS,GAAG,KAAK,MAAM,GAAGA,EAAC,GAAG,KAAK,QAAQ,EAAE,EAAE;AAAA,QAAC;AAAA,MAAC,MAAK,EAAC,SAAS,oBAAkB,SAAS,gBAAgB,KAAK,QAAQ;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,UAAU,MAAI;AAAC,WAAK,SAAO,KAAK,KAAK,IAAE,KAAK,MAAM;AAAA,IAAC,CAAE,GAAE,KAAK,SAAO,GAAE,KAAK,SAAO,YAAW,QAAO,KAAK,WAAS,YAAW,UAAS,KAAK,iBAAe,EAAC,GAAE,GAAE,GAAE,EAAC,GAAE,KAAK,gBAAc,YAAU,EAAE,OAAO,WAAW,UAAS,KAAK,OAAO,SAAS,aAAW,EAAE,OAAO,WAAW,aAAW,UAAU,KAAK,OAAO,SAAS,WAAU,EAAE,OAAO,WAAW,SAAS,GAAE,GAAG,KAAK,KAAK,QAAO,UAAS,SAAO,KAAK,SAAO,uBAAqB,GAAG,KAAK,MAAM,oBAAoB,MAAI;AAAC,WAAK,SAAS;AAAA,IAAC,CAAE,GAAE,GAAG,KAAK,KAAK,QAAO,KAAK,OAAO,SAAS,WAAU,YAAY,CAAAA,OAAG;AAAC,SAAG,QAAQ,KAAK,OAAO,SAAS,QAAQ,KAAG,KAAK,OAAO,SAAS,SAAS,SAASA,GAAE,MAAM,KAAG,KAAK,OAAO,UAAU,MAAMA,IAAE,KAAK,QAAO,YAAY;AAAA,IAAC,CAAE,GAAE,GAAG,KAAK,MAAK,KAAK,OAAO,SAAS,WAAU,WAAW,CAAAA,OAAG,KAAK,UAAUA,EAAC,CAAE,GAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,WAAW,kBAAiB;AAAC,WAAM,CAAC,EAAE,SAAS,qBAAmB,SAAS,2BAAyB,SAAS,wBAAsB,SAAS;AAAA,EAAoB;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,YAAW,mBAAiB,CAAC,KAAK;AAAA,EAAa;AAAA,EAAC,WAAW,SAAQ;AAAC,QAAG,GAAG,SAAS,SAAS,cAAc,EAAE,QAAM;AAAG,QAAI,IAAE;AAAG,WAAM,CAAC,UAAS,OAAM,IAAI,EAAE,KAAM,OAAG,EAAE,CAAC,GAAG,SAAS,SAAS,GAAG,CAAC,gBAAgB,CAAC,KAAG,CAAC,GAAG,SAAS,SAAS,GAAG,CAAC,kBAAkB,CAAC,OAAK,IAAE,GAAE,KAAI,GAAE;AAAA,EAAC;AAAA,EAAC,WAAW,WAAU;AAAC,WAAM,UAAQ,KAAK,SAAO,eAAa;AAAA,EAAY;AAAA,EAAC,IAAI,YAAW;AAAC,WAAM,CAAC,KAAK,OAAO,OAAO,WAAW,SAAQ,KAAK,OAAO,SAAQ,YAAW,mBAAiB,KAAK,OAAO,OAAO,WAAW,UAAS,CAAC,KAAK,OAAO,aAAW,YAAW,mBAAiB,CAAC,QAAQ,SAAO,KAAK,OAAO,OAAO,eAAa,CAAC,KAAK,OAAO,OAAO,WAAW,SAAS,EAAE,MAAM,OAAO;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,QAAG,CAAC,KAAK,UAAU,QAAM;AAAG,QAAG,CAAC,YAAW,mBAAiB,KAAK,cAAc,QAAO,SAAS,KAAK,QAAO,KAAK,OAAO,OAAO,WAAW,WAAW,QAAQ;AAAE,UAAM,IAAE,KAAK,SAAO,KAAK,OAAO,YAAY,EAAE,GAAG,KAAK,MAAM,GAAG,KAAK,QAAQ,SAAS,IAAE,KAAK,OAAO,YAAY,EAAE;AAAkB,WAAO,KAAG,EAAE,aAAW,MAAI,KAAK,OAAO,YAAY,EAAE,OAAK,MAAI,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,QAAQ,SAAO,KAAK,OAAO,OAAO,WAAW,YAAU,KAAK,OAAO,QAAM,KAAK,OAAO,SAAS,cAAY,KAAK,OAAO,SAAS;AAAA,EAAS;AAAC;AAAC,SAAS,UAAU,GAAE,IAAE,GAAE;AAAC,SAAO,IAAI,QAAS,CAAC,GAAE,MAAI;AAAC,UAAM,IAAE,IAAI,SAAM,IAAE,MAAI;AAAC,aAAO,EAAE,QAAO,OAAO,EAAE,UAAS,EAAE,gBAAc,IAAE,IAAE,GAAG,CAAC;AAAA,IAAC;AAAE,WAAO,OAAO,GAAE,EAAC,QAAO,GAAE,SAAQ,GAAE,KAAI,EAAC,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,IAAM,KAAG,EAAC,eAAc;AAAC,cAAY,KAAK,SAAS,WAAU,KAAK,OAAO,UAAU,UAAU,QAAQ,KAAI,EAAE,GAAE,IAAE,GAAE,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,aAAY,KAAK,UAAU,EAAE;AAAC,GAAE,qBAAqB,IAAE,OAAG;AAAC,OAAG,KAAK,UAAQ,KAAK,MAAM,aAAa,YAAW,EAAE,IAAE,KAAK,MAAM,gBAAgB,UAAU;AAAC,GAAE,QAAO;AAAC,MAAG,KAAK,UAAU,MAAM,GAAE,CAAC,KAAK,UAAU,GAAG,QAAO,KAAK,MAAM,KAAK,0BAA0B,KAAK,QAAQ,IAAI,KAAK,IAAI,EAAE,GAAE,KAAK,GAAG,qBAAqB,KAAK,MAAK,IAAE;AAAE,KAAG,QAAQ,KAAK,SAAS,QAAQ,MAAI,SAAS,OAAO,KAAK,IAAI,GAAE,KAAK,UAAU,SAAS,IAAG,GAAG,qBAAqB,KAAK,IAAI,GAAE,KAAK,WAAS,SAAS,MAAM,KAAK,IAAI,GAAE,KAAK,SAAO,MAAK,KAAK,QAAM,MAAK,KAAK,OAAK,MAAK,KAAK,UAAQ,MAAK,KAAK,QAAM,MAAK,SAAS,aAAa,KAAK,IAAI,GAAE,SAAS,WAAW,KAAK,IAAI,GAAE,SAAS,eAAe,KAAK,IAAI,GAAE,GAAG,aAAa,KAAK,IAAI,GAAE,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,IAAI,WAAU,QAAQ,OAAK,KAAK,WAAS,KAAK,OAAO,GAAE,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,QAAQ,WAAU,QAAQ,WAAS,KAAK,OAAO,GAAE,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,SAAQ,KAAK,KAAK,GAAE,KAAK,QAAM,MAAG,WAAY,MAAI;AAAC,iBAAa,KAAK,MAAK,KAAK,OAAM,OAAO;AAAA,EAAC,GAAG,CAAC,GAAE,GAAG,SAAS,KAAK,IAAI,GAAE,KAAK,UAAQ,GAAG,UAAU,KAAK,MAAK,KAAK,QAAO,KAAE,EAAE,MAAO,MAAI;AAAA,EAAC,CAAE,GAAE,KAAK,OAAO,YAAU,SAAS,eAAe,KAAK,IAAI,GAAE,KAAK,OAAO,iBAAe,SAAS,iBAAiB,KAAK,IAAI;AAAC,GAAE,WAAU;AAAC,MAAI,IAAE,KAAK,IAAI,QAAO,KAAK,MAAM;AAAE,MAAG,GAAG,OAAO,KAAK,OAAO,KAAK,KAAG,CAAC,GAAG,MAAM,KAAK,OAAO,KAAK,MAAI,KAAG,KAAK,KAAK,OAAO,KAAK,KAAI,MAAM,KAAK,KAAK,SAAS,QAAQ,QAAM,CAAC,CAAC,EAAE,QAAS,OAAG;AAAC,MAAE,aAAa,cAAa,CAAC;AAAA,EAAC,CAAE,GAAE,KAAK,SAAQ;AAAC,UAAMA,KAAE,WAAW,KAAK,MAAK,QAAQ;AAAE,QAAG,CAAC,GAAG,QAAQA,EAAC,EAAE;AAAO,UAAM,IAAE,GAAG,MAAM,KAAK,OAAO,KAAK,IAAE,UAAQ,KAAK,OAAO,OAAM,IAAE,KAAK,IAAI,cAAa,KAAK,MAAM;AAAE,IAAAA,GAAE,aAAa,SAAQ,EAAE,QAAQ,WAAU,CAAC,CAAC;AAAA,EAAC;AAAC,GAAE,aAAa,GAAE;AAAC,cAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,eAAc,CAAC;AAAC,GAAE,UAAU,GAAE,IAAE,MAAG;AAAC,SAAO,KAAG,KAAK,SAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC,KAAG,KAAK,MAAM,aAAa,eAAc,CAAC,GAAE,KAAK,SAAS,OAAO,gBAAgB,QAAQ,GAAE,MAAM,KAAK,IAAI,EAAE,KAAM,MAAI,UAAU,CAAC,CAAE,EAAE,MAAO,CAAAD,OAAG;AAAC,UAAM,MAAI,KAAK,UAAQ,GAAG,aAAa,KAAK,MAAK,KAAE,GAAEA;AAAA,EAAC,CAAE,EAAE,KAAM,MAAI;AAAC,QAAG,MAAI,KAAK,OAAO,OAAM,IAAI,MAAM,gDAAgD;AAAA,EAAC,CAAE,EAAE,KAAM,OAAK,OAAO,OAAO,KAAK,SAAS,OAAO,OAAM,EAAC,iBAAgB,QAAQ,CAAC,MAAK,gBAAe,GAAE,CAAC,GAAE,GAAG,aAAa,KAAK,MAAK,IAAE,GAAE,EAAG;AAAE,GAAE,aAAa,GAAE;AAAC,cAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,SAAQ,KAAK,OAAO,GAAE,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,QAAO,KAAK,MAAM,GAAE,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,SAAQ,KAAK,OAAO,GAAE,MAAM,KAAK,KAAK,SAAS,QAAQ,QAAM,CAAC,CAAC,EAAE,QAAS,CAAAC,OAAG;AAAC,WAAO,OAAOA,IAAE,EAAC,SAAQ,KAAK,QAAO,CAAC,GAAEA,GAAE,aAAa,cAAa,KAAK,IAAI,KAAK,UAAQ,UAAQ,QAAO,KAAK,MAAM,CAAC;AAAA,EAAC,CAAE,GAAE,GAAG,MAAM,CAAC,KAAG,iBAAe,EAAE,QAAM,GAAG,eAAe,KAAK,IAAI;AAAC,GAAE,aAAa,GAAE;AAAC,OAAK,UAAQ,CAAC,WAAU,SAAS,EAAE,SAAS,EAAE,IAAI,GAAE,aAAa,KAAK,OAAO,OAAO,GAAE,KAAK,OAAO,UAAQ,WAAY,MAAI;AAAC,gBAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,SAAQ,KAAK,OAAO,GAAE,GAAG,eAAe,KAAK,IAAI;AAAA,EAAC,GAAG,KAAK,UAAQ,MAAI,CAAC;AAAC,GAAE,eAAe,GAAE;AAAC,QAAK,EAAC,UAAS,EAAC,IAAE,KAAK;AAAS,MAAG,KAAG,KAAK,OAAO,cAAa;AAAC,UAAM,IAAE,KAAK,SAAO,KAAK,eAAa,MAAI,KAAK,IAAI;AAAE,SAAK,eAAe,QAAQ,KAAG,KAAK,WAAS,KAAK,UAAQ,EAAE,WAAS,EAAE,SAAO,CAAC,CAAC;AAAA,EAAC;AAAC,GAAE,gBAAe;AAAC,SAAO,OAAO,EAAC,GAAG,KAAK,MAAM,MAAK,CAAC,EAAE,OAAQ,OAAG,CAAC,GAAG,MAAM,CAAC,KAAG,GAAG,OAAO,CAAC,KAAG,EAAE,WAAW,QAAQ,CAAE,EAAE,QAAS,OAAG;AAAC,SAAK,SAAS,UAAU,MAAM,YAAY,GAAE,KAAK,MAAM,MAAM,iBAAiB,CAAC,CAAC,GAAE,KAAK,MAAM,MAAM,eAAe,CAAC;AAAA,EAAC,CAAE,GAAE,GAAG,MAAM,KAAK,MAAM,KAAK,KAAG,KAAK,MAAM,gBAAgB,OAAO;AAAC,EAAC;AAAE,IAAM,YAAN,MAAe;AAAA,EAAC,YAAY,GAAE;AAAC,sBAAkB,MAAK,cAAc,MAAI;AAAC,YAAK,EAAC,QAAOA,GAAC,IAAE,MAAK,EAAC,UAAS,EAAC,IAAEA;AAAE,MAAAA,GAAE,QAAM,MAAG,YAAY,EAAE,WAAUA,GAAE,OAAO,WAAW,SAAQ,IAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,UAAU,CAACA,KAAE,SAAK;AAAC,YAAK,EAAC,QAAO,EAAC,IAAE;AAAK,QAAE,OAAO,SAAS,UAAQ,eAAe,KAAK,GAAE,QAAO,iBAAgB,KAAK,WAAUA,IAAE,KAAE,GAAE,eAAe,KAAK,GAAE,SAAS,MAAK,SAAQ,KAAK,YAAWA,EAAC,GAAE,KAAK,KAAK,GAAE,SAAS,MAAK,cAAa,KAAK,UAAU;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,aAAa,MAAI;AAAC,YAAK,EAAC,QAAOA,GAAC,IAAE,MAAK,EAAC,QAAO,GAAE,UAAS,GAAE,QAAO,EAAC,IAAEA;AAAE,OAAC,EAAE,SAAS,UAAQ,EAAE,SAAS,WAAS,GAAG,KAAKA,IAAE,EAAE,WAAU,iBAAgB,KAAK,WAAU,KAAE,GAAE,GAAG,KAAKA,IAAE,EAAE,WAAU,4EAA4E,CAAAD,OAAG;AAAC,cAAK,EAAC,UAASM,GAAC,IAAE;AAAE,QAAAA,MAAG,sBAAoBN,GAAE,SAAOM,GAAE,UAAQ,OAAGA,GAAE,QAAM;AAAI,YAAIF,KAAE;AAAE,SAAC,cAAa,aAAY,WAAW,EAAE,SAASJ,GAAE,IAAI,MAAI,GAAG,eAAe,KAAKC,IAAE,IAAE,GAAEG,KAAEH,GAAE,QAAM,MAAI,MAAK,aAAa,EAAE,QAAQ,GAAE,EAAE,WAAS,WAAY,MAAI,GAAG,eAAe,KAAKA,IAAE,KAAE,GAAGG,EAAC;AAAA,MAAC,CAAE;AAAE,YAAM,IAAE,MAAI;AAAC,YAAG,CAACH,GAAE,WAASA,GAAE,OAAO,MAAM,QAAQ;AAAO,cAAMD,KAAE,EAAE,SAAQ,EAAC,QAAOG,GAAC,IAAEF,GAAE,YAAW,CAACK,IAAEF,EAAC,IAAE,eAAe,KAAKH,EAAC,GAAE,IAAE,YAAY,iBAAiBK,EAAC,MAAMF,EAAC,EAAE;AAAE,YAAG,CAACD,GAAE,QAAO,MAAK,KAAGH,GAAE,MAAM,QAAM,MAAKA,GAAE,MAAM,SAAO,SAAOA,GAAE,MAAM,WAAS,MAAKA,GAAE,MAAM,SAAO;AAAO,cAAK,CAAC,GAAE,CAAC,IAAE,gBAAgB,GAAE,IAAE,IAAE,IAAEM,KAAEF;AAAE,aAAGJ,GAAE,MAAM,QAAM,IAAE,SAAO,QAAOA,GAAE,MAAM,SAAO,IAAE,SAAO,WAASA,GAAE,MAAM,WAAS,IAAE,IAAEI,KAAEE,KAAE,OAAK,MAAKN,GAAE,MAAM,SAAO,IAAE,WAAS;AAAA,MAAK,GAAE,IAAE,MAAI;AAAC,qBAAa,EAAE,OAAO,GAAE,EAAE,UAAQ,WAAW,GAAE,EAAE;AAAA,MAAC;AAAE,SAAG,KAAKC,IAAE,EAAE,WAAU,kCAAkC,CAAAD,OAAG;AAAC,cAAK,EAAC,QAAOG,GAAC,IAAEF,GAAE;AAAW,YAAGE,OAAI,EAAE,UAAU;AAAO,YAAG,CAACF,GAAE,WAAS,GAAG,MAAMA,GAAE,OAAO,KAAK,EAAE;AAAO,UAAE;AAAE,SAAC,sBAAoBD,GAAE,OAAK,KAAG,KAAK,KAAKC,IAAE,QAAO,UAAS,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,SAAS,MAAI;AAAC,YAAK,EAAC,QAAOA,GAAC,IAAE,MAAK,EAAC,UAAS,EAAC,IAAEA;AAAE,UAAG,GAAG,KAAKA,IAAEA,GAAE,OAAM,6BAA6B,CAAAD,OAAG,SAAS,WAAW,KAAKC,IAAED,EAAC,CAAE,GAAE,GAAG,KAAKC,IAAEA,GAAE,OAAM,4CAA4C,CAAAD,OAAG,SAAS,eAAe,KAAKC,IAAED,EAAC,CAAE,GAAE,GAAG,KAAKC,IAAEA,GAAE,OAAM,SAAS,MAAI;AAAC,QAAAA,GAAE,WAASA,GAAE,WAASA,GAAE,OAAO,eAAaA,GAAE,QAAQ,GAAEA,GAAE,MAAM;AAAA,MAAE,CAAE,GAAE,GAAG,KAAKA,IAAEA,GAAE,OAAM,mCAAmC,CAAAD,OAAG,SAAS,eAAe,KAAKC,IAAED,EAAC,CAAE,GAAE,GAAG,KAAKC,IAAEA,GAAE,OAAM,gBAAgB,CAAAD,OAAG,SAAS,aAAa,KAAKC,IAAED,EAAC,CAAE,GAAE,GAAG,KAAKC,IAAEA,GAAE,OAAM,+CAA+C,CAAAD,OAAG,GAAG,aAAa,KAAKC,IAAED,EAAC,CAAE,GAAE,GAAG,KAAKC,IAAEA,GAAE,OAAM,kCAAkC,CAAAD,OAAG,GAAG,aAAa,KAAKC,IAAED,EAAC,CAAE,GAAEC,GAAE,UAAU,MAAIA,GAAE,OAAO,eAAa,CAACA,GAAE,SAAQ;AAAC,cAAMC,KAAE,WAAW,KAAKD,IAAE,IAAIA,GAAE,OAAO,WAAW,KAAK,EAAE;AAAE,YAAG,CAAC,GAAG,QAAQC,EAAC,EAAE;AAAO,WAAG,KAAKD,IAAE,EAAE,WAAU,SAAS,OAAG;AAAC,WAAC,CAAC,EAAE,WAAUC,EAAC,EAAE,SAAS,EAAE,MAAM,KAAGA,GAAE,SAAS,EAAE,MAAM,OAAKD,GAAE,SAAOA,GAAE,OAAO,iBAAeA,GAAE,SAAO,KAAK,MAAM,GAAEA,GAAE,SAAQ,SAAS,GAAE,KAAK,MAAM,GAAG,MAAI;AAAC,2BAAeA,GAAE,KAAK,CAAC;AAAA,UAAC,GAAG,MAAM,KAAG,KAAK,MAAM,GAAG,MAAI;AAAC,2BAAeA,GAAE,WAAW,CAAC;AAAA,UAAC,GAAG,MAAM;AAAA,QAAG,CAAE;AAAA,MAAC;AAAC,MAAAA,GAAE,UAAU,MAAIA,GAAE,OAAO,sBAAoB,GAAG,KAAKA,IAAE,EAAE,SAAQ,eAAe,CAAAA,OAAG;AAAC,QAAAA,GAAE,eAAe;AAAA,MAAC,GAAG,KAAE,GAAE,GAAG,KAAKA,IAAEA,GAAE,OAAM,gBAAgB,MAAI;AAAC,QAAAA,GAAE,QAAQ,IAAI,EAAC,QAAOA,GAAE,QAAO,OAAMA,GAAE,MAAK,CAAC;AAAA,MAAC,CAAE,GAAE,GAAG,KAAKA,IAAEA,GAAE,OAAM,cAAc,MAAI;AAAC,iBAAS,cAAc,KAAKA,IAAE,OAAO,GAAEA,GAAE,QAAQ,IAAI,EAAC,OAAMA,GAAE,MAAK,CAAC;AAAA,MAAC,CAAE,GAAE,GAAG,KAAKA,IAAEA,GAAE,OAAM,iBAAiB,CAAAD,OAAG;AAAC,iBAAS,cAAc,KAAKC,IAAE,WAAU,MAAKD,GAAE,OAAO,OAAO;AAAA,MAAC,CAAE,GAAE,GAAG,KAAKC,IAAEA,GAAE,OAAM,uBAAuB,MAAI;AAAC,iBAAS,eAAe,KAAKA,EAAC;AAAA,MAAC,CAAE;AAAE,YAAM,IAAEA,GAAE,OAAO,OAAO,OAAO,CAAC,SAAQ,SAAS,CAAC,EAAE,KAAK,GAAG;AAAE,SAAG,KAAKA,IAAEA,GAAE,OAAM,GAAG,CAAAC,OAAG;AAAC,YAAG,EAAC,QAAO,IAAE,CAAC,EAAC,IAAEA;AAAE,oBAAUA,GAAE,SAAO,IAAED,GAAE,MAAM,QAAO,aAAa,KAAKA,IAAE,EAAE,WAAUC,GAAE,MAAK,MAAG,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,SAAS,CAACD,IAAE,GAAE,MAAI;AAAC,YAAK,EAAC,QAAO,EAAC,IAAE,MAAK,IAAE,EAAE,OAAO,UAAU,CAAC;AAAE,UAAI,IAAE;AAAG,SAAG,SAAS,CAAC,MAAI,IAAE,EAAE,KAAK,GAAEA,EAAC,IAAG,UAAK,KAAG,GAAG,SAAS,CAAC,KAAG,EAAE,KAAK,GAAEA,EAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,QAAQ,CAACA,IAAE,GAAE,GAAE,GAAE,IAAE,SAAK;AAAC,YAAK,EAAC,QAAO,EAAC,IAAE,MAAK,IAAE,EAAE,OAAO,UAAU,CAAC,GAAE,IAAE,GAAG,SAAS,CAAC;AAAE,SAAG,KAAK,GAAEA,IAAE,GAAG,CAAAA,OAAG,KAAK,MAAMA,IAAE,GAAE,CAAC,GAAG,KAAG,CAAC,CAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,YAAY,MAAI;AAAC,YAAK,EAAC,QAAOA,GAAC,IAAE,MAAK,EAAC,UAAS,EAAC,IAAEA,IAAE,IAAE,QAAQ,OAAK,WAAS;AAAQ,UAAG,EAAE,QAAQ,QAAM,MAAM,KAAK,EAAE,QAAQ,IAAI,EAAE,QAAS,CAAAD,OAAG;AAAC,aAAK,KAAKA,IAAE,SAAS,MAAI;AAAC,yBAAeC,GAAE,WAAW,CAAC;AAAA,QAAC,GAAG,MAAM;AAAA,MAAC,CAAE,GAAE,KAAK,KAAK,EAAE,QAAQ,SAAQ,SAAQA,GAAE,SAAQ,SAAS,GAAE,KAAK,KAAK,EAAE,QAAQ,QAAO,SAAS,MAAI;AAAC,QAAAA,GAAE,eAAa,KAAK,IAAI,GAAEA,GAAE,OAAO;AAAA,MAAC,GAAG,QAAQ,GAAE,KAAK,KAAK,EAAE,QAAQ,aAAY,SAAS,MAAI;AAAC,QAAAA,GAAE,eAAa,KAAK,IAAI,GAAEA,GAAE,QAAQ;AAAA,MAAC,GAAG,aAAa,GAAE,KAAK,KAAK,EAAE,QAAQ,MAAK,SAAS,MAAI;AAAC,QAAAA,GAAE,QAAM,CAACA,GAAE;AAAA,MAAK,GAAG,MAAM,GAAE,KAAK,KAAK,EAAE,QAAQ,UAAS,SAAS,MAAIA,GAAE,eAAe,CAAE,GAAE,KAAK,KAAK,EAAE,QAAQ,UAAS,SAAS,MAAI;AAAC,qBAAa,KAAKA,IAAEA,GAAE,OAAM,UAAU;AAAA,MAAC,GAAG,UAAU,GAAE,KAAK,KAAK,EAAE,QAAQ,YAAW,SAAS,MAAI;AAAC,QAAAA,GAAE,WAAW,OAAO;AAAA,MAAC,GAAG,YAAY,GAAE,KAAK,KAAK,EAAE,QAAQ,KAAI,SAAS,MAAI;AAAC,QAAAA,GAAE,MAAI;AAAA,MAAQ,GAAG,KAAK,GAAE,KAAK,KAAK,EAAE,QAAQ,SAAQ,SAAQA,GAAE,SAAQ,SAAS,GAAE,KAAK,KAAK,EAAE,QAAQ,UAAS,SAAS,CAAAD,OAAG;AAAC,QAAAA,GAAE,gBAAgB,GAAEA,GAAE,eAAe,GAAE,SAAS,WAAW,KAAKC,IAAED,EAAC;AAAA,MAAC,GAAG,MAAK,KAAE,GAAE,KAAK,KAAK,EAAE,QAAQ,UAAS,SAAS,CAAAA,OAAG;AAAC,SAAC,KAAI,OAAO,EAAE,SAASA,GAAE,GAAG,MAAI,YAAUA,GAAE,OAAKA,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,SAAS,WAAW,KAAKC,IAAED,EAAC,KAAG,SAAS,mBAAmB,KAAKC,IAAE,MAAK,IAAE;AAAA,MAAE,GAAG,MAAK,KAAE,GAAE,KAAK,KAAK,EAAE,SAAS,MAAK,WAAW,CAAAD,OAAG;AAAC,qBAAWA,GAAE,OAAK,SAAS,WAAW,KAAKC,IAAED,EAAC;AAAA,MAAC,CAAE,GAAE,KAAK,KAAK,EAAE,OAAO,MAAK,uBAAuB,CAAAC,OAAG;AAAC,cAAMC,KAAE,EAAE,SAAS,sBAAsB,GAAE,IAAE,MAAIA,GAAE,SAAOD,GAAE,QAAMC,GAAE;AAAM,QAAAD,GAAE,cAAc,aAAa,cAAa,CAAC;AAAA,MAAC,CAAE,GAAE,KAAK,KAAK,EAAE,OAAO,MAAK,uDAAuD,CAAAD,OAAG;AAAC,cAAME,KAAEF,GAAE,eAAc,IAAE;AAAiB,YAAG,GAAG,cAAcA,EAAC,KAAG,CAAC,CAAC,aAAY,YAAY,EAAE,SAASA,GAAE,GAAG,EAAE;AAAO,QAAAC,GAAE,eAAa,KAAK,IAAI;AAAE,cAAM,IAAEC,GAAE,aAAa,CAAC,GAAE,IAAE,CAAC,WAAU,YAAW,OAAO,EAAE,SAASF,GAAE,IAAI;AAAE,aAAG,KAAGE,GAAE,gBAAgB,CAAC,GAAE,eAAeD,GAAE,KAAK,CAAC,KAAG,CAAC,KAAGA,GAAE,YAAUC,GAAE,aAAa,GAAE,EAAE,GAAED,GAAE,MAAM;AAAA,MAAE,CAAE,GAAE,QAAQ,OAAM;AAAC,cAAMD,KAAE,YAAY,KAAKC,IAAE,qBAAqB;AAAE,cAAM,KAAKD,EAAC,EAAE,QAAS,CAAAC,OAAG,KAAK,KAAKA,IAAE,GAAG,CAAAA,OAAG,QAAQA,GAAE,MAAM,CAAE,CAAE;AAAA,MAAC;AAAC,WAAK,KAAK,EAAE,OAAO,MAAK,GAAG,CAAAD,OAAG;AAAC,cAAME,KAAEF,GAAE;AAAc,YAAI,IAAEE,GAAE,aAAa,YAAY;AAAE,WAAG,MAAM,CAAC,MAAI,IAAEA,GAAE,QAAOA,GAAE,gBAAgB,YAAY,GAAED,GAAE,cAAY,IAAEC,GAAE,MAAID,GAAE;AAAA,MAAQ,GAAG,MAAM,GAAE,KAAK,KAAK,EAAE,UAAS,mCAAmC,CAAAD,OAAG,SAAS,kBAAkB,KAAKC,IAAED,EAAC,CAAE,GAAE,KAAK,KAAK,EAAE,UAAS,uBAAuB,CAAAA,OAAG;AAAC,cAAK,EAAC,mBAAkBE,GAAC,IAAED;AAAE,QAAAC,MAAGA,GAAE,UAAQA,GAAE,UAAUF,EAAC;AAAA,MAAC,CAAE,GAAE,KAAK,KAAK,EAAE,UAAS,6BAA6B,MAAI;AAAC,cAAK,EAAC,mBAAkBA,GAAC,IAAEC;AAAE,QAAAD,MAAGA,GAAE,UAAQA,GAAE,QAAQ,OAAG,IAAE;AAAA,MAAC,CAAE,GAAE,KAAK,KAAK,EAAE,UAAS,wBAAwB,CAAAA,OAAG;AAAC,cAAK,EAAC,mBAAkBE,GAAC,IAAED;AAAE,QAAAC,MAAGA,GAAE,UAAQA,GAAE,eAAeF,EAAC;AAAA,MAAC,CAAE,GAAE,KAAK,KAAK,EAAE,UAAS,oBAAoB,CAAAA,OAAG;AAAC,cAAK,EAAC,mBAAkBE,GAAC,IAAED;AAAE,QAAAC,MAAGA,GAAE,UAAQA,GAAE,aAAaF,EAAC;AAAA,MAAC,CAAE,GAAE,QAAQ,YAAU,MAAM,KAAK,YAAY,KAAKC,IAAE,qBAAqB,CAAC,EAAE,QAAS,CAAAD,OAAG;AAAC,aAAK,KAAKA,IAAE,SAAS,CAAAA,OAAG,SAAS,gBAAgB,KAAKC,IAAED,GAAE,MAAM,CAAE;AAAA,MAAC,CAAE,GAAEC,GAAE,OAAO,gBAAc,CAAC,GAAG,QAAQ,EAAE,QAAQ,QAAQ,KAAG,KAAK,KAAK,EAAE,QAAQ,aAAY,SAAS,MAAI;AAAC,cAAIA,GAAE,gBAAcA,GAAE,OAAO,aAAW,CAACA,GAAE,OAAO,YAAW,SAAS,WAAW,KAAKA,EAAC;AAAA,MAAE,CAAE,GAAE,KAAK,KAAK,EAAE,OAAO,QAAO,GAAG,CAAAD,OAAG;AAAC,QAAAC,GAAE,SAAOD,GAAE,OAAO;AAAA,MAAK,GAAG,QAAQ,GAAE,KAAK,KAAK,EAAE,UAAS,yBAAyB,CAAAE,OAAG;AAAC,UAAE,SAAS,QAAM,CAACD,GAAE,SAAO,iBAAeC,GAAE;AAAA,MAAI,CAAE,GAAE,EAAE,cAAY,MAAM,KAAK,EAAE,WAAW,QAAQ,EAAE,OAAQ,CAAAD,OAAG,CAACA,GAAE,SAAS,EAAE,SAAS,CAAE,EAAE,QAAS,CAAAC,OAAG;AAAC,aAAK,KAAKA,IAAE,yBAAyB,CAAAA,OAAG;AAAC,YAAE,aAAW,EAAE,SAAS,QAAM,CAACD,GAAE,SAAO,iBAAeC,GAAE;AAAA,QAAK,CAAE;AAAA,MAAC,CAAE,GAAE,KAAK,KAAK,EAAE,UAAS,qDAAqD,CAAAD,OAAG;AAAC,UAAE,SAAS,UAAQ,CAAC,aAAY,YAAY,EAAE,SAASA,GAAE,IAAI;AAAA,MAAC,CAAE,GAAE,KAAK,KAAK,EAAE,UAAS,WAAW,MAAI;AAAC,cAAK,EAAC,QAAOC,IAAE,QAAO,EAAC,IAAED;AAAE,oBAAY,EAAE,UAASC,GAAE,WAAW,cAAa,IAAE,GAAE,GAAG,eAAe,KAAKD,IAAE,IAAE,GAAE,WAAY,MAAI;AAAC,sBAAY,EAAE,UAASC,GAAE,WAAW,cAAa,KAAE;AAAA,QAAC,GAAG,CAAC;AAAE,cAAM,IAAE,KAAK,QAAM,MAAI;AAAI,qBAAa,EAAE,QAAQ,GAAE,EAAE,WAAS,WAAY,MAAI,GAAG,eAAe,KAAKD,IAAE,KAAE,GAAG,CAAC;AAAA,MAAC,CAAE,GAAE,KAAK,KAAK,EAAE,OAAO,QAAO,SAAS,CAAAD,OAAG;AAAC,cAAME,KAAEF,GAAE,mCAAkC,CAAC,GAAE,CAAC,IAAE,CAACA,GAAE,QAAO,CAACA,GAAE,MAAM,EAAE,IAAK,CAAAC,OAAGC,KAAE,CAACD,KAAEA,EAAE,GAAE,IAAE,KAAK,KAAK,KAAK,IAAI,CAAC,IAAE,KAAK,IAAI,CAAC,IAAE,IAAE,CAAC;AAAE,QAAAA,GAAE,eAAe,IAAE,EAAE;AAAE,cAAK,EAAC,QAAO,EAAC,IAAEA,GAAE;AAAM,SAAC,MAAI,KAAG,IAAE,KAAG,OAAK,KAAG,IAAE,MAAID,GAAE,eAAe;AAAA,MAAC,GAAG,UAAS,KAAE;AAAA,IAAC,CAAE,GAAE,KAAK,SAAO,GAAE,KAAK,UAAQ,MAAK,KAAK,aAAW,MAAK,KAAK,cAAY,MAAK,KAAK,YAAU,KAAK,UAAU,KAAK,IAAI,GAAE,KAAK,aAAW,KAAK,WAAW,KAAK,IAAI,GAAE,KAAK,aAAW,KAAK,WAAW,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,UAAU,GAAE;AAAC,UAAK,EAAC,QAAO,EAAC,IAAE,MAAK,EAAC,UAAS,EAAC,IAAE,GAAE,EAAC,KAAI,GAAE,MAAK,GAAE,QAAO,GAAE,SAAQ,GAAE,SAAQ,GAAE,UAAS,EAAC,IAAE,GAAE,IAAE,cAAY,GAAE,IAAE,KAAG,MAAI,KAAK;AAAQ,QAAG,KAAG,KAAG,KAAG,EAAE;AAAO,QAAG,CAAC,EAAE;AAAO,QAAG,GAAE;AAAC,YAAMM,KAAE,SAAS;AAAc,UAAG,GAAG,QAAQA,EAAC,GAAE;AAAC,cAAK,EAAC,UAASH,GAAC,IAAE,EAAE,OAAO,WAAU,EAAC,MAAKC,GAAC,IAAE,EAAE;AAAO,YAAGE,OAAIF,MAAG,QAAQE,IAAEH,EAAC,EAAE;AAAO,YAAG,QAAM,EAAE,OAAK,QAAQG,IAAE,4BAA4B,EAAE;AAAA,MAAM;AAAC,cAAO,CAAC,KAAI,aAAY,WAAU,cAAa,aAAY,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,GAAG,EAAE,SAAS,CAAC,MAAI,EAAE,eAAe,GAAE,EAAE,gBAAgB,IAAG,GAAE;AAAA,QAAC,KAAI;AAAA,QAAI,KAAI;AAAA,QAAI,KAAI;AAAA,QAAI,KAAI;AAAA,QAAI,KAAI;AAAA,QAAI,KAAI;AAAA,QAAI,KAAI;AAAA,QAAI,KAAI;AAAA,QAAI,KAAI;AAAA,QAAI,KAAI;AAAI,gBAAI,IAAE,SAAS,GAAE,EAAE,GAAE,EAAE,cAAY,EAAE,WAAS,KAAG;AAAG;AAAA,QAAM,KAAI;AAAA,QAAI,KAAI;AAAI,eAAG,eAAe,EAAE,WAAW,CAAC;AAAE;AAAA,QAAM,KAAI;AAAU,YAAE,eAAe,GAAE;AAAE;AAAA,QAAM,KAAI;AAAY,YAAE,eAAe,GAAE;AAAE;AAAA,QAAM,KAAI;AAAI,gBAAI,EAAE,QAAM,CAAC,EAAE;AAAO;AAAA,QAAM,KAAI;AAAa,YAAE,QAAQ;AAAE;AAAA,QAAM,KAAI;AAAY,YAAE,OAAO;AAAE;AAAA,QAAM,KAAI;AAAI,YAAE,WAAW,OAAO;AAAE;AAAA,QAAM,KAAI;AAAI,eAAG,EAAE,eAAe;AAAE;AAAA,QAAM,KAAI;AAAI,YAAE,OAAK,CAAC,EAAE;AAAA,MAAI;AAAC,mBAAW,KAAG,CAAC,EAAE,WAAW,eAAa,EAAE,WAAW,UAAQ,EAAE,WAAW,OAAO,GAAE,KAAK,UAAQ;AAAA,IAAC,MAAM,MAAK,UAAQ;AAAK,QAAI;AAAA,EAAC;AAAA,EAAC,WAAW,GAAE;AAAC,aAAS,WAAW,KAAK,KAAK,QAAO,CAAC;AAAA,EAAC;AAAC;AAAmK,SAAS,qBAAqB,GAAE,GAAE;AAAC,SAAO,EAAE,IAAE,EAAC,SAAQ,CAAC,EAAC,GAAE,EAAE,OAAO,GAAE,EAAE;AAAO;AAAC,IAAI,aAAW,qBAAsB,SAAS,GAAE,GAAE;AAAC,IAAE,UAAQ,WAAU;AAAC,QAAII,KAAE,WAAU;AAAA,IAAC,GAAEC,KAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC;AAAE,aAAS,EAAED,IAAEC,IAAE;AAAC,MAAAD,KAAEA,GAAE,OAAKA,KAAE,CAACA,EAAC;AAAE,UAAIE,IAAEC,IAAEC,IAAEC,KAAE,CAAC,GAAEC,KAAEN,GAAE,QAAOO,KAAED;AAAE,WAAIJ,KAAE,SAASF,IAAEQ,IAAE;AAAC,QAAAA,GAAE,UAAQH,GAAE,KAAKL,EAAC,GAAE,EAAEO,MAAGN,GAAEI,EAAC;AAAA,MAAC,GAAEC,OAAK,CAAAH,KAAEH,GAAEM,EAAC,IAAGF,KAAE,EAAED,EAAC,KAAGD,GAAEC,IAAEC,EAAC,KAAG,EAAED,EAAC,IAAE,EAAEA,EAAC,KAAG,CAAC,GAAG,KAAKD,EAAC;AAAA,IAAC;AAAC,aAAS,EAAEF,IAAEC,IAAE;AAAC,UAAGD,IAAE;AAAC,YAAIE,KAAE,EAAEF,EAAC;AAAE,YAAG,EAAEA,EAAC,IAAEC,IAAEC,GAAE,QAAKA,GAAE,SAAQ,CAAAA,GAAE,CAAC,EAAEF,IAAEC,EAAC,GAAEC,GAAE,OAAO,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,EAAED,IAAEO,IAAE;AAAC,MAAAP,GAAE,SAAOA,KAAE,EAAC,SAAQA,GAAC,IAAGO,GAAE,UAAQP,GAAE,SAAOD,IAAGQ,EAAC,KAAGP,GAAE,WAASD,IAAGC,EAAC;AAAA,IAAC;AAAC,aAAS,EAAEA,IAAEO,IAAEC,IAAEP,IAAE;AAAC,UAAIC,IAAEC,IAAEE,KAAE,UAASC,KAAEE,GAAE,OAAM,KAAGA,GAAE,cAAY,KAAG,GAAE,IAAEA,GAAE,UAAQT,IAAE,IAAEC,GAAE,QAAQ,aAAY,EAAE,GAAE,IAAEA,GAAE,QAAQ,eAAc,EAAE;AAAE,MAAAC,KAAEA,MAAG,GAAE,iBAAiB,KAAK,CAAC,MAAIE,KAAEE,GAAE,cAAc,MAAM,GAAG,MAAI,cAAaF,GAAE,OAAK,IAAGD,KAAE,eAAcC,OAAIA,GAAE,YAAUD,KAAE,GAAEC,GAAE,MAAI,WAAUA,GAAE,KAAG,YAAU,oCAAoC,KAAK,CAAC,KAAGA,KAAEE,GAAE,cAAc,KAAK,GAAG,MAAI,MAAIF,KAAEE,GAAE,cAAc,QAAQ,GAAG,MAAIL,IAAEG,GAAE,QAAM,WAASG,MAAGA,KAAGH,GAAE,SAAOA,GAAE,UAAQA,GAAE,eAAa,SAASJ,IAAE;AAAC,YAAIM,KAAEN,GAAE,KAAK,CAAC;AAAE,YAAGG,GAAE,KAAG;AAAC,UAAAC,GAAE,MAAM,QAAQ,WAASE,KAAE;AAAA,QAAI,SAAON,IAAE;AAAC,gBAAIA,GAAE,SAAOM,KAAE;AAAA,QAAI;AAAC,YAAG,OAAKA,IAAE;AAAC,eAAIJ,MAAG,KAAG,EAAE,QAAO,EAAED,IAAEO,IAAEC,IAAEP,EAAC;AAAA,QAAC,WAAS,aAAWE,GAAE,OAAK,WAASA,GAAE,GAAG,QAAOA,GAAE,MAAI;AAAa,QAAAI,GAAEP,IAAEK,IAAEN,GAAE,gBAAgB;AAAA,MAAC,GAAE,UAAK,EAAEC,IAAEG,EAAC,KAAGE,GAAE,KAAK,YAAYF,EAAC;AAAA,IAAC;AAAC,aAAS,EAAEJ,IAAEC,IAAEO,IAAE;AAAC,UAAIC,IAAEP,IAAEC,MAAGH,KAAEA,GAAE,OAAKA,KAAE,CAACA,EAAC,GAAG,QAAOI,KAAED,IAAEG,KAAE,CAAC;AAAE,WAAIG,KAAE,SAAST,IAAEQ,IAAEC,IAAE;AAAC,YAAG,OAAKD,MAAGF,GAAE,KAAKN,EAAC,GAAE,OAAKQ,IAAE;AAAC,cAAG,CAACC,GAAE;AAAO,UAAAH,GAAE,KAAKN,EAAC;AAAA,QAAC;AAAC,UAAEG,MAAGF,GAAEK,EAAC;AAAA,MAAC,GAAEJ,KAAE,GAAEA,KAAEE,IAAEF,KAAI,GAAEF,GAAEE,EAAC,GAAEO,IAAED,EAAC;AAAA,IAAC;AAAC,aAAS,EAAER,IAAEQ,IAAEC,IAAE;AAAC,UAAIP,IAAEG;AAAE,UAAGG,MAAGA,GAAE,SAAON,KAAEM,KAAGH,MAAGH,KAAEO,KAAED,OAAI,CAAC,GAAEN,IAAE;AAAC,YAAGA,MAAKD,GAAE,OAAK;AAAS,QAAAA,GAAEC,EAAC,IAAE;AAAA,MAAE;AAAC,eAASK,GAAEN,IAAEO,IAAE;AAAC,UAAER,IAAG,SAASA,IAAE;AAAC,YAAEK,IAAEL,EAAC,GAAEC,MAAG,EAAE,EAAC,SAAQA,IAAE,OAAMO,GAAC,GAAER,EAAC,GAAE,EAAEE,IAAEF,EAAC;AAAA,QAAC,GAAGK,EAAC;AAAA,MAAC;AAAC,UAAGA,GAAE,cAAc,QAAO,IAAI,QAAQE,EAAC;AAAE,MAAAA,GAAE;AAAA,IAAC;AAAC,WAAO,EAAE,QAAM,SAASP,IAAEC,IAAE;AAAC,aAAO,EAAED,IAAG,SAASA,IAAE;AAAC,UAAEC,IAAED,EAAC;AAAA,MAAC,CAAE,GAAE;AAAA,IAAC,GAAE,EAAE,OAAK,SAASA,IAAE;AAAC,QAAEA,IAAE,CAAC,CAAC;AAAA,IAAC,GAAE,EAAE,QAAM,WAAU;AAAC,MAAAC,KAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC;AAAA,IAAC,GAAE,EAAE,YAAU,SAASD,IAAE;AAAC,aAAOA,MAAKC;AAAA,IAAC,GAAE;AAAA,EAAC,EAAE;AAAC,CAAE;AAAE,SAAS,WAAW,GAAE;AAAC,SAAO,IAAI,QAAS,CAAC,GAAE,MAAI;AAAC,eAAW,GAAE,EAAC,SAAQ,GAAE,OAAM,EAAC,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,UAAU,GAAE;AAAC,MAAG,GAAG,MAAM,CAAC,EAAE,QAAO;AAAK,MAAG,GAAG,OAAO,OAAO,CAAC,CAAC,EAAE,QAAO;AAAE,SAAO,EAAE,MAAM,iCAAiC,IAAE,OAAO,KAAG;AAAC;AAAC,SAAS,UAAU,GAAE;AAAC,QAAM,IAAE,EAAE,MAAM,wDAAwD;AAAE,SAAO,KAAG,MAAI,EAAE,SAAO,EAAE,CAAC,IAAE;AAAI;AAAC,SAAS,sBAAsB,GAAE;AAAC,OAAG,CAAC,KAAK,MAAM,cAAY,KAAK,MAAM,YAAU,OAAI,KAAK,MAAM,WAAS,MAAI,KAAK,MAAM,SAAO,CAAC,GAAE,aAAa,KAAK,MAAK,KAAK,OAAM,IAAE,SAAO,OAAO;AAAE;AAAC,IAAM,QAAM,EAAC,QAAO;AAAC,QAAM,IAAE;AAAK,cAAY,EAAE,SAAS,SAAQ,EAAE,OAAO,WAAW,OAAM,IAAE,GAAE,EAAE,QAAQ,QAAM,EAAE,OAAO,MAAM,SAAQ,eAAe,KAAK,CAAC,GAAE,GAAG,OAAO,OAAO,KAAK,IAAE,MAAM,MAAM,KAAK,CAAC,IAAE,WAAW,EAAE,OAAO,KAAK,MAAM,GAAG,EAAE,KAAM,MAAI;AAAC,UAAM,MAAM,KAAK,CAAC;AAAA,EAAC,CAAE,EAAE,MAAO,OAAG;AAAC,MAAE,MAAM,KAAK,wCAAuC,CAAC;AAAA,EAAC,CAAE;AAAC,GAAE,QAAO;AAAC,QAAM,IAAE,MAAK,IAAE,EAAE,OAAO,OAAM,EAAC,SAAQ,GAAE,gBAAe,GAAE,GAAG,EAAC,IAAE;AAAE,MAAI,IAAE,EAAE,MAAM,aAAa,KAAK,GAAE,IAAE;AAAG,KAAG,MAAM,CAAC,KAAG,IAAE,EAAE,MAAM,aAAa,EAAE,OAAO,WAAW,MAAM,EAAE,GAAE,IAAE,EAAE,MAAM,aAAa,EAAE,OAAO,WAAW,MAAM,IAAI,KAAG,IAAE,UAAU,CAAC;AAAE,QAAM,IAAE,IAAE,EAAC,GAAE,EAAC,IAAE,CAAC;AAAE,OAAG,OAAO,OAAO,GAAE,EAAC,UAAS,OAAG,UAAS,MAAE,CAAC;AAAE,QAAM,IAAE,eAAe,EAAC,MAAK,EAAE,OAAO,KAAK,QAAO,UAAS,EAAE,UAAS,OAAM,EAAE,OAAM,SAAQ,SAAQ,aAAY,EAAE,OAAO,aAAY,GAAG,GAAE,GAAG,EAAC,CAAC,GAAE,IAAE,UAAU,CAAC,GAAE,IAAE,cAAc,QAAQ,GAAE,IAAE,OAAO,EAAE,OAAO,KAAK,MAAM,QAAO,GAAE,CAAC;AAAE,MAAG,EAAE,aAAa,OAAM,CAAC,GAAE,EAAE,aAAa,mBAAkB,EAAE,GAAE,EAAE,aAAa,SAAQ,CAAC,YAAW,cAAa,sBAAqB,mBAAkB,iBAAgB,WAAW,EAAE,KAAK,IAAI,CAAC,GAAE,GAAG,MAAM,CAAC,KAAG,EAAE,aAAa,kBAAiB,CAAC,GAAE,KAAG,CAAC,EAAE,eAAe,GAAE,aAAa,eAAc,EAAE,MAAM,GAAE,EAAE,QAAM,eAAe,GAAE,EAAE,KAAK;AAAA,OAAM;AAAC,UAAMA,KAAE,cAAc,OAAM,EAAC,OAAM,EAAE,OAAO,WAAW,gBAAe,eAAc,EAAE,OAAM,CAAC;AAAE,IAAAA,GAAE,YAAY,CAAC,GAAE,EAAE,QAAM,eAAeA,IAAE,EAAE,KAAK;AAAA,EAAC;AAAC,IAAE,kBAAgB,MAAM,OAAO,EAAE,OAAO,KAAK,MAAM,KAAI,CAAC,CAAC,EAAE,KAAM,CAAAA,OAAG;AAAC,KAAC,GAAG,MAAMA,EAAC,KAAGA,GAAE,iBAAe,GAAG,UAAU,KAAK,GAAEA,GAAE,aAAa,EAAE,MAAO,MAAI;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE,GAAE,EAAE,QAAM,IAAI,OAAO,MAAM,OAAO,GAAE,EAAC,WAAU,EAAE,OAAO,WAAU,OAAM,EAAE,MAAK,CAAC,GAAE,EAAE,MAAM,SAAO,MAAG,EAAE,MAAM,cAAY,GAAE,EAAE,UAAU,MAAI,EAAE,MAAM,iBAAiB,GAAE,EAAE,MAAM,OAAK,OAAK,sBAAsB,KAAK,GAAE,IAAE,GAAE,EAAE,MAAM,KAAK,IAAG,EAAE,MAAM,QAAM,OAAK,sBAAsB,KAAK,GAAE,KAAE,GAAE,EAAE,MAAM,MAAM,IAAG,EAAE,MAAM,OAAK,MAAI;AAAC,MAAE,MAAM,GAAE,EAAE,cAAY;AAAA,EAAC;AAAE,MAAG,EAAC,aAAY,EAAC,IAAE,EAAE;AAAM,SAAO,eAAe,EAAE,OAAM,eAAc,EAAC,KAAI,MAAI,GAAE,IAAIA,IAAE;AAAC,UAAK,EAAC,OAAMO,IAAE,OAAMC,IAAE,QAAOP,IAAE,QAAOC,GAAC,IAAE,GAAEC,KAAEF,MAAG,CAACM,GAAE;AAAU,IAAAC,GAAE,UAAQ,MAAG,aAAa,KAAK,GAAEA,IAAE,SAAS,GAAE,QAAQ,QAAQL,MAAGI,GAAE,UAAU,CAAC,CAAC,EAAE,KAAM,MAAIA,GAAE,eAAeP,EAAC,CAAE,EAAE,KAAM,MAAIG,MAAGI,GAAE,MAAM,CAAE,EAAE,KAAM,MAAIJ,MAAGI,GAAE,UAAUL,EAAC,CAAE,EAAE,MAAO,MAAI;AAAA,IAAC,CAAE;AAAA,EAAC,EAAC,CAAC;AAAE,MAAI,IAAE,EAAE,OAAO,MAAM;AAAS,SAAO,eAAe,EAAE,OAAM,gBAAe,EAAC,KAAI,MAAI,GAAE,IAAIF,IAAE;AAAC,MAAE,MAAM,gBAAgBA,EAAC,EAAE,KAAM,MAAI;AAAC,UAAEA,IAAE,aAAa,KAAK,GAAE,EAAE,OAAM,YAAY;AAAA,IAAC,CAAE,EAAE,MAAO,MAAI;AAAC,QAAE,QAAQ,QAAM,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,EAAC,CAAC;AAAE,MAAG,EAAC,QAAO,EAAC,IAAE,EAAE;AAAO,SAAO,eAAe,EAAE,OAAM,UAAS,EAAC,KAAI,MAAI,GAAE,IAAIA,IAAE;AAAC,MAAE,MAAM,UAAUA,EAAC,EAAE,KAAM,MAAI;AAAC,UAAEA,IAAE,aAAa,KAAK,GAAE,EAAE,OAAM,cAAc;AAAA,IAAC,CAAE;AAAA,EAAC,EAAC,CAAC;AAAE,MAAG,EAAC,OAAM,EAAC,IAAE,EAAE;AAAO,SAAO,eAAe,EAAE,OAAM,SAAQ,EAAC,KAAI,MAAI,GAAE,IAAIA,IAAE;AAAC,UAAMO,KAAE,CAAC,CAAC,GAAG,QAAQP,EAAC,KAAGA;AAAE,MAAE,MAAM,SAAS,CAAC,CAACO,MAAG,EAAE,OAAO,KAAK,EAAE,KAAM,MAAI;AAAC,UAAEA,IAAE,aAAa,KAAK,GAAE,EAAE,OAAM,cAAc;AAAA,IAAC,CAAE;AAAA,EAAC,EAAC,CAAC;AAAE,MAAI,GAAE,EAAC,MAAK,EAAC,IAAE,EAAE;AAAO,SAAO,eAAe,EAAE,OAAM,QAAO,EAAC,KAAI,MAAI,GAAE,IAAIP,IAAE;AAAC,UAAMO,KAAE,GAAG,QAAQP,EAAC,IAAEA,KAAE,EAAE,OAAO,KAAK;AAAO,MAAE,MAAM,QAAQO,EAAC,EAAE,KAAM,MAAI;AAAC,UAAEA;AAAA,IAAC,CAAE;AAAA,EAAC,EAAC,CAAC,GAAE,EAAE,MAAM,YAAY,EAAE,KAAM,CAAAP,OAAG;AAAC,QAAEA,IAAE,SAAS,eAAe,KAAK,CAAC;AAAA,EAAC,CAAE,EAAE,MAAO,CAAAD,OAAG;AAAC,SAAK,MAAM,KAAKA,EAAC;AAAA,EAAC,CAAE,GAAE,OAAO,eAAe,EAAE,OAAM,cAAa,EAAC,KAAI,MAAI,EAAC,CAAC,GAAE,OAAO,eAAe,EAAE,OAAM,SAAQ,EAAC,KAAI,MAAI,EAAE,gBAAc,EAAE,SAAQ,CAAC,GAAE,QAAQ,IAAI,CAAC,EAAE,MAAM,cAAc,GAAE,EAAE,MAAM,eAAe,CAAC,CAAC,EAAE,KAAM,CAAAC,OAAG;AAAC,UAAK,CAACO,IAAEC,EAAC,IAAER;AAAE,MAAE,MAAM,QAAM,iBAAiBO,IAAEC,EAAC,GAAE,eAAe,KAAK,IAAI;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,aAAa,EAAE,OAAO,SAAS,EAAE,KAAM,CAAAR,OAAG;AAAC,MAAE,OAAO,YAAUA;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,cAAc,EAAE,KAAM,CAAAA,OAAG;AAAC,MAAE,OAAO,QAAMA,IAAE,GAAG,SAAS,KAAK,IAAI;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,eAAe,EAAE,KAAM,CAAAA,OAAG;AAAC,QAAEA,IAAE,aAAa,KAAK,GAAE,EAAE,OAAM,YAAY;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,YAAY,EAAE,KAAM,CAAAA,OAAG;AAAC,MAAE,MAAM,WAASA,IAAE,aAAa,KAAK,GAAE,EAAE,OAAM,gBAAgB;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,cAAc,EAAE,KAAM,CAAAA,OAAG;AAAC,MAAE,MAAM,aAAWA,IAAE,SAAS,MAAM,KAAK,CAAC;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,GAAG,aAAa,CAAC,EAAC,MAAKA,KAAE,CAAC,EAAC,MAAI;AAAC,UAAMO,KAAEP,GAAE,IAAK,CAAAD,OAAG,UAAUA,GAAE,IAAI,CAAE;AAAE,aAAS,WAAW,KAAK,GAAEQ,EAAC;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,GAAG,UAAU,MAAI;AAAC,QAAG,EAAE,MAAM,UAAU,EAAE,KAAM,CAAAP,OAAG;AAAC,4BAAsB,KAAK,GAAE,CAACA,EAAC,GAAEA,MAAG,aAAa,KAAK,GAAE,EAAE,OAAM,SAAS;AAAA,IAAC,CAAE,GAAE,GAAG,QAAQ,EAAE,MAAM,OAAO,KAAG,EAAE,UAAU,IAAG;AAAC,QAAE,MAAM,QAAQ,aAAa,YAAW,EAAE;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,GAAG,eAAe,MAAI;AAAC,iBAAa,KAAK,GAAE,EAAE,OAAM,SAAS;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,GAAG,aAAa,MAAI;AAAC,iBAAa,KAAK,GAAE,EAAE,OAAM,SAAS;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,GAAG,QAAQ,MAAI;AAAC,0BAAsB,KAAK,GAAE,IAAE,GAAE,aAAa,KAAK,GAAE,EAAE,OAAM,SAAS;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,GAAG,SAAS,MAAI;AAAC,0BAAsB,KAAK,GAAE,KAAE;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,GAAG,cAAc,CAAAA,OAAG;AAAC,MAAE,MAAM,UAAQ,OAAG,IAAEA,GAAE,SAAQ,aAAa,KAAK,GAAE,EAAE,OAAM,YAAY;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,GAAG,YAAY,CAAAA,OAAG;AAAC,MAAE,MAAM,WAASA,GAAE,SAAQ,aAAa,KAAK,GAAE,EAAE,OAAM,UAAU,GAAE,MAAI,SAASA,GAAE,SAAQ,EAAE,KAAG,aAAa,KAAK,GAAE,EAAE,OAAM,gBAAgB,GAAE,EAAE,MAAM,YAAY,EAAE,KAAM,CAAAA,OAAG;AAAC,MAAAA,OAAI,EAAE,MAAM,aAAW,EAAE,MAAM,WAASA,IAAE,aAAa,KAAK,GAAE,EAAE,OAAM,gBAAgB;AAAA,IAAE,CAAE;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,GAAG,UAAU,MAAI;AAAC,MAAE,MAAM,UAAQ,OAAG,aAAa,KAAK,GAAE,EAAE,OAAM,QAAQ;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,GAAG,SAAS,MAAI;AAAC,MAAE,MAAM,SAAO,MAAG,aAAa,KAAK,GAAE,EAAE,OAAM,OAAO;AAAA,EAAC,CAAE,GAAE,EAAE,MAAM,GAAG,SAAS,CAAAA,OAAG;AAAC,MAAE,MAAM,QAAMA,IAAE,aAAa,KAAK,GAAE,EAAE,OAAM,OAAO;AAAA,EAAC,CAAE,GAAE,EAAE,kBAAgB,WAAY,MAAI,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC;AAAC,EAAC;AAAE,SAAS,QAAQ,GAAE;AAAC,MAAG,GAAG,MAAM,CAAC,EAAE,QAAO;AAAK,SAAO,EAAE,MAAM,8DAA8D,IAAE,OAAO,KAAG;AAAC;AAAC,SAAS,oBAAoB,GAAE;AAAC,OAAG,CAAC,KAAK,MAAM,cAAY,KAAK,MAAM,YAAU,OAAI,KAAK,MAAM,WAAS,MAAI,KAAK,MAAM,SAAO,CAAC,GAAE,aAAa,KAAK,MAAK,KAAK,OAAM,IAAE,SAAO,OAAO;AAAE;AAAC,SAAS,QAAQ,GAAE;AAAC,SAAO,EAAE,WAAS,qCAAmC,YAAU,OAAO,SAAS,WAAS,2BAAyB;AAAM;AAAC,IAAM,UAAQ,EAAC,QAAO;AAAC,MAAG,YAAY,KAAK,SAAS,SAAQ,KAAK,OAAO,WAAW,OAAM,IAAE,GAAE,GAAG,OAAO,OAAO,EAAE,KAAG,GAAG,SAAS,OAAO,GAAG,MAAM,EAAE,SAAQ,MAAM,KAAK,IAAI;AAAA,OAAM;AAAC,UAAM,IAAE,OAAO;AAAwB,WAAO,0BAAwB,MAAI;AAAC,SAAG,SAAS,CAAC,KAAG,EAAE,GAAE,QAAQ,MAAM,KAAK,IAAI;AAAA,IAAC,GAAE,WAAW,KAAK,OAAO,KAAK,QAAQ,GAAG,EAAE,MAAO,CAAAD,OAAG;AAAC,WAAK,MAAM,KAAK,8BAA6BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,GAAE,SAAS,GAAE;AAAC,QAAM,OAAO,KAAK,OAAO,KAAK,QAAQ,KAAI,CAAC,CAAC,EAAE,KAAM,CAAAA,OAAG;AAAC,QAAG,GAAG,OAAOA,EAAC,GAAE;AAAC,YAAK,EAAC,OAAM,GAAE,QAAO,GAAE,OAAM,EAAC,IAAEA;AAAE,WAAK,OAAO,QAAM,GAAE,GAAG,SAAS,KAAK,IAAI,GAAE,KAAK,MAAM,QAAM,iBAAiB,GAAE,CAAC;AAAA,IAAC;AAAC,mBAAe,KAAK,IAAI;AAAA,EAAC,CAAE,EAAE,MAAO,MAAI;AAAC,mBAAe,KAAK,IAAI;AAAA,EAAC,CAAE;AAAC,GAAE,QAAO;AAAC,QAAM,IAAE,MAAK,IAAE,EAAE,OAAO,SAAQ,IAAE,EAAE,SAAO,EAAE,MAAM,aAAa,IAAI;AAAE,MAAG,CAAC,GAAG,MAAM,CAAC,KAAG,EAAE,WAAW,UAAU,EAAE;AAAO,MAAI,IAAE,EAAE,MAAM,aAAa,KAAK;AAAE,KAAG,MAAM,CAAC,MAAI,IAAE,EAAE,MAAM,aAAa,KAAK,OAAO,WAAW,MAAM,EAAE;AAAG,QAAM,IAAE,QAAQ,CAAC,GAAE,IAAE,cAAc,OAAM,EAAC,IAAG,WAAW,EAAE,QAAQ,GAAE,eAAc,EAAE,iBAAe,EAAE,SAAO,OAAM,CAAC;AAAE,MAAG,EAAE,QAAM,eAAe,GAAE,EAAE,KAAK,GAAE,EAAE,gBAAe;AAAC,UAAMC,KAAE,CAAAD,OAAG,0BAA0B,CAAC,IAAIA,EAAC;AAAc,cAAUC,GAAE,QAAQ,GAAE,GAAG,EAAE,MAAO,MAAI,UAAUA,GAAE,IAAI,GAAE,GAAG,CAAE,EAAE,MAAO,MAAI,UAAUA,GAAE,IAAI,CAAC,CAAE,EAAE,KAAM,CAAAA,OAAG,GAAG,UAAU,KAAK,GAAEA,GAAE,GAAG,CAAE,EAAE,KAAM,CAAAA,OAAG;AAAC,MAAAA,GAAE,SAAS,QAAQ,MAAI,EAAE,SAAS,OAAO,MAAM,iBAAe;AAAA,IAAQ,CAAE,EAAE,MAAO,MAAI;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,IAAE,QAAM,IAAI,OAAO,GAAG,OAAO,EAAE,OAAM,EAAC,SAAQ,GAAE,MAAK,QAAQ,CAAC,GAAE,YAAW,OAAO,CAAC,GAAE,EAAC,UAAS,EAAE,OAAO,WAAS,IAAE,GAAE,IAAG,EAAE,OAAO,IAAG,UAAS,EAAE,UAAU,MAAI,EAAE,iBAAe,IAAE,GAAE,WAAU,GAAE,aAAY,EAAE,OAAO,eAAa,CAAC,EAAE,OAAO,WAAW,YAAU,IAAE,GAAE,gBAAe,EAAE,SAAS,SAAO,IAAE,GAAE,cAAa,EAAE,OAAO,SAAS,UAAS,iBAAgB,SAAO,OAAO,SAAS,OAAK,KAAI,GAAE,CAAC,GAAE,QAAO,EAAC,QAAQA,IAAE;AAAC,QAAG,CAAC,EAAE,MAAM,OAAM;AAAC,YAAMO,KAAEP,GAAE,MAAKQ,KAAE,EAAC,GAAE,wOAAuO,GAAE,wHAAuH,KAAI,sIAAqI,KAAI,wFAAuF,KAAI,uFAAsF,EAAED,EAAC,KAAG;AAA4B,QAAE,MAAM,QAAM,EAAC,MAAKA,IAAE,SAAQC,GAAC,GAAE,aAAa,KAAK,GAAE,EAAE,OAAM,OAAO;AAAA,IAAC;AAAA,EAAC,GAAE,qBAAqBR,IAAE;AAAC,UAAMO,KAAEP,GAAE;AAAO,MAAE,MAAM,eAAaO,GAAE,gBAAgB,GAAE,aAAa,KAAK,GAAE,EAAE,OAAM,YAAY;AAAA,EAAC,GAAE,QAAQA,IAAE;AAAC,QAAG,GAAG,SAAS,EAAE,MAAM,IAAI,EAAE;AAAO,UAAMC,KAAED,GAAE;AAAO,YAAQ,SAAS,KAAK,GAAE,CAAC,GAAE,EAAE,MAAM,OAAK,MAAI;AAAC,0BAAoB,KAAK,GAAE,IAAE,GAAEC,GAAE,UAAU;AAAA,IAAC,GAAE,EAAE,MAAM,QAAM,MAAI;AAAC,0BAAoB,KAAK,GAAE,KAAE,GAAEA,GAAE,WAAW;AAAA,IAAC,GAAE,EAAE,MAAM,OAAK,MAAI;AAAC,MAAAA,GAAE,UAAU;AAAA,IAAC,GAAE,EAAE,MAAM,WAASA,GAAE,YAAY,GAAE,EAAE,MAAM,SAAO,MAAG,EAAE,MAAM,cAAY,GAAE,OAAO,eAAe,EAAE,OAAM,eAAc,EAAC,KAAI,MAAI,OAAOA,GAAE,eAAe,CAAC,GAAE,IAAIR,IAAE;AAAC,QAAE,UAAQ,CAAC,EAAE,MAAM,aAAW,EAAE,MAAM,KAAK,GAAE,EAAE,MAAM,UAAQ,MAAG,aAAa,KAAK,GAAE,EAAE,OAAM,SAAS,GAAEQ,GAAE,OAAOR,EAAC;AAAA,IAAC,EAAC,CAAC,GAAE,OAAO,eAAe,EAAE,OAAM,gBAAe,EAAC,KAAI,MAAIQ,GAAE,gBAAgB,GAAE,IAAIT,IAAE;AAAC,MAAAS,GAAE,gBAAgBT,EAAC;AAAA,IAAC,EAAC,CAAC;AAAE,QAAG,EAAC,QAAOG,GAAC,IAAE,EAAE;AAAO,WAAO,eAAe,EAAE,OAAM,UAAS,EAAC,KAAI,MAAIA,IAAE,IAAIF,IAAE;AAAC,MAAAE,KAAEF,IAAEQ,GAAE,UAAU,MAAIN,EAAC,GAAE,aAAa,KAAK,GAAE,EAAE,OAAM,cAAc;AAAA,IAAC,EAAC,CAAC;AAAE,QAAG,EAAC,OAAM,EAAC,IAAE,EAAE;AAAO,WAAO,eAAe,EAAE,OAAM,SAAQ,EAAC,KAAI,MAAI,GAAE,IAAIF,IAAE;AAAC,YAAMO,KAAE,GAAG,QAAQP,EAAC,IAAEA,KAAE;AAAE,UAAEO,IAAEC,GAAED,KAAE,SAAO,QAAQ,EAAE,GAAEC,GAAE,UAAU,MAAIN,EAAC,GAAE,aAAa,KAAK,GAAE,EAAE,OAAM,cAAc;AAAA,IAAC,EAAC,CAAC,GAAE,OAAO,eAAe,EAAE,OAAM,cAAa,EAAC,KAAI,MAAIM,GAAE,YAAY,EAAC,CAAC,GAAE,OAAO,eAAe,EAAE,OAAM,SAAQ,EAAC,KAAI,MAAI,EAAE,gBAAc,EAAE,SAAQ,CAAC;AAAE,UAAM,IAAEA,GAAE,0BAA0B;AAAE,MAAE,QAAQ,QAAM,EAAE,OAAQ,CAAAR,OAAG,EAAE,OAAO,MAAM,QAAQ,SAASA,EAAC,CAAE,GAAE,EAAE,UAAU,MAAI,EAAE,kBAAgB,EAAE,MAAM,aAAa,YAAW,EAAE,GAAE,aAAa,KAAK,GAAE,EAAE,OAAM,YAAY,GAAE,aAAa,KAAK,GAAE,EAAE,OAAM,gBAAgB,GAAE,cAAc,EAAE,OAAO,SAAS,GAAE,EAAE,OAAO,YAAU,YAAa,MAAI;AAAC,QAAE,MAAM,WAASQ,GAAE,uBAAuB,IAAG,SAAO,EAAE,MAAM,gBAAc,EAAE,MAAM,eAAa,EAAE,MAAM,aAAW,aAAa,KAAK,GAAE,EAAE,OAAM,UAAU,GAAE,EAAE,MAAM,eAAa,EAAE,MAAM,UAAS,MAAI,EAAE,MAAM,aAAW,cAAc,EAAE,OAAO,SAAS,GAAE,aAAa,KAAK,GAAE,EAAE,OAAM,gBAAgB;AAAA,IAAE,GAAG,GAAG,GAAE,EAAE,kBAAgB,WAAY,MAAI,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;AAAA,EAAC,GAAE,cAAcD,IAAE;AAAC,UAAMC,KAAED,GAAE;AAAO,kBAAc,EAAE,OAAO,OAAO;AAAE,YAAO,EAAE,MAAM,WAAS,CAAC,GAAE,CAAC,EAAE,SAASA,GAAE,IAAI,MAAI,EAAE,MAAM,UAAQ,OAAG,aAAa,KAAK,GAAE,EAAE,OAAM,QAAQ,IAAGA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAG,qBAAa,KAAK,GAAE,EAAE,OAAM,YAAY,GAAE,EAAE,MAAM,WAASC,GAAE,uBAAuB,GAAE,aAAa,KAAK,GAAE,EAAE,OAAM,UAAU;AAAE;AAAA,MAAM,KAAK;AAAE,4BAAoB,KAAK,GAAE,KAAE,GAAE,EAAE,MAAM,QAAMA,GAAE,UAAU,GAAEA,GAAE,UAAU,KAAG,aAAa,KAAK,GAAE,EAAE,OAAM,OAAO;AAAE;AAAA,MAAM,KAAK;AAAE,UAAE,kBAAgB,CAAC,EAAE,OAAO,YAAU,EAAE,MAAM,UAAQ,CAAC,EAAE,MAAM,YAAU,EAAE,MAAM,MAAM,KAAG,oBAAoB,KAAK,GAAE,IAAE,GAAE,aAAa,KAAK,GAAE,EAAE,OAAM,SAAS,GAAE,EAAE,OAAO,UAAQ,YAAa,MAAI;AAAC,uBAAa,KAAK,GAAE,EAAE,OAAM,YAAY;AAAA,QAAC,GAAG,EAAE,GAAE,EAAE,MAAM,aAAWA,GAAE,YAAY,MAAI,EAAE,MAAM,WAASA,GAAE,YAAY,GAAE,aAAa,KAAK,GAAE,EAAE,OAAM,gBAAgB;AAAI;AAAA,MAAM,KAAK;AAAE,UAAE,SAAO,EAAE,MAAM,OAAO,GAAE,oBAAoB,KAAK,GAAE,KAAE;AAAE;AAAA,MAAM,KAAK;AAAE,qBAAa,KAAK,GAAE,EAAE,OAAM,SAAS;AAAA,IAAC;AAAC,iBAAa,KAAK,GAAE,EAAE,SAAS,WAAU,eAAc,OAAG,EAAC,MAAKD,GAAE,KAAI,CAAC;AAAA,EAAC,EAAC,EAAC,CAAC;AAAC,EAAC;AAA76K,IAA+6K,QAAM,EAAC,QAAO;AAAC,OAAK,SAAO,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,KAAK,QAAQ,OAAM,KAAK,IAAI,GAAE,IAAE,GAAE,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,SAAS,QAAQ,OAAM,KAAK,QAAQ,GAAE,IAAE,GAAE,KAAK,WAAS,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,KAAK,QAAQ,OAAM,OAAO,GAAE,IAAE,GAAE,KAAK,YAAU,KAAK,SAAS,UAAQ,cAAc,OAAM,EAAC,OAAM,KAAK,OAAO,WAAW,MAAK,CAAC,GAAE,KAAK,KAAK,OAAM,KAAK,SAAS,OAAO,GAAE,KAAK,SAAS,SAAO,cAAc,OAAM,EAAC,OAAM,KAAK,OAAO,WAAW,OAAM,CAAC,GAAE,KAAK,SAAS,QAAQ,YAAY,KAAK,SAAS,MAAM,IAAG,KAAK,UAAQ,MAAM,MAAM,KAAK,IAAI,IAAE,KAAK,YAAU,QAAQ,MAAM,KAAK,IAAI,IAAE,KAAK,WAAS,MAAM,MAAM,KAAK,IAAI,KAAG,KAAK,MAAM,KAAK,yBAAyB;AAAC,EAAC;AAA+I,IAAM,MAAN,MAAS;AAAA,EAAC,YAAY,GAAE;AAAC,sBAAkB,MAAK,QAAQ,MAAI;AAAC,WAAK,YAAU,GAAG,OAAO,OAAO,MAAM,KAAG,GAAG,OAAO,OAAO,OAAO,GAAG,IAAE,KAAK,MAAM,IAAE,WAAW,KAAK,OAAO,OAAO,KAAK,UAAU,GAAG,EAAE,KAAM,MAAI;AAAC,aAAK,MAAM;AAAA,MAAC,CAAE,EAAE,MAAO,MAAI;AAAC,aAAK,QAAQ,SAAQ,IAAI,MAAM,+BAA+B,CAAC;AAAA,MAAC,CAAE;AAAA,IAAE,CAAE,GAAE,kBAAkB,MAAK,SAAS,MAAI;AAAC,UAAIE;AAAE,WAAK,aAAWA,KAAE,MAAM,WAASA,GAAE,QAAQ,QAAQ,GAAEA,GAAE,SAAS,oBAAkBA,GAAE,SAAS,iBAAiB,QAAQ,GAAEA,GAAE,SAAS,UAAU,OAAO,IAAG,KAAK,iBAAiB,MAAK,SAAS,GAAE,KAAK,eAAe,KAAM,MAAI;AAAC,aAAK,iBAAiB,sBAAsB;AAAA,MAAC,CAAE,GAAE,KAAK,UAAU,GAAE,KAAK,SAAS;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,YAAY,MAAI;AAAC,WAAK,SAAS,YAAU,cAAc,OAAM,EAAC,OAAM,KAAK,OAAO,OAAO,WAAW,IAAG,CAAC,GAAE,KAAK,OAAO,SAAS,UAAU,YAAY,KAAK,SAAS,SAAS,GAAE,OAAO,IAAI,SAAS,aAAa,OAAO,IAAI,eAAe,UAAU,OAAO,GAAE,OAAO,IAAI,SAAS,UAAU,KAAK,OAAO,OAAO,IAAI,QAAQ,GAAE,OAAO,IAAI,SAAS,qCAAqC,KAAK,OAAO,OAAO,WAAW,GAAE,KAAK,SAAS,mBAAiB,IAAI,OAAO,IAAI,mBAAmB,KAAK,SAAS,WAAU,KAAK,OAAO,KAAK,GAAE,KAAK,SAAO,IAAI,OAAO,IAAI,UAAU,KAAK,SAAS,gBAAgB,GAAE,KAAK,OAAO,iBAAiB,OAAO,IAAI,sBAAsB,KAAK,oBAAoB,CAAAA,OAAG,KAAK,mBAAmBA,EAAC,GAAG,KAAE,GAAE,KAAK,OAAO,iBAAiB,OAAO,IAAI,aAAa,KAAK,UAAU,CAAAA,OAAG,KAAK,UAAUA,EAAC,GAAG,KAAE,GAAE,KAAK,WAAW;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,cAAc,MAAI;AAAC,YAAK,EAAC,WAAUA,GAAC,IAAE,KAAK,OAAO;AAAS,UAAG;AAAC,cAAM,IAAE,IAAI,OAAO,IAAI;AAAW,UAAE,WAAS,KAAK,QAAO,EAAE,oBAAkBA,GAAE,aAAY,EAAE,qBAAmBA,GAAE,cAAa,EAAE,uBAAqBA,GAAE,aAAY,EAAE,wBAAsBA,GAAE,cAAa,EAAE,yBAAuB,OAAG,EAAE,mBAAmB,CAAC,KAAK,OAAO,KAAK,GAAE,KAAK,OAAO,WAAW,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,aAAK,UAAUA,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,iBAAiB,CAACA,KAAE,UAAK;AAAC,UAAG,CAACA,GAAE,QAAO,cAAc,KAAK,cAAc,GAAE,KAAK,KAAK,SAAS,UAAU,gBAAgB,iBAAiB;AAAE,WAAK,iBAAe,YAAa,MAAI;AAAC,cAAMA,KAAE,WAAW,KAAK,IAAI,KAAK,QAAQ,iBAAiB,GAAE,CAAC,CAAC,GAAE,IAAE,GAAG,KAAK,IAAI,iBAAgB,KAAK,OAAO,MAAM,CAAC,MAAMA,EAAC;AAAG,aAAK,SAAS,UAAU,aAAa,mBAAkB,CAAC;AAAA,MAAC,GAAG,GAAG;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,sBAAsB,CAAAA,OAAG;AAAC,UAAG,CAAC,KAAK,QAAQ;AAAO,YAAM,IAAE,IAAI,OAAO,IAAI;AAAqB,QAAE,8CAA4C,MAAG,EAAE,mBAAiB,MAAG,KAAK,UAAQA,GAAE,cAAc,KAAK,QAAO,CAAC,GAAE,KAAK,YAAU,KAAK,QAAQ,aAAa,GAAE,KAAK,QAAQ,iBAAiB,OAAO,IAAI,aAAa,KAAK,UAAU,CAAAA,OAAG,KAAK,UAAUA,EAAC,CAAE,GAAE,OAAO,KAAK,OAAO,IAAI,QAAQ,IAAI,EAAE,QAAS,CAAAA,OAAG;AAAC,aAAK,QAAQ,iBAAiB,OAAO,IAAI,QAAQ,KAAKA,EAAC,GAAG,CAAAA,OAAG,KAAK,UAAUA,EAAC,CAAE;AAAA,MAAC,CAAE,GAAE,KAAK,QAAQ,QAAQ;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,gBAAgB,MAAI;AAAC,SAAG,MAAM,KAAK,SAAS,KAAG,KAAK,UAAU,QAAS,CAAAA,OAAG;AAAC,YAAG,MAAIA,MAAG,OAAKA,MAAGA,KAAE,KAAK,OAAO,UAAS;AAAC,gBAAM,IAAE,KAAK,OAAO,SAAS;AAAS,cAAG,GAAG,QAAQ,CAAC,GAAE;AAAC,kBAAM,IAAE,MAAI,KAAK,OAAO,WAASA,IAAE,IAAE,cAAc,QAAO,EAAC,OAAM,KAAK,OAAO,OAAO,WAAW,KAAI,CAAC;AAAE,cAAE,MAAM,OAAK,GAAG,EAAE,SAAS,CAAC,KAAI,EAAE,YAAY,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,aAAa,CAAAA,OAAG;AAAC,YAAK,EAAC,WAAU,EAAC,IAAE,KAAK,OAAO,UAAS,IAAEA,GAAE,MAAM,GAAE,IAAEA,GAAE,UAAU;AAAE,eAAQ,CAAAA,OAAG;AAAC,qBAAa,KAAK,KAAK,QAAO,KAAK,OAAO,OAAM,MAAMA,GAAE,QAAQ,MAAK,EAAE,EAAE,YAAY,CAAC,EAAE;AAAA,MAAC,GAAGA,GAAE,IAAI,GAAEA,GAAE,MAAK;AAAA,QAAC,KAAK,OAAO,IAAI,QAAQ,KAAK;AAAO,eAAK,QAAQ,QAAQ,GAAE,KAAK,cAAc,IAAE,GAAE,EAAE,SAAS,MAAI,EAAE,QAAM,EAAE,aAAY,EAAE,SAAO,EAAE;AAAc;AAAA,QAAM,KAAK,OAAO,IAAI,QAAQ,KAAK;AAAQ,eAAK,QAAQ,UAAU,KAAK,OAAO,MAAM;AAAE;AAAA,QAAM,KAAK,OAAO,IAAI,QAAQ,KAAK;AAAkB,eAAK,OAAO,QAAM,KAAK,QAAQ,IAAE,KAAK,OAAO,gBAAgB;AAAE;AAAA,QAAM,KAAK,OAAO,IAAI,QAAQ,KAAK;AAAwB,eAAK,aAAa;AAAE;AAAA,QAAM,KAAK,OAAO,IAAI,QAAQ,KAAK;AAAyB,eAAK,cAAc,GAAE,KAAK,cAAc;AAAE;AAAA,QAAM,KAAK,OAAO,IAAI,QAAQ,KAAK;AAAI,YAAE,WAAS,KAAK,OAAO,MAAM,KAAK,uBAAuB,EAAE,QAAQ,WAAW,CAAC,EAAE;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,aAAa,CAAAA,OAAG;AAAC,WAAK,OAAO,GAAE,KAAK,OAAO,MAAM,KAAK,aAAYA,EAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,aAAa,MAAI;AAAC,YAAK,EAAC,WAAUA,GAAC,IAAE,KAAK,OAAO;AAAS,UAAI;AAAE,WAAK,OAAO,GAAG,WAAW,MAAI;AAAC,aAAK,aAAa;AAAA,MAAC,CAAE,GAAE,KAAK,OAAO,GAAG,SAAS,MAAI;AAAC,aAAK,OAAO,gBAAgB;AAAA,MAAC,CAAE,GAAE,KAAK,OAAO,GAAG,cAAc,MAAI;AAAC,YAAE,KAAK,OAAO;AAAA,MAAW,CAAE,GAAE,KAAK,OAAO,GAAG,UAAU,MAAI;AAAC,cAAMA,KAAE,KAAK,OAAO;AAAY,WAAG,MAAM,KAAK,SAAS,KAAG,KAAK,UAAU,QAAS,CAAC,GAAE,MAAI;AAAC,cAAE,KAAG,IAAEA,OAAI,KAAK,QAAQ,eAAe,GAAE,KAAK,UAAU,OAAO,GAAE,CAAC;AAAA,QAAE,CAAE;AAAA,MAAC,CAAE,GAAE,OAAO,iBAAiB,UAAU,MAAI;AAAC,aAAK,WAAS,KAAK,QAAQ,OAAOA,GAAE,aAAYA,GAAE,cAAa,OAAO,IAAI,SAAS,MAAM;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,QAAQ,MAAI;AAAC,YAAK,EAAC,WAAUA,GAAC,IAAE,KAAK,OAAO;AAAS,WAAK,kBAAgB,KAAK,cAAc,GAAE,KAAK,eAAe,KAAM,MAAI;AAAC,aAAK,QAAQ,UAAU,KAAK,OAAO,MAAM,GAAE,KAAK,SAAS,iBAAiB,WAAW;AAAE,YAAG;AAAC,eAAK,gBAAc,KAAK,QAAQ,KAAKA,GAAE,aAAYA,GAAE,cAAa,OAAO,IAAI,SAAS,MAAM,GAAE,KAAK,QAAQ,MAAM,IAAG,KAAK,cAAY;AAAA,QAAE,SAAOA,IAAE;AAAC,eAAK,UAAUA,EAAC;AAAA,QAAC;AAAA,MAAC,CAAE,EAAE,MAAO,MAAI;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,iBAAiB,MAAI;AAAC,WAAK,SAAS,UAAU,MAAM,SAAO,IAAG,KAAK,UAAQ,OAAG,eAAe,KAAK,OAAO,MAAM,KAAK,CAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,gBAAgB,MAAI;AAAC,WAAK,SAAS,UAAU,MAAM,SAAO,GAAE,KAAK,UAAQ,MAAG,KAAK,OAAO,MAAM,MAAM;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,UAAU,MAAI;AAAC,WAAK,eAAa,KAAK,cAAc,GAAE,KAAK,QAAQ,OAAO,GAAE,KAAK,QAAQ;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,WAAW,MAAI;AAAC,WAAK,eAAe,KAAM,MAAI;AAAC,aAAK,WAAS,KAAK,QAAQ,QAAQ,GAAE,KAAK,iBAAe,IAAI,QAAS,CAAAA,OAAG;AAAC,eAAK,GAAG,UAASA,EAAC,GAAE,KAAK,OAAO,MAAM,IAAI,KAAK,OAAO;AAAA,QAAC,CAAE,GAAE,KAAK,cAAY,OAAG,KAAK,WAAW;AAAA,MAAC,CAAE,EAAE,MAAO,MAAI;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,WAAW,CAACA,OAAK,MAAI;AAAC,YAAM,IAAE,KAAK,OAAOA,EAAC;AAAE,SAAG,MAAM,CAAC,KAAG,EAAE,QAAS,CAAAA,OAAG;AAAC,WAAG,SAASA,EAAC,KAAGA,GAAE,MAAM,MAAK,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,MAAM,CAACA,IAAE,OAAK,GAAG,MAAM,KAAK,OAAOA,EAAC,CAAC,MAAI,KAAK,OAAOA,EAAC,IAAE,CAAC,IAAG,KAAK,OAAOA,EAAC,EAAE,KAAK,CAAC,GAAE,KAAM,GAAE,kBAAkB,MAAK,oBAAoB,CAACA,IAAE,MAAI;AAAC,WAAK,OAAO,MAAM,IAAI,8BAA8B,CAAC,EAAE,GAAE,KAAK,cAAY,WAAY,MAAI;AAAC,aAAK,OAAO,GAAE,KAAK,iBAAiB,oBAAoB;AAAA,MAAC,GAAGA,EAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,oBAAoB,CAAAA,OAAG;AAAC,SAAG,gBAAgB,KAAK,WAAW,MAAI,KAAK,OAAO,MAAM,IAAI,8BAA8BA,EAAC,EAAE,GAAE,aAAa,KAAK,WAAW,GAAE,KAAK,cAAY;AAAA,IAAK,CAAE,GAAE,KAAK,SAAO,GAAE,KAAK,SAAO,EAAE,OAAO,KAAI,KAAK,UAAQ,OAAG,KAAK,cAAY,OAAG,KAAK,WAAS,EAAC,WAAU,MAAK,kBAAiB,KAAI,GAAE,KAAK,UAAQ,MAAK,KAAK,SAAO,MAAK,KAAK,YAAU,MAAK,KAAK,SAAO,CAAC,GAAE,KAAK,cAAY,MAAK,KAAK,iBAAe,MAAK,KAAK,iBAAe,IAAI,QAAS,CAACA,IAAE,MAAI;AAAC,WAAK,GAAG,UAASA,EAAC,GAAE,KAAK,GAAG,SAAQ,CAAC;AAAA,IAAC,CAAE,GAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,UAAK,EAAC,QAAO,EAAC,IAAE;AAAK,WAAO,KAAK,OAAO,WAAS,KAAK,OAAO,WAAS,EAAE,YAAU,CAAC,GAAG,MAAM,EAAE,WAAW,KAAG,GAAG,IAAI,EAAE,MAAM;AAAA,EAAE;AAAA,EAAC,IAAI,SAAQ;AAAC,UAAK,EAAC,QAAO,EAAC,IAAE;AAAK,QAAG,GAAG,IAAI,EAAE,MAAM,EAAE,QAAO,EAAE;AAAO,WAAM,8CAA8C,eAAe,EAAC,gBAAe,4BAA2B,cAAa,4BAA2B,QAAO,OAAO,SAAS,UAAS,IAAG,KAAK,IAAI,GAAE,UAAS,KAAI,WAAU,KAAI,UAAS,EAAE,YAAW,CAAC,CAAC;AAAA,EAAE;AAAC;AAAC,SAAS,MAAM,IAAE,GAAE,IAAE,GAAE,IAAE,KAAI;AAAC,SAAO,KAAK,IAAI,KAAK,IAAI,GAAE,CAAC,GAAE,CAAC;AAAC;AAAC,IAAM,WAAS,OAAG;AAAC,QAAM,IAAE,CAAC;AAAE,SAAO,EAAE,MAAM,oBAAoB,EAAE,QAAS,CAAAA,OAAG;AAAC,UAAM,IAAE,CAAC;AAAE,IAAAA,GAAE,MAAM,YAAY,EAAE,QAAS,CAAAA,OAAG;AAAC,UAAG,GAAG,OAAO,EAAE,SAAS,GAAE;AAAC,YAAG,CAAC,GAAG,MAAMA,GAAE,KAAK,CAAC,KAAG,GAAG,MAAM,EAAE,IAAI,GAAE;AAAC,gBAAMC,KAAED,GAAE,KAAK,EAAE,MAAM,QAAQ;AAAE,WAAC,EAAE,IAAI,IAAEC,IAAEA,GAAE,CAAC,MAAI,CAAC,EAAE,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,MAAM,GAAG;AAAA,QAAE;AAAA,MAAC,OAAK;AAAC,cAAMA,KAAED,GAAE,MAAM,yGAAyG;AAAE,QAAAC,OAAI,EAAE,YAAU,KAAG,OAAOA,GAAE,CAAC,KAAG,CAAC,IAAE,KAAG,KAAG,OAAOA,GAAE,CAAC,CAAC,IAAE,OAAOA,GAAE,CAAC,CAAC,IAAE,OAAO,KAAKA,GAAE,CAAC,CAAC,EAAE,GAAE,EAAE,UAAQ,KAAG,OAAOA,GAAE,CAAC,KAAG,CAAC,IAAE,KAAG,KAAG,OAAOA,GAAE,CAAC,CAAC,IAAE,OAAOA,GAAE,CAAC,CAAC,IAAE,OAAO,KAAKA,GAAE,CAAC,CAAC,EAAE;AAAA,MAAE;AAAA,IAAC,CAAE,GAAE,EAAE,QAAM,EAAE,KAAK,CAAC;AAAA,EAAC,CAAE,GAAE;AAAC;AAA7lB,IAA+lB,WAAS,CAAC,GAAE,MAAI;AAAC,QAAM,IAAE,CAAC;AAAE,SAAO,IAAE,EAAE,QAAM,EAAE,UAAQ,EAAE,QAAM,EAAE,OAAM,EAAE,SAAO,IAAE,IAAE,EAAE,UAAQ,EAAE,SAAO,EAAE,QAAO,EAAE,QAAM,IAAE,EAAE,SAAQ;AAAC;AAAE,IAAM,oBAAN,MAAuB;AAAA,EAAC,YAAY,GAAE;AAAC,sBAAkB,MAAK,QAAQ,MAAI;AAAC,WAAK,OAAO,SAAS,QAAQ,gBAAc,KAAK,OAAO,SAAS,QAAQ,YAAY,SAAO,KAAK,UAAS,KAAK,WAAS,KAAK,cAAc,EAAE,KAAM,MAAI;AAAC,aAAK,YAAU,KAAK,OAAO,GAAE,KAAK,6BAA6B,GAAE,KAAK,UAAU,GAAE,KAAK,SAAO;AAAA,MAAG,CAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,iBAAiB,MAAI,IAAI,QAAS,CAAAD,OAAG;AAAC,YAAK,EAAC,KAAI,EAAC,IAAE,KAAK,OAAO,OAAO;AAAkB,UAAG,GAAG,MAAM,CAAC,EAAE,OAAM,IAAI,MAAM,gDAAgD;AAAE,YAAM,IAAE,MAAI;AAAC,aAAK,WAAW,KAAM,CAACA,IAAEC,OAAID,GAAE,SAAOC,GAAE,MAAO,GAAE,KAAK,OAAO,MAAM,IAAI,sBAAqB,KAAK,UAAU,GAAED,GAAE;AAAA,MAAC;AAAE,UAAG,GAAG,SAAS,CAAC,EAAE,GAAG,CAAAA,OAAG;AAAC,aAAK,aAAWA,IAAE,EAAE;AAAA,MAAC,CAAE;AAAA,WAAM;AAAC,cAAMA,MAAG,GAAG,OAAO,CAAC,IAAE,CAAC,CAAC,IAAE,GAAG,IAAK,CAAAA,OAAG,KAAK,aAAaA,EAAC,CAAE;AAAE,gBAAQ,IAAIA,EAAC,EAAE,KAAK,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE,CAAE,GAAE,kBAAkB,MAAK,gBAAgB,CAAAA,OAAG,IAAI,QAAS,OAAG;AAAC,YAAMA,EAAC,EAAE,KAAM,OAAG;AAAC,cAAM,IAAE,EAAC,QAAO,SAAS,CAAC,GAAE,QAAO,MAAK,WAAU,GAAE;AAAE,UAAE,OAAO,CAAC,EAAE,KAAK,WAAW,GAAG,KAAG,EAAE,OAAO,CAAC,EAAE,KAAK,WAAW,SAAS,KAAG,EAAE,OAAO,CAAC,EAAE,KAAK,WAAW,UAAU,MAAI,EAAE,YAAUA,GAAE,UAAU,GAAEA,GAAE,YAAY,GAAG,IAAE,CAAC;AAAG,cAAM,IAAE,IAAI;AAAM,UAAE,SAAO,MAAI;AAAC,YAAE,SAAO,EAAE,eAAc,EAAE,QAAM,EAAE,cAAa,KAAK,WAAW,KAAK,CAAC,GAAE,EAAE;AAAA,QAAC,GAAE,EAAE,MAAI,EAAE,YAAU,EAAE,OAAO,CAAC,EAAE;AAAA,MAAI,CAAE;AAAA,IAAC,CAAE,CAAE,GAAE,kBAAkB,MAAK,aAAa,CAAAA,OAAG;AAAC,UAAG,KAAK,UAAQ,GAAG,MAAMA,EAAC,KAAG,CAAC,aAAY,WAAW,EAAE,SAASA,GAAE,IAAI,KAAG,KAAK,OAAO,MAAM,UAAS;AAAC,YAAG,gBAAcA,GAAE,KAAK,MAAK,WAAS,KAAK,OAAO,MAAM,YAAU,KAAK,OAAO,SAAS,OAAO,KAAK,QAAM;AAAA,aAAS;AAAC,cAAI,GAAE;AAAE,gBAAM,IAAE,KAAK,OAAO,SAAS,SAAS,sBAAsB,GAAE,IAAE,MAAI,EAAE,SAAOA,GAAE,QAAM,EAAE;AAAM,eAAK,WAAS,KAAK,OAAO,MAAM,YAAU,IAAE,MAAK,KAAK,WAAS,MAAI,KAAK,WAAS,IAAG,KAAK,WAAS,KAAK,OAAO,MAAM,WAAS,MAAI,KAAK,WAAS,KAAK,OAAO,MAAM,WAAS,IAAG,KAAK,YAAUA,GAAE,OAAM,KAAK,SAAS,MAAM,KAAK,YAAU,WAAW,KAAK,QAAQ;AAAE,gBAAM,IAAE,UAAQ,IAAE,KAAK,OAAO,OAAO,YAAU,WAAS,KAAG,UAAQ,IAAE,EAAE,WAAS,WAAS,IAAE,SAAO,EAAE,KAAM,CAAC,EAAC,MAAKA,GAAC,MAAIA,OAAI,KAAK,MAAM,KAAK,QAAQ,CAAE;AAAE,eAAG,KAAK,SAAS,MAAM,KAAK,mBAAmB,cAAa,GAAG,EAAE,KAAK,MAAM;AAAA,QAAC;AAAC,aAAK,uBAAuB;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,WAAW,MAAI;AAAC,WAAK,qBAAqB,OAAG,IAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,kBAAkB,CAAAA,OAAG;AAAC,OAAC,GAAG,gBAAgBA,GAAE,MAAM,KAAG,UAAKA,GAAE,UAAQ,MAAIA,GAAE,YAAU,KAAK,YAAU,MAAG,KAAK,OAAO,MAAM,aAAW,KAAK,yBAAyB,IAAE,GAAE,KAAK,qBAAqB,OAAG,IAAE,GAAE,KAAK,uBAAuB;AAAA,IAAG,CAAE,GAAE,kBAAkB,MAAK,gBAAgB,MAAI;AAAC,WAAK,YAAU,OAAG,KAAK,KAAK,KAAK,QAAQ,MAAI,KAAK,KAAK,KAAK,OAAO,MAAM,WAAW,IAAE,KAAK,yBAAyB,KAAE,IAAE,KAAK,KAAK,KAAK,QAAO,KAAK,OAAO,OAAM,cAAc,MAAI;AAAC,aAAK,aAAW,KAAK,yBAAyB,KAAE;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,aAAa,MAAI;AAAC,WAAK,OAAO,GAAG,QAAQ,MAAI;AAAC,aAAK,qBAAqB,OAAG,IAAE;AAAA,MAAC,CAAE,GAAE,KAAK,OAAO,GAAG,UAAU,MAAI;AAAC,aAAK,qBAAqB,KAAE;AAAA,MAAC,CAAE,GAAE,KAAK,OAAO,GAAG,cAAc,MAAI;AAAC,aAAK,WAAS,KAAK,OAAO,MAAM;AAAA,MAAW,CAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,UAAU,MAAI;AAAC,WAAK,SAAS,MAAM,YAAU,cAAc,OAAM,EAAC,OAAM,KAAK,OAAO,OAAO,WAAW,kBAAkB,eAAc,CAAC,GAAE,KAAK,SAAS,MAAM,iBAAe,cAAc,OAAM,EAAC,OAAM,KAAK,OAAO,OAAO,WAAW,kBAAkB,eAAc,CAAC,GAAE,KAAK,SAAS,MAAM,UAAU,YAAY,KAAK,SAAS,MAAM,cAAc;AAAE,YAAMA,KAAE,cAAc,OAAM,EAAC,OAAM,KAAK,OAAO,OAAO,WAAW,kBAAkB,cAAa,CAAC;AAAE,WAAK,SAAS,MAAM,OAAK,cAAc,QAAO,CAAC,GAAE,OAAO,GAAEA,GAAE,YAAY,KAAK,SAAS,MAAM,IAAI,GAAE,KAAK,SAAS,MAAM,eAAe,YAAYA,EAAC,GAAE,GAAG,QAAQ,KAAK,OAAO,SAAS,QAAQ,KAAG,KAAK,OAAO,SAAS,SAAS,YAAY,KAAK,SAAS,MAAM,SAAS,GAAE,KAAK,SAAS,UAAU,YAAU,cAAc,OAAM,EAAC,OAAM,KAAK,OAAO,OAAO,WAAW,kBAAkB,mBAAkB,CAAC,GAAE,KAAK,OAAO,SAAS,QAAQ,YAAY,KAAK,SAAS,UAAU,SAAS;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,WAAW,MAAI;AAAC,WAAK,SAAS,MAAM,aAAW,KAAK,SAAS,MAAM,UAAU,OAAO,GAAE,KAAK,SAAS,UAAU,aAAW,KAAK,SAAS,UAAU,UAAU,OAAO;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,0BAA0B,MAAI;AAAC,WAAK,YAAU,KAAK,0BAA0B,IAAE,KAAK,4BAA4B;AAAE,YAAMA,KAAE,KAAK,WAAW,CAAC,EAAE,OAAO,UAAW,CAAAA,OAAG,KAAK,YAAUA,GAAE,aAAW,KAAK,YAAUA,GAAE,OAAQ,GAAE,IAAEA,MAAG;AAAE,UAAI,IAAE;AAAE,WAAK,aAAW,KAAK,qBAAqB,CAAC,GAAE,MAAI,KAAK,WAAW,QAAS,CAACC,IAAE,MAAI;AAAC,aAAK,aAAa,SAASA,GAAE,OAAOD,EAAC,EAAE,IAAI,MAAI,IAAE;AAAA,MAAE,CAAE,GAAEA,OAAI,KAAK,iBAAe,KAAK,eAAaA,IAAE,KAAK,UAAU,CAAC;AAAA,IAAG,CAAE,GAAE,kBAAkB,MAAK,aAAa,CAACA,KAAE,MAAI;AAAC,YAAM,IAAE,KAAK,cAAa,IAAE,KAAK,WAAWA,EAAC,GAAE,EAAC,WAAU,EAAC,IAAE,GAAE,IAAE,EAAE,OAAO,CAAC,GAAE,IAAE,EAAE,OAAO,CAAC,EAAE,MAAK,IAAE,IAAE;AAAE,UAAG,KAAK,uBAAqB,KAAK,oBAAoB,QAAQ,aAAW,EAAE,MAAK,UAAU,KAAK,qBAAoB,GAAEA,IAAE,GAAE,GAAE,KAAE,GAAE,KAAK,oBAAoB,QAAQ,QAAM,GAAE,KAAK,gBAAgB,KAAK,mBAAmB;AAAA,WAAM;AAAC,aAAK,gBAAc,KAAK,iBAAe,KAAK,aAAa,SAAO;AAAM,cAAME,KAAE,IAAI;AAAM,QAAAA,GAAE,MAAI,GAAEA,GAAE,QAAQ,QAAM,GAAEA,GAAE,QAAQ,WAAS,GAAE,KAAK,uBAAqB,GAAE,KAAK,OAAO,MAAM,IAAI,kBAAkB,CAAC,EAAE,GAAEA,GAAE,SAAO,MAAI,KAAK,UAAUA,IAAE,GAAEF,IAAE,GAAE,GAAE,IAAE,GAAE,KAAK,eAAaE,IAAE,KAAK,gBAAgBA,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,aAAa,CAACF,IAAE,GAAE,GAAE,GAAE,GAAE,IAAE,SAAK;AAAC,WAAK,OAAO,MAAM,IAAI,kBAAkB,CAAC,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,GAAE,KAAK,sBAAsBA,IAAE,CAAC,GAAE,MAAI,KAAK,sBAAsB,YAAYA,EAAC,GAAE,KAAK,sBAAoBA,IAAE,KAAK,aAAa,SAAS,CAAC,KAAG,KAAK,aAAa,KAAK,CAAC,IAAG,KAAK,cAAc,GAAE,IAAE,EAAE,KAAK,KAAK,cAAc,GAAE,KAAE,CAAC,EAAE,KAAK,KAAK,iBAAiB,GAAEA,IAAE,GAAE,CAAC,CAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,mBAAmB,CAAAA,OAAG;AAAC,YAAM,KAAK,KAAK,sBAAsB,QAAQ,EAAE,QAAS,OAAG;AAAC,YAAG,UAAQ,EAAE,QAAQ,YAAY,EAAE;AAAO,cAAM,IAAE,KAAK,eAAa,MAAI;AAAI,YAAG,EAAE,QAAQ,UAAQA,GAAE,QAAQ,SAAO,CAAC,EAAE,QAAQ,UAAS;AAAC,YAAE,QAAQ,WAAS;AAAG,gBAAK,EAAC,uBAAsBA,GAAC,IAAE;AAAK,qBAAY,MAAI;AAAC,YAAAA,GAAE,YAAY,CAAC,GAAE,KAAK,OAAO,MAAM,IAAI,mBAAmB,EAAE,QAAQ,QAAQ,EAAE;AAAA,UAAC,GAAG,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,iBAAiB,CAACA,IAAE,IAAE,SAAK,IAAI,QAAS,OAAG;AAAC,iBAAY,MAAI;AAAC,cAAM,IAAE,KAAK,WAAW,CAAC,EAAE,OAAOA,EAAC,EAAE;AAAK,YAAG,KAAK,yBAAuB,GAAE;AAAC,cAAI;AAAE,cAAE,IAAE,KAAK,WAAW,CAAC,EAAE,OAAO,MAAMA,EAAC,IAAE,KAAK,WAAW,CAAC,EAAE,OAAO,MAAM,GAAEA,EAAC,EAAE,QAAQ;AAAE,cAAI,IAAE;AAAG,YAAE,QAAS,CAAAA,OAAG;AAAC,kBAAMC,KAAED,GAAE;AAAK,gBAAGC,OAAI,KAAG,CAAC,KAAK,aAAa,SAASA,EAAC,GAAE;AAAC,kBAAE,MAAG,KAAK,OAAO,MAAM,IAAI,8BAA8BA,EAAC,EAAE;AAAE,oBAAK,EAAC,WAAUD,GAAC,IAAE,KAAK,WAAW,CAAC,GAAEG,KAAEH,KAAEC,IAAEG,KAAE,IAAI;AAAM,cAAAA,GAAE,MAAID,IAAEC,GAAE,SAAO,MAAI;AAAC,qBAAK,OAAO,MAAM,IAAI,6BAA6BH,EAAC,EAAE,GAAE,KAAK,aAAa,SAASA,EAAC,KAAG,KAAK,aAAa,KAAKA,EAAC,GAAE,EAAE;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC,CAAE,GAAE,KAAG,EAAE;AAAA,QAAC;AAAA,MAAC,GAAG,GAAG;AAAA,IAAC,CAAE,CAAE,GAAE,kBAAkB,MAAK,oBAAoB,CAACD,IAAE,GAAE,GAAE,MAAI;AAAC,UAAGA,KAAE,KAAK,WAAW,SAAO,GAAE;AAAC,YAAI,IAAE,EAAE;AAAc,aAAK,iBAAe,IAAE,EAAE,IAAG,IAAE,KAAK,wBAAsB,WAAY,MAAI;AAAC,eAAK,yBAAuB,MAAI,KAAK,OAAO,MAAM,IAAI,qCAAqC,CAAC,EAAE,GAAE,KAAK,UAAUA,KAAE,CAAC;AAAA,QAAE,GAAG,GAAG;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,wBAAwB,CAACA,KAAE,OAAG,IAAE,UAAK;AAAC,YAAM,IAAE,KAAK,OAAO,OAAO,WAAW,kBAAkB;AAAoB,WAAK,SAAS,MAAM,UAAU,UAAU,OAAO,GAAEA,EAAC,GAAE,CAACA,MAAG,MAAI,KAAK,eAAa,MAAK,KAAK,uBAAqB;AAAA,IAAK,CAAE,GAAE,kBAAkB,MAAK,4BAA4B,CAACA,KAAE,UAAK;AAAC,YAAM,IAAE,KAAK,OAAO,OAAO,WAAW,kBAAkB;AAAwB,WAAK,SAAS,UAAU,UAAU,UAAU,OAAO,GAAEA,EAAC,GAAEA,OAAI,KAAK,eAAa,MAAK,KAAK,uBAAqB;AAAA,IAAK,CAAE,GAAE,kBAAkB,MAAK,gCAAgC,MAAI;AAAC,OAAC,KAAK,SAAS,MAAM,eAAe,eAAa,MAAI,KAAK,SAAS,MAAM,eAAe,cAAY,QAAM,KAAK,qBAAmB;AAAA,IAAG,CAAE,GAAE,kBAAkB,MAAK,+BAA+B,MAAI;AAAC,YAAK,EAAC,gBAAeA,GAAC,IAAE,KAAK,SAAS;AAAM,UAAG,KAAK,oBAAmB;AAAC,YAAGA,GAAE,eAAa,MAAIA,GAAE,cAAY,IAAG;AAAC,gBAAM,IAAE,KAAK,MAAMA,GAAE,eAAa,KAAK,gBAAgB;AAAE,UAAAA,GAAE,MAAM,QAAM,GAAG,CAAC;AAAA,QAAI,WAASA,GAAE,eAAa,MAAIA,GAAE,cAAY,IAAG;AAAC,gBAAM,IAAE,KAAK,MAAMA,GAAE,cAAY,KAAK,gBAAgB;AAAE,UAAAA,GAAE,MAAM,SAAO,GAAG,CAAC;AAAA,QAAI;AAAA,MAAC,OAAK;AAAC,cAAM,IAAE,KAAK,MAAM,KAAK,uBAAqB,KAAK,gBAAgB;AAAE,QAAAA,GAAE,MAAM,SAAO,GAAG,KAAK,oBAAoB,MAAKA,GAAE,MAAM,QAAM,GAAG,CAAC;AAAA,MAAI;AAAC,WAAK,qBAAqB;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,wBAAwB,MAAI;AAAC,YAAMA,KAAE,KAAK,OAAO,SAAS,SAAS,sBAAsB,GAAE,IAAE,KAAK,OAAO,SAAS,UAAU,sBAAsB,GAAE,EAAC,WAAU,EAAC,IAAE,KAAK,SAAS,OAAM,IAAE,EAAE,OAAKA,GAAE,OAAK,IAAG,IAAE,EAAE,QAAMA,GAAE,OAAK,EAAE,cAAY,IAAG,IAAE,KAAK,YAAUA,GAAE,OAAK,EAAE,cAAY,GAAE,IAAE,MAAM,GAAE,GAAE,CAAC;AAAE,QAAE,MAAM,OAAK,GAAG,CAAC,MAAK,EAAE,MAAM,YAAY,0BAAyB,IAAE,IAAE,IAAI;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,6BAA6B,MAAI;AAAC,YAAK,EAAC,OAAMA,IAAE,QAAO,EAAC,IAAE,SAAS,KAAK,kBAAiB,EAAC,OAAM,KAAK,OAAO,MAAM,aAAY,QAAO,KAAK,OAAO,MAAM,aAAY,CAAC;AAAE,WAAK,SAAS,UAAU,UAAU,MAAM,QAAM,GAAGA,EAAC,MAAK,KAAK,SAAS,UAAU,UAAU,MAAM,SAAO,GAAG,CAAC;AAAA,IAAI,CAAE,GAAE,kBAAkB,MAAK,yBAAyB,CAACA,IAAE,MAAI;AAAC,UAAG,CAAC,KAAK,aAAa;AAAO,YAAM,IAAE,KAAK,uBAAqB,EAAE;AAAE,MAAAA,GAAE,MAAM,SAAOA,GAAE,gBAAc,IAAE,MAAKA,GAAE,MAAM,QAAMA,GAAE,eAAa,IAAE,MAAKA,GAAE,MAAM,OAAK,IAAI,EAAE,IAAE,CAAC,MAAKA,GAAE,MAAM,MAAI,IAAI,EAAE,IAAE,CAAC;AAAA,IAAI,CAAE,GAAE,KAAK,SAAO,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,SAAO,OAAG,KAAK,oBAAkB,KAAK,IAAI,GAAE,KAAK,YAAU,OAAG,KAAK,eAAa,CAAC,GAAE,KAAK,WAAS,EAAC,OAAM,CAAC,GAAE,WAAU,CAAC,EAAC,GAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,OAAO,WAAS,KAAK,OAAO,WAAS,KAAK,OAAO,OAAO,kBAAkB;AAAA,EAAO;AAAA,EAAC,IAAI,wBAAuB;AAAC,WAAO,KAAK,YAAU,KAAK,SAAS,UAAU,YAAU,KAAK,SAAS,MAAM;AAAA,EAAc;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,OAAO,KAAK,KAAK,WAAW,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,SAAS,GAAG;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,eAAa,KAAK,WAAW,CAAC,EAAE,OAAO,CAAC,EAAE,IAAE,KAAK,WAAW,CAAC,EAAE,OAAO,CAAC,EAAE,IAAE,KAAK,WAAW,CAAC,EAAE,QAAM,KAAK,WAAW,CAAC,EAAE;AAAA,EAAM;AAAA,EAAC,IAAI,uBAAsB;AAAC,QAAG,KAAK,WAAU;AAAC,YAAK,EAAC,QAAO,EAAC,IAAE,SAAS,KAAK,kBAAiB,EAAC,OAAM,KAAK,OAAO,MAAM,aAAY,QAAO,KAAK,OAAO,MAAM,aAAY,CAAC;AAAE,aAAO;AAAA,IAAC;AAAC,WAAO,KAAK,qBAAmB,KAAK,SAAS,MAAM,eAAe,eAAa,KAAK,MAAM,KAAK,OAAO,MAAM,cAAY,KAAK,mBAAiB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,sBAAqB;AAAC,WAAO,KAAK,YAAU,KAAK,+BAA6B,KAAK;AAAA,EAA4B;AAAA,EAAC,IAAI,oBAAoB,GAAE;AAAC,SAAK,YAAU,KAAK,+BAA6B,IAAE,KAAK,+BAA6B;AAAA,EAAC;AAAC;AAAC,IAAM,SAAO,EAAC,eAAe,GAAE,GAAE;AAAC,KAAG,OAAO,CAAC,IAAE,cAAc,GAAE,KAAK,OAAM,EAAC,KAAI,EAAC,CAAC,IAAE,GAAG,MAAM,CAAC,KAAG,EAAE,QAAS,CAAAC,OAAG;AAAC,kBAAc,GAAE,KAAK,OAAMA,EAAC;AAAA,EAAC,CAAE;AAAC,GAAE,OAAO,GAAE;AAAC,UAAQ,GAAE,gBAAgB,KAAG,MAAM,eAAe,KAAK,IAAI,GAAE,KAAK,QAAQ,KAAK,MAAM,MAAI;AAAC,SAAK,QAAQ,UAAQ,CAAC,GAAE,cAAc,KAAK,KAAK,GAAE,KAAK,QAAM,MAAK,GAAG,QAAQ,KAAK,SAAS,SAAS,KAAG,KAAK,SAAS,UAAU,gBAAgB,OAAO;AAAE,UAAK,EAAC,SAAQ,GAAE,MAAK,EAAC,IAAE,GAAE,CAAC,EAAC,UAAS,IAAE,UAAU,OAAM,KAAI,EAAC,CAAC,IAAE,GAAE,IAAE,YAAU,IAAE,IAAE,OAAM,IAAE,YAAU,IAAE,CAAC,IAAE,EAAC,KAAI,EAAC;AAAE,WAAO,OAAO,MAAK,EAAC,UAAS,GAAE,MAAK,GAAE,WAAU,QAAQ,MAAM,GAAE,GAAE,KAAK,OAAO,WAAW,GAAE,OAAM,cAAc,GAAE,CAAC,EAAC,CAAC,GAAE,KAAK,SAAS,UAAU,YAAY,KAAK,KAAK,GAAE,GAAG,QAAQ,EAAE,QAAQ,MAAI,KAAK,OAAO,WAAS,EAAE,WAAU,KAAK,YAAU,KAAK,OAAO,eAAa,KAAK,MAAM,aAAa,eAAc,EAAE,GAAE,KAAK,OAAO,YAAU,KAAK,MAAM,aAAa,YAAW,EAAE,GAAE,GAAG,MAAM,EAAE,MAAM,MAAI,KAAK,SAAO,EAAE,SAAQ,KAAK,OAAO,KAAK,UAAQ,KAAK,MAAM,aAAa,QAAO,EAAE,GAAE,KAAK,OAAO,SAAO,KAAK,MAAM,aAAa,SAAQ,EAAE,GAAE,KAAK,OAAO,eAAa,KAAK,MAAM,aAAa,eAAc,EAAE,IAAG,GAAG,aAAa,KAAK,IAAI,GAAE,KAAK,WAAS,OAAO,eAAe,KAAK,MAAK,UAAS,CAAC,GAAE,KAAK,OAAO,QAAM,EAAE,OAAM,MAAM,MAAM,KAAK,IAAI,GAAE,KAAK,WAAS,OAAO,KAAK,CAAC,EAAE,SAAS,QAAQ,KAAG,OAAO,eAAe,KAAK,MAAK,SAAQ,EAAE,MAAM,IAAG,KAAK,WAAS,KAAK,WAAS,CAAC,KAAK,UAAU,OAAK,GAAG,MAAM,KAAK,IAAI,GAAE,KAAK,WAAS,KAAK,MAAM,KAAK,GAAE,GAAG,MAAM,EAAE,iBAAiB,MAAI,OAAO,OAAO,KAAK,OAAO,mBAAkB,EAAE,iBAAiB,GAAE,KAAK,qBAAmB,KAAK,kBAAkB,WAAS,KAAK,kBAAkB,QAAQ,GAAE,KAAK,oBAAkB,OAAM,KAAK,OAAO,kBAAkB,YAAU,KAAK,oBAAkB,IAAI,kBAAkB,IAAI,KAAI,KAAK,WAAW,OAAO;AAAA,EAAC,GAAG,IAAE,KAAG,KAAK,MAAM,KAAK,uBAAuB;AAAC,EAAC;AAAE,IAAM,OAAN,MAAM,MAAI;AAAA,EAAC,YAAY,GAAE,GAAE;AAAC,QAAG,kBAAkB,MAAK,QAAQ,MAAI,GAAG,SAAS,KAAK,MAAM,IAAI,KAAG,KAAK,OAAK,KAAK,IAAI,WAAS,KAAK,IAAI,eAAe,KAAM,MAAI,KAAK,IAAI,KAAK,CAAE,EAAE,MAAO,MAAI,eAAe,KAAK,MAAM,KAAK,CAAC,CAAE,GAAE,KAAK,MAAM,KAAK,KAAG,IAAK,GAAE,kBAAkB,MAAK,SAAS,MAAI,KAAK,WAAS,GAAG,SAAS,KAAK,MAAM,KAAK,IAAE,KAAK,MAAM,MAAM,IAAE,IAAK,GAAE,kBAAkB,MAAK,cAAc,CAAAD,QAAI,GAAG,QAAQA,EAAC,IAAEA,KAAE,CAAC,KAAK,WAAS,KAAK,KAAK,IAAE,KAAK,MAAM,CAAE,GAAE,kBAAkB,MAAK,QAAQ,MAAI;AAAC,WAAK,WAAS,KAAK,MAAM,GAAE,KAAK,QAAQ,KAAG,GAAG,SAAS,KAAK,MAAM,IAAI,KAAG,KAAK,MAAM,KAAK;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,WAAW,MAAI;AAAC,WAAK,cAAY;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,UAAU,CAAAA,OAAG;AAAC,WAAK,eAAa,GAAG,OAAOA,EAAC,IAAEA,KAAE,KAAK,OAAO;AAAA,IAAQ,CAAE,GAAE,kBAAkB,MAAK,WAAW,CAAAA,OAAG;AAAC,WAAK,eAAa,GAAG,OAAOA,EAAC,IAAEA,KAAE,KAAK,OAAO;AAAA,IAAQ,CAAE,GAAE,kBAAkB,MAAK,kBAAkB,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,MAAM,QAAM,IAAE,KAAK;AAAO,WAAK,SAAOA,MAAG,GAAG,OAAOD,EAAC,IAAEA,KAAE;AAAA,IAAE,CAAE,GAAE,kBAAkB,MAAK,kBAAkB,CAAAA,OAAG;AAAC,WAAK,eAAe,CAACA,EAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,WAAW,MAAI;AAAC,cAAQ,WAAS,KAAK,MAAM,+BAA+B;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,kBAAkB,CAAAA,OAAG;AAAC,UAAG,KAAK,UAAU,MAAI,CAAC,KAAK,SAAQ;AAAC,cAAMC,KAAE,SAAS,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,YAAY,GAAEC,KAAE,WAASF,KAAE,SAAO,CAACA,IAAEG,KAAE,YAAY,KAAK,SAAS,WAAU,KAAK,OAAO,WAAW,cAAaD,EAAC;AAAE,YAAGC,MAAG,GAAG,MAAM,KAAK,OAAO,QAAQ,KAAG,KAAK,OAAO,SAAS,SAAS,UAAU,KAAG,CAAC,GAAG,MAAM,KAAK,OAAO,QAAQ,KAAG,SAAS,WAAW,KAAK,MAAK,KAAE,GAAEA,OAAIF,IAAE;AAAC,gBAAMD,KAAEG,KAAE,mBAAiB;AAAgB,uBAAa,KAAK,MAAK,KAAK,OAAMH,EAAC;AAAA,QAAC;AAAC,eAAM,CAACG;AAAA,MAAC;AAAC,aAAM;AAAA,IAAE,CAAE,GAAE,kBAAkB,MAAK,MAAM,CAACH,IAAEC,OAAI;AAAC,SAAG,KAAK,MAAK,KAAK,SAAS,WAAUD,IAAEC,EAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,QAAQ,CAACD,IAAEC,OAAI;AAAC,WAAK,KAAK,MAAK,KAAK,SAAS,WAAUD,IAAEC,EAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,OAAO,CAACD,IAAEC,OAAI;AAAC,UAAI,KAAK,SAAS,WAAUD,IAAEC,EAAC;AAAA,IAAC,CAAE,GAAE,kBAAkB,MAAK,WAAW,CAACD,IAAEC,KAAE,UAAK;AAAC,UAAG,CAAC,KAAK,MAAM;AAAO,YAAMC,KAAE,MAAI;AAAC,iBAAS,KAAK,MAAM,WAAS,IAAG,KAAK,QAAM,MAAKD,MAAG,OAAO,KAAK,KAAK,QAAQ,EAAE,WAAS,cAAc,KAAK,SAAS,QAAQ,IAAI,GAAE,cAAc,KAAK,SAAS,QAAQ,GAAE,cAAc,KAAK,SAAS,QAAQ,GAAE,cAAc,KAAK,SAAS,OAAO,GAAE,KAAK,SAAS,QAAQ,OAAK,MAAK,KAAK,SAAS,WAAS,MAAK,KAAK,SAAS,WAAS,MAAK,KAAK,SAAS,UAAQ,OAAM,GAAG,SAASD,EAAC,KAAGA,GAAE,MAAI,gBAAgB,KAAK,IAAI,GAAE,MAAM,eAAe,KAAK,IAAI,GAAE,eAAe,KAAK,SAAS,UAAS,KAAK,SAAS,SAAS,GAAE,aAAa,KAAK,MAAK,KAAK,SAAS,UAAS,aAAY,IAAE,GAAE,GAAG,SAASA,EAAC,KAAGA,GAAE,KAAK,KAAK,SAAS,QAAQ,GAAE,KAAK,QAAM,OAAG,WAAY,MAAI;AAAC,eAAK,WAAS,MAAK,KAAK,QAAM;AAAA,QAAI,GAAG,GAAG;AAAA,MAAE;AAAE,WAAK,KAAK,GAAE,aAAa,KAAK,OAAO,OAAO,GAAE,aAAa,KAAK,OAAO,QAAQ,GAAE,aAAa,KAAK,OAAO,OAAO,GAAE,KAAK,WAAS,GAAG,qBAAqB,KAAK,MAAK,IAAE,GAAEE,GAAE,KAAG,KAAK,aAAW,cAAc,KAAK,OAAO,SAAS,GAAE,cAAc,KAAK,OAAO,OAAO,GAAE,SAAO,KAAK,SAAO,GAAG,SAAS,KAAK,MAAM,OAAO,KAAG,KAAK,MAAM,QAAQ,GAAEA,GAAE,KAAG,KAAK,YAAU,SAAO,KAAK,SAAO,KAAK,MAAM,OAAO,EAAE,KAAKA,EAAC,GAAE,WAAWA,IAAE,GAAG;AAAA,IAAE,CAAE,GAAE,kBAAkB,MAAK,YAAY,CAAAF,OAAG,QAAQ,KAAK,KAAK,MAAKA,EAAC,CAAE,GAAE,KAAK,SAAO,CAAC,GAAE,KAAK,QAAM,OAAG,KAAK,UAAQ,OAAG,KAAK,SAAO,OAAG,KAAK,QAAM,QAAQ,OAAM,KAAK,QAAM,GAAE,GAAG,OAAO,KAAK,KAAK,MAAI,KAAK,QAAM,SAAS,iBAAiB,KAAK,KAAK,KAAI,OAAO,UAAQ,KAAK,iBAAiB,UAAQ,GAAG,SAAS,KAAK,KAAK,KAAG,GAAG,MAAM,KAAK,KAAK,OAAK,KAAK,QAAM,KAAK,MAAM,CAAC,IAAG,KAAK,SAAO,OAAO,CAAC,GAAE,UAAS,MAAK,UAAS,KAAG,CAAC,IAAG,MAAI;AAAC,UAAG;AAAC,eAAO,KAAK,MAAM,KAAK,MAAM,aAAa,kBAAkB,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,eAAM,CAAC;AAAA,MAAC;AAAA,IAAC,GAAG,CAAC,GAAE,KAAK,WAAS,EAAC,WAAU,MAAK,YAAW,MAAK,UAAS,MAAK,SAAQ,CAAC,GAAE,SAAQ,CAAC,GAAE,UAAS,CAAC,GAAE,QAAO,CAAC,GAAE,UAAS,EAAC,OAAM,MAAK,MAAK,MAAK,QAAO,CAAC,GAAE,SAAQ,CAAC,EAAC,EAAC,GAAE,KAAK,WAAS,EAAC,QAAO,MAAK,cAAa,IAAG,MAAK,oBAAI,UAAO,GAAE,KAAK,aAAW,EAAC,QAAO,MAAE,GAAE,KAAK,UAAQ,EAAC,OAAM,CAAC,GAAE,SAAQ,CAAC,EAAC,GAAE,KAAK,QAAM,IAAI,QAAQ,KAAK,OAAO,KAAK,GAAE,KAAK,MAAM,IAAI,UAAS,KAAK,MAAM,GAAE,KAAK,MAAM,IAAI,WAAU,OAAO,GAAE,GAAG,gBAAgB,KAAK,KAAK,KAAG,CAAC,GAAG,QAAQ,KAAK,KAAK,EAAE,QAAO,KAAK,KAAK,MAAM,MAAM,0CAA0C;AAAE,QAAG,KAAK,MAAM,KAAK,QAAO,KAAK,KAAK,MAAM,KAAK,sBAAsB;AAAE,QAAG,CAAC,KAAK,OAAO,QAAQ,QAAO,KAAK,KAAK,MAAM,MAAM,kCAAkC;AAAE,QAAG,CAAC,QAAQ,MAAM,EAAE,IAAI,QAAO,KAAK,KAAK,MAAM,MAAM,0BAA0B;AAAE,UAAM,IAAE,KAAK,MAAM,UAAU,IAAE;AAAE,MAAE,WAAS,OAAG,KAAK,SAAS,WAAS;AAAE,UAAM,IAAE,KAAK,MAAM,QAAQ,YAAY;AAAE,QAAI,IAAE,MAAK,IAAE;AAAK,YAAO,GAAE;AAAA,MAAC,KAAI;AAAM,YAAG,IAAE,KAAK,MAAM,cAAc,QAAQ,GAAE,GAAG,QAAQ,CAAC,GAAE;AAAC,cAAG,IAAE,SAAS,EAAE,aAAa,KAAK,CAAC,GAAE,KAAK,WAAS,iBAAiB,EAAE,SAAS,CAAC,GAAE,KAAK,SAAS,YAAU,KAAK,OAAM,KAAK,QAAM,GAAE,KAAK,SAAS,UAAU,YAAU,IAAG,EAAE,OAAO,QAAO;AAAC,kBAAMA,KAAE,CAAC,KAAI,MAAM;AAAE,YAAAA,GAAE,SAAS,EAAE,aAAa,IAAI,UAAU,CAAC,MAAI,KAAK,OAAO,WAAS,OAAIA,GAAE,SAAS,EAAE,aAAa,IAAI,MAAM,CAAC,MAAI,KAAK,OAAO,KAAK,SAAO,OAAI,KAAK,aAAW,KAAK,OAAO,cAAYA,GAAE,SAAS,EAAE,aAAa,IAAI,aAAa,CAAC,GAAE,KAAK,OAAO,QAAQ,KAAG,EAAE,aAAa,IAAI,IAAI,KAAG,KAAK,OAAO,cAAY;AAAA,UAAE;AAAA,QAAC,MAAM,MAAK,WAAS,KAAK,MAAM,aAAa,KAAK,OAAO,WAAW,MAAM,QAAQ,GAAE,KAAK,MAAM,gBAAgB,KAAK,OAAO,WAAW,MAAM,QAAQ;AAAE,YAAG,GAAG,MAAM,KAAK,QAAQ,KAAG,CAAC,OAAO,OAAO,SAAS,EAAE,SAAS,KAAK,QAAQ,EAAE,QAAO,KAAK,KAAK,MAAM,MAAM,gCAAgC;AAAE,aAAK,OAAK,MAAM;AAAM;AAAA,MAAM,KAAI;AAAA,MAAQ,KAAI;AAAQ,aAAK,OAAK,GAAE,KAAK,WAAS,UAAU,OAAM,KAAK,MAAM,aAAa,aAAa,MAAI,KAAK,OAAO,cAAY,OAAI,KAAK,MAAM,aAAa,UAAU,MAAI,KAAK,OAAO,WAAS,QAAK,KAAK,MAAM,aAAa,aAAa,KAAG,KAAK,MAAM,aAAa,oBAAoB,OAAK,KAAK,OAAO,cAAY,OAAI,KAAK,MAAM,aAAa,OAAO,MAAI,KAAK,OAAO,QAAM,OAAI,KAAK,MAAM,aAAa,MAAM,MAAI,KAAK,OAAO,KAAK,SAAO;AAAI;AAAA,MAAM;AAAQ,eAAO,KAAK,KAAK,MAAM,MAAM,gCAAgC;AAAA,IAAC;AAAC,SAAK,YAAU,QAAQ,MAAM,KAAK,MAAK,KAAK,QAAQ,GAAE,KAAK,UAAU,OAAK,KAAK,iBAAe,CAAC,GAAE,KAAK,YAAU,IAAI,UAAU,IAAI,GAAE,KAAK,UAAQ,IAAI,QAAQ,IAAI,GAAE,KAAK,MAAM,OAAK,MAAK,GAAG,QAAQ,KAAK,SAAS,SAAS,MAAI,KAAK,SAAS,YAAU,cAAc,KAAK,GAAE,KAAK,KAAK,OAAM,KAAK,SAAS,SAAS,IAAG,GAAG,cAAc,KAAK,IAAI,GAAE,GAAG,aAAa,KAAK,IAAI,GAAE,MAAM,MAAM,KAAK,IAAI,GAAE,KAAK,OAAO,SAAO,GAAG,KAAK,MAAK,KAAK,SAAS,WAAU,KAAK,OAAO,OAAO,KAAK,GAAG,GAAG,CAAAA,OAAG;AAAC,WAAK,MAAM,IAAI,UAAUA,GAAE,IAAI,EAAE;AAAA,IAAC,CAAE,GAAE,KAAK,aAAW,IAAI,WAAW,IAAI,IAAG,KAAK,WAAS,KAAK,WAAS,CAAC,KAAK,UAAU,OAAK,GAAG,MAAM,KAAK,IAAI,GAAE,KAAK,UAAU,UAAU,GAAE,KAAK,UAAU,OAAO,GAAE,KAAK,OAAO,IAAI,YAAU,KAAK,MAAI,IAAI,IAAI,IAAI,IAAG,KAAK,WAAS,KAAK,OAAO,YAAU,KAAK,KAAK,WAAW,MAAI,eAAe,KAAK,KAAK,CAAC,CAAE,GAAE,KAAK,eAAa,GAAE,KAAK,OAAO,kBAAkB,YAAU,KAAK,oBAAkB,IAAI,kBAAkB,IAAI,MAAI,KAAK,MAAM,MAAM,0BAA0B;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,aAAW,UAAU;AAAA,EAAK;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,aAAW,KAAK;AAAA,EAAO;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,aAAW,UAAU;AAAA,EAAO;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,aAAW,UAAU;AAAA,EAAK;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,SAAO,MAAM;AAAA,EAAK;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,SAAO,MAAM;AAAA,EAAK;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,QAAQ,KAAK,SAAO,CAAC,KAAK,UAAQ,CAAC,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,QAAQ,KAAK,MAAM,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,QAAQ,KAAK,UAAQ,MAAI,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,QAAQ,KAAK,MAAM,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,YAAY,GAAE;AAAC,QAAG,CAAC,KAAK,SAAS;AAAO,UAAM,IAAE,GAAG,OAAO,CAAC,KAAG,IAAE;AAAE,SAAK,MAAM,cAAY,IAAE,KAAK,IAAI,GAAE,KAAK,QAAQ,IAAE,GAAE,KAAK,MAAM,IAAI,cAAc,KAAK,WAAW,UAAU;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,OAAO,KAAK,MAAM,WAAW;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,UAAK,EAAC,UAAS,EAAC,IAAE,KAAK;AAAM,WAAO,GAAG,OAAO,CAAC,IAAE,IAAE,KAAG,EAAE,UAAQ,KAAK,WAAS,IAAE,EAAE,IAAI,CAAC,IAAE,KAAK,WAAS;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,QAAQ,KAAK,MAAM,OAAO;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,UAAM,IAAE,WAAW,KAAK,OAAO,QAAQ,GAAE,KAAG,KAAK,SAAO,CAAC,GAAG,UAAS,IAAE,GAAG,OAAO,CAAC,KAAG,MAAI,IAAE,IAAE,IAAE;AAAE,WAAO,KAAG;AAAA,EAAC;AAAA,EAAC,IAAI,OAAO,GAAE;AAAC,QAAI,IAAE;AAAE,OAAG,OAAO,CAAC,MAAI,IAAE,OAAO,CAAC,IAAG,GAAG,OAAO,CAAC,MAAI,IAAE,KAAK,QAAQ,IAAI,QAAQ,IAAG,GAAG,OAAO,CAAC,MAAI,EAAC,QAAO,EAAC,IAAE,KAAK,SAAQ,IAAE,MAAI,IAAE,IAAG,IAAE,MAAI,IAAE,IAAG,KAAK,OAAO,SAAO,GAAE,KAAK,MAAM,SAAO,GAAE,CAAC,GAAG,MAAM,CAAC,KAAG,KAAK,SAAO,IAAE,MAAI,KAAK,QAAM;AAAA,EAAG;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,OAAO,KAAK,MAAM,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,MAAM,GAAE;AAAC,QAAI,IAAE;AAAE,OAAG,QAAQ,CAAC,MAAI,IAAE,KAAK,QAAQ,IAAI,OAAO,IAAG,GAAG,QAAQ,CAAC,MAAI,IAAE,KAAK,OAAO,QAAO,KAAK,OAAO,QAAM,GAAE,KAAK,MAAM,QAAM;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,QAAQ,KAAK,MAAM,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAM,CAAC,KAAK,YAAU,CAAC,CAAC,KAAK,YAAU,QAAQ,KAAK,MAAM,WAAW,KAAG,QAAQ,KAAK,MAAM,2BAA2B,KAAG,QAAQ,KAAK,MAAM,eAAa,KAAK,MAAM,YAAY,MAAM;AAAA,EAAG;AAAA,EAAC,IAAI,MAAM,GAAE;AAAC,QAAI,IAAE;AAAK,OAAG,OAAO,CAAC,MAAI,IAAE,IAAG,GAAG,OAAO,CAAC,MAAI,IAAE,KAAK,QAAQ,IAAI,OAAO,IAAG,GAAG,OAAO,CAAC,MAAI,IAAE,KAAK,OAAO,MAAM;AAAU,UAAK,EAAC,cAAa,GAAE,cAAa,EAAC,IAAE;AAAK,QAAE,MAAM,GAAE,GAAE,CAAC,GAAE,KAAK,OAAO,MAAM,WAAS,GAAE,WAAY,MAAI;AAAC,WAAK,UAAQ,KAAK,MAAM,eAAa;AAAA,IAAE,GAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,OAAO,KAAK,MAAM,YAAY;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,YAAU,KAAK,IAAI,GAAG,KAAK,QAAQ,KAAK,IAAE,KAAK,UAAQ,MAAG;AAAA,EAAK;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,YAAU,KAAK,IAAI,GAAG,KAAK,QAAQ,KAAK,IAAE,KAAK,UAAQ,IAAE;AAAA,EAAE;AAAA,EAAC,IAAI,QAAQ,GAAE;AAAC,UAAM,IAAE,KAAK,OAAO,SAAQ,IAAE,KAAK,QAAQ;AAAQ,QAAG,CAAC,EAAE,OAAO;AAAO,QAAI,IAAE,CAAC,CAAC,GAAG,MAAM,CAAC,KAAG,OAAO,CAAC,GAAE,KAAK,QAAQ,IAAI,SAAS,GAAE,EAAE,UAAS,EAAE,OAAO,EAAE,KAAK,GAAG,MAAM,GAAE,IAAE;AAAG,QAAG,CAAC,EAAE,SAAS,CAAC,GAAE;AAAC,YAAMA,KAAE,QAAQ,GAAE,CAAC;AAAE,WAAK,MAAM,KAAK,+BAA+B,CAAC,WAAWA,EAAC,UAAU,GAAE,IAAEA,IAAE,IAAE;AAAA,IAAE;AAAC,MAAE,WAAS,GAAE,KAAK,MAAM,UAAQ,GAAE,KAAG,KAAK,QAAQ,IAAI,EAAC,SAAQ,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,MAAM;AAAA,EAAO;AAAA,EAAC,IAAI,KAAK,GAAE;AAAC,UAAM,IAAE,GAAG,QAAQ,CAAC,IAAE,IAAE,KAAK,OAAO,KAAK;AAAO,SAAK,OAAO,KAAK,SAAO,GAAE,KAAK,MAAM,OAAK;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,QAAQ,KAAK,MAAM,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,OAAO,GAAE;AAAC,WAAO,OAAO,KAAK,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,MAAM;AAAA,EAAU;AAAA,EAAC,IAAI,WAAU;AAAC,UAAK,EAAC,UAAS,EAAC,IAAE,KAAK,OAAO;AAAK,WAAO,GAAG,IAAI,CAAC,IAAE,IAAE,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,SAAS,GAAE;AAAC,OAAG,IAAI,CAAC,MAAI,KAAK,OAAO,KAAK,WAAS,GAAE,SAAS,eAAe,KAAK,IAAI;AAAA,EAAE;AAAA,EAAC,IAAI,OAAO,GAAE;AAAC,SAAK,UAAQ,GAAG,UAAU,KAAK,MAAK,GAAE,KAAE,EAAE,MAAO,MAAI;AAAA,IAAC,CAAE,IAAE,KAAK,MAAM,KAAK,kCAAkC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,UAAQ,KAAK,MAAM,aAAa,QAAQ,KAAG,KAAK,MAAM,aAAa,aAAa,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,QAAO;AAAC,QAAG,CAAC,KAAK,QAAQ,QAAO;AAAK,UAAM,IAAE,kBAAkB,eAAe,KAAK,IAAI,CAAC;AAAE,WAAO,GAAG,MAAM,CAAC,IAAE,EAAE,KAAK,GAAG,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,MAAM,GAAE;AAAC,SAAK,UAAQ,GAAG,OAAO,CAAC,KAAG,oBAAoB,CAAC,KAAG,KAAK,OAAO,QAAM,kBAAkB,CAAC,GAAE,eAAe,KAAK,IAAI,KAAG,KAAK,MAAM,MAAM,mCAAmC,CAAC,GAAG,IAAE,KAAK,MAAM,KAAK,wCAAwC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAS,GAAE;AAAC,SAAK,OAAO,WAAS,GAAG,QAAQ,CAAC,IAAE,IAAE,KAAK,OAAO;AAAA,EAAQ;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,QAAQ,KAAK,OAAO,QAAQ;AAAA,EAAC;AAAA,EAAC,eAAe,GAAE;AAAC,aAAS,OAAO,KAAK,MAAK,GAAE,KAAE;AAAA,EAAC;AAAA,EAAC,IAAI,aAAa,GAAE;AAAC,aAAS,IAAI,KAAK,MAAK,GAAE,KAAE,GAAE,SAAS,MAAM,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,UAAK,EAAC,SAAQ,GAAE,cAAa,EAAC,IAAE,KAAK;AAAS,WAAO,IAAE,IAAE;AAAA,EAAE;AAAA,EAAC,IAAI,SAAS,GAAE;AAAC,aAAS,YAAY,KAAK,MAAK,GAAE,KAAE;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,YAAO,SAAS,gBAAgB,KAAK,IAAI,KAAG,CAAC,GAAG;AAAA,EAAQ;AAAA,EAAC,IAAI,IAAI,GAAE;AAAC,QAAG,CAAC,QAAQ,IAAI;AAAO,UAAM,IAAE,GAAG,QAAQ,CAAC,IAAE,IAAE,CAAC,KAAK;AAAI,OAAG,SAAS,KAAK,MAAM,yBAAyB,KAAG,KAAK,MAAM,0BAA0B,IAAE,IAAI,SAAO,IAAI,QAAQ,GAAE,GAAG,SAAS,KAAK,MAAM,uBAAuB,MAAI,CAAC,KAAK,OAAK,IAAE,KAAK,MAAM,wBAAwB,IAAE,KAAK,OAAK,CAAC,KAAG,SAAS,qBAAqB;AAAA,EAAE;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,QAAQ,MAAI,GAAG,MAAM,KAAK,MAAM,sBAAsB,IAAE,KAAK,UAAQ,SAAS,0BAAwB,KAAK,MAAM,2BAAyB,IAAI,SAAO;AAAA,EAAI;AAAA,EAAC,qBAAqB,GAAE;AAAC,SAAK,qBAAmB,KAAK,kBAAkB,WAAS,KAAK,kBAAkB,QAAQ,GAAE,KAAK,oBAAkB,OAAM,OAAO,OAAO,KAAK,OAAO,mBAAkB,CAAC,GAAE,KAAK,OAAO,kBAAkB,YAAU,KAAK,oBAAkB,IAAI,kBAAkB,IAAI;AAAA,EAAE;AAAA,EAAC,OAAO,UAAU,GAAE,GAAE;AAAC,WAAO,QAAQ,MAAM,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,WAAW,GAAE,GAAE;AAAC,WAAO,WAAW,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,MAAM,GAAE,IAAE,CAAC,GAAE;AAAC,QAAI,IAAE;AAAK,WAAO,GAAG,OAAO,CAAC,IAAE,IAAE,MAAM,KAAK,SAAS,iBAAiB,CAAC,CAAC,IAAE,GAAG,SAAS,CAAC,IAAE,IAAE,MAAM,KAAK,CAAC,IAAE,GAAG,MAAM,CAAC,MAAI,IAAE,EAAE,OAAO,GAAG,OAAO,IAAG,GAAG,MAAM,CAAC,IAAE,OAAK,EAAE,IAAK,CAAAA,OAAG,IAAI,MAAKA,IAAE,CAAC,CAAE;AAAA,EAAC;AAAC;AAAC,KAAK,WAAS,UAAU,QAAQ;;;ADGtquH,wBAAsB;;;AEHtB,mBAA0E;AAE1E,SAAS,SAAS,KAAK,eAAe,OAAO,CAAC,GAAG;AAC/C,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAS,IAAI;AAC7C,QAAM,aAAS,qBAAO,IAAI;AAC1B,QAAM,EAAE,aAAAK,cAAa,SAAAC,UAAS,QAAAC,SAAQ,OAAO,IAAI;AACjD,8BAAU,MAAM;AACd,UAAM,oBAAoBF,aAAY,OAAO,SAAS,MAAM;AAC5D,gBAAY,iBAAiB;AAC7B,WAAO,MAAM;AACX,UAAIC;AACF,QAAAA,SAAQ,mBAAmB,MAAM;AAAA,IACrC;AAAA,EACF,GAAG,IAAI;AACP,QAAM,UAAM,sBAAQ,MAAMC,QAAO,UAAU,MAAM,GAAG,CAAC,QAAQ,CAAC;AAC9D,wCAAoB,KAAK,KAAK,CAAC,GAAG,CAAC;AACnC,SAAO;AACT;;;AFXA,IAAM,cAAc,CAAC,GAAG,WAAW;AACjC,QAAM,OAAO,IAAI,KAAO,eAAe,OAAO,WAAW,CAAC,CAAC;AAC3D,MAAI,OAAO;AACT,SAAK,SAAS,OAAO;AACvB,SAAO;AACT;AACA,IAAM,UAAU,CAAC,SAAS;AACxB,MAAI;AACF,SAAK,QAAQ;AACjB;AACA,IAAMC,QAAO,MAAM;AACnB;AACA,IAAM,SAAS,CAAC,SAAS;AACvB,MAAI,CAAC,MAAM;AACT,WAAO,MAAM,IAAI,MAAM,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAAG;AAAA,MACjD,KAAK,CAAC,QAAQ,SAAS;AACrB,YAAI,SAAS,QAAQ;AACnB,iBAAO,OAAO,IAAI;AAAA,QACpB;AACA,eAAOA;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,OAAO;AAAA;AAAA;AAAA;AAAA,IAIZ;AAAA,EACF;AACF;AACA,SAAS,QAAQ,KAAK,QAAQ,OAAO,MAAM;AACzC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,OAAO,SAAS,OAAO,MAAM;AAAA,EACxC;AACF;AACA,IAAMC,QAAa,iBAAW,CAAC,OAAO,QAAQ;AAC5C,QAAM,EAAE,QAAAC,SAAQ,UAAU,MAAM,GAAG,KAAK,IAAI;AAC5C,QAAM,YAAY,QAAQ,KAAK;AAAA,IAC7B,QAAAA;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,SAAS,EAAE,KAAK,WAAW,WAAW,mBAAmB,GAAG,KAAK,CAAC;AAC/F,CAAC;AACD,KAAK,YAAY,OAAO,YAAY,IAAI,UAAU,cAAc;AAC9D,EAAAD,MAAK,cAAc;AACnB,EAAAA,MAAK,eAAe;AAAA,IAClB,SAAS;AAAA,MACP,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA,QACb,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,QACP;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,QACA;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,EAAAA,MAAK,YAAY;AAAA,IACf,SAAS,kBAAAE,QAAU;AAAA,IACnB,QAAQ,kBAAAA,QAAU;AAAA,EACpB;AACF;", "names": ["t", "e", "i", "s", "r", "a", "n", "o", "c", "u", "e", "t", "n", "r", "a", "o", "l", "c", "i", "s", "e", "t", "i", "s", "n", "instantiate", "destroy", "getAPI", "noop", "<PERSON><PERSON><PERSON>", "source", "PropTypes"]}